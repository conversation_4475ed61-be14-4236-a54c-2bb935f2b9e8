<template>
    <div class="teach-plan-container">
        <header class="headers relative pb-[10vh]">
            <div class="left">
                <div class="gohome" @click="jumpPage('toolboxPage')">
                    <img src="@/assets/workShopPage/leftarrow.png">
                </div>教案内容生成助手
            </div>
        </header>
        <!-- 顶部图标 -->
        <div class="icon-container">
            <img :src="logo" alt="教案生成助手" class="icon-img" />
        </div>

        <!-- 标题和描述 -->
        <div class="header-section">
            <h1 class="main-title">你好~我是教案内容生成助手</h1>
            <p class="subtitle">根据讲授课程、主题、核心知识等条件，可以帮您进行对应教案内容的生成~</p>
        </div>

        <!-- 表单区域 -->
        <div class="form-container">
            <NForm ref="formRef" :model="formData" :rules="formRules" size="large" label-placement="top"
                require-mark-placement="left">
                <!-- 讲授课程 -->
                <NFormItem label="讲授课程" path="course">
                    <NSelect v-model:value="formData.course" :options="courseOptions" :loading="courseLoading"
                        placeholder="请选择讲授课程" clearable @update:value="handleCourseChange" />
                </NFormItem>

                <!-- 讲授主题 -->
                <NFormItem label="讲授主题" path="topic">
                    <NSelect v-model:value="formData.topic" :options="topicOptions" :loading="topicLoading"
                        :disabled="!formData.course || topicLoading" placeholder="请选择讲授主题" clearable
                        @update:value="handleTopicChange" />
                </NFormItem>

                <!-- 核心知识 -->
                <NFormItem label="核心知识" path="coreKnowledge">
                    <NSelect v-model:value="formData.coreKnowledge" :options="coreKnowledgeOptions"
                        :loading="coreKnowledgeLoading" :disabled="!formData.topic || coreKnowledgeLoading" multiple
                        placeholder="请选择核心知识" clearable @update:value="handleCoreKnowledgeChange" />
                </NFormItem>

                <!-- 认知策略 -->
                <NFormItem label="认知策略" path="cognitiveStrategy">
                    <NSelect v-model:value="formData.cognitiveStrategy" :options="cognitiveStrategyOptions" multiple
                        placeholder="请选择认知策略" />
                </NFormItem>

                <!-- 教学目标 -->
                <NFormItem label="教学目标" path="teachingObjectives">
                    <NInput v-model:value="formData.teachingObjectives" placeholder="请描述教学目标" />
                </NFormItem>

                <!-- 提交按钮 -->
                <div class="submit-container">
                    <NButton type="primary" size="large" @click="handleSubmit" class="submit-button">
                        智能生成教案
                    </NButton>
                </div>
            </NForm>
        </div>

        <!-- 执行进度弹框 -->
        <AgentExecutionPreviewModal v-model:show="showProgressModal" title="正在生成教案内容" :nodes="executionNodes"
            :allow-close="isExecutionComplete" @close="handleProgressClose" @downloadResult="handleDownload" />
    </div>
</template>

<script setup lang="ts">
    import { ref, computed, onMounted } from 'vue'
    import { NForm, NFormItem, NInput, NSelect, NButton, useMessage } from 'naive-ui'
    import AgentExecutionPreviewModal from '@/components/common/AgentExecutionPreviewModal/index.vue'
    import { getTeachingPlanListApi, saveTeachingPlanApi, exportTeachingPlanWordApi } from '@/api/teachPlan'
    import logo from '@/assets/teachPlanGen/logo.png'
    // 导入图片资源
    import icon1 from '@/assets/teachPlanGen/step1.png'
    import icon2 from '@/assets/teachPlanGen/step2.png'
    import icon3 from '@/assets/teachPlanGen/step3.png'
    import { useRouter } from 'vue-router'
    const router = useRouter()
    const message = useMessage()
    const formRef = ref(null)
    const showProgressModal = ref(false)

    // 表单数据
    const formData = ref({
        course: null,
        topic: null,
        coreKnowledge: [],
        cognitiveStrategy: [],
        teachingObjectives: null
    })

    // 表单验证规则
    const formRules = {
        course: [
            { required: true, message: '请选择讲授课程', trigger: 'change' }
        ],
        topic: [
            { required: true, message: '请选择讲授主题', trigger: 'change' }
        ],
        coreKnowledge: [
            {
                required: true,
                type: 'array',
                min: 1,
                message: '请至少选择一个核心知识',
                trigger: 'change'
            }
        ],
        cognitiveStrategy: [
            {
                required: true,
                type: 'array',
                min: 1,
                message: '请至少选择一个认知策略',
                trigger: 'change'
            }
        ],
        teachingObjectives: [
        ]
    }

    // 动态选项数据
    const courseOptions = ref([])
    const topicOptions = ref([])
    const coreKnowledgeOptions = ref([])

    // 加载状态
    const courseLoading = ref(false)
    const topicLoading = ref(false)
    const coreKnowledgeLoading = ref(false)

    // 执行节点数据
    const executionNodes = ref([
        {
            id: 'llm',
            label: '识别必要条件',
            description: '分析课程信息和教学要求',
            details: [],
            status: 'pending',
            icon: icon1
        },
        {
            id: 'code',
            label: '知识点查询',
            description: '使用AI分析知识结构',
            details: [],
            status: 'pending',
            icon: icon2
        },
        {
            id: 'teachingplan',
            label: '教案内容生成',
            description: '完善教案细节和格式',
            details: [],
            status: 'pending',
            icon: icon3
        }
    ])

    // 计算属性
    const isExecutionComplete = computed(() => {
        return executionNodes.value.every(node =>
            node.status === 'completed' || node.status === 'error'
        )
    })

    // 认知策略选项
    const cognitiveStrategyOptions = ref([
        { label: '调查测试', value: 'survey_test' },
        { label: '康奈尔笔记法', value: 'cornell_notes' },
        { label: 'SQ3R 阅读法', value: 'sq3r_reading' },
        { label: '实践参与', value: 'practical_participation' },
        { label: '小组讨论', value: 'group_discussion' },
        { label: '团队学习', value: 'team_learning' },
        { label: '小组合作', value: 'group_cooperation' },
        { label: '汇报演说', value: 'presentation_speech' }
    ])

    // API请求函数
    const loadCourseOptions = async () => {
        try {
            courseLoading.value = true
            const response = await getTeachingPlanListApi({ pid: 0 })
            if (response.data && Array.isArray(response.data)) {
                courseOptions.value = response.data.map(item => ({
                    label: item.name || item.label,
                    value: item.id || item.value
                }))
            }
        } catch (error) {
            console.error('加载课程选项失败:', error)
            message.error('加载课程选项失败，请稍后重试')
        } finally {
            courseLoading.value = false
        }
    }

    const loadTopicOptions = async (courseId) => {
        try {
            topicLoading.value = true
            const response = await getTeachingPlanListApi({ pid: courseId })
            if (response.data && Array.isArray(response.data)) {
                topicOptions.value = response.data.map(item => ({
                    label: item.name || item.label,
                    value: item.id || item.value
                }))
            }
        } catch (error) {
            console.error('加载主题选项失败:', error)
            message.error('加载主题选项失败，请稍后重试')
        } finally {
            topicLoading.value = false
        }
    }

    const loadCoreKnowledgeOptions = async (topicId) => {
        try {
            coreKnowledgeLoading.value = true
            const response = await getTeachingPlanListApi({ pid: topicId })
            if (response.data && Array.isArray(response.data)) {
                coreKnowledgeOptions.value = response.data.map(item => ({
                    label: item.name || item.label,
                    value: item.id || item.value
                }))
            }
        } catch (error) {
            console.error('加载核心知识选项失败:', error)
            message.error('加载核心知识选项失败，请稍后重试')
        } finally {
            coreKnowledgeLoading.value = false
        }
    }

    // 处理课程变化
    const handleCourseChange = (value) => {
        // 清空后续选择
        formData.value.topic = null
        formData.value.coreKnowledge = []
        formData.value.cognitiveStrategy = []
        topicOptions.value = []
        coreKnowledgeOptions.value = []

        // 加载主题选项
        if (value) {
            loadTopicOptions(value)
        }
    }

    // 处理主题变化
    const handleTopicChange = (value) => {
        // 清空后续选择
        formData.value.coreKnowledge = []
        formData.value.cognitiveStrategy = []
        coreKnowledgeOptions.value = []

        // 加载核心知识选项
        if (value) {
            loadCoreKnowledgeOptions(value)
        }
    }

    // 处理核心知识变化
    const handleCoreKnowledgeChange = (value) => {
        if (!value || (Array.isArray(value) && value.length === 0)) {
            formData.value.cognitiveStrategy = []
        } else {
            // 如果选择了核心知识且认知策略为空，随机选择一个认知策略
            if (formData.value.cognitiveStrategy.length === 0 && cognitiveStrategyOptions.value.length > 0) {
                const randomIndex = Math.floor(Math.random() * cognitiveStrategyOptions.value.length)
                formData.value.cognitiveStrategy = [cognitiveStrategyOptions.value[randomIndex].value]
            }
        }
    }

    // 重置执行节点状态
    const resetExecutionNodes = () => {
        executionNodes.value.forEach(node => {
            node.status = 'pending'
            node.startTime = undefined
            node.endTime = undefined
            node.details = []
        })
    }

    // 更新节点状态
    const updateNodeStatus = (nodeId, status, details = null) => {
        const node = executionNodes.value.find(n => n.id === nodeId)
        if (node) {
            node.status = status
            if (status === 'running') {
                node.startTime = Date.now()
            } else if (status === 'completed' || status === 'error') {
                node.endTime = Date.now()
            }
            // 如果有details内容，则更新节点的details
            if (details && details.length > 0) {
                node.details = details
            }
        }
    }
    let downId = ''
    // 处理流式响应进度
    const handleProgressResponse = (event) => {
        try {
            const xhr = event.target
            const { responseText } = xhr

            // 按行分割响应文本
            const lines = responseText.split('\n').filter(line => line.trim() !== '')
            // console.log('进度响应行数据:', lines)

            // 处理每一行数据
            for (const line of lines) {
                const trimmedLine = line.replace(/^data: /, '').trim()

                try {
                    // 解析JSON数据
                    const data = JSON.parse(trimmedLine?.substring(5))
                    downId = data.id

                    // 提取节点内容details
                    const content = data.choices?.[0]?.message?.content
                    let details = null
                    if (content) {
                        // 将content按行分割作为details数组
                        details = content.split('/n').filter(line => line.trim() !== '').slice(0, 3)
                    }

                    // 根据nodeName更新对应节点状态
                    if (data.nodeName === '__START__') {
                        // 开始执行，第一个节点开始运行
                        updateNodeStatus('llm', 'running')
                    }
                    else if (data.nodeName === 'llm') {
                        // llm节点完成，第一个节点完成，第二个节点开始运行
                        updateNodeStatus('llm', 'completed', details)
                        updateNodeStatus('code', 'running')
                        console.log('llm节点完成，第一个节点完成，第二个节点开始运行', details)
                    }
                    else if (data.nodeName === 'code') {
                        // code节点完成，第二个节点完成，第三个节点开始运行
                        updateNodeStatus('code', 'completed', details)
                        console.log('code节点完成，第二个节点完成，第三个节点开始运行', details)
                        updateNodeStatus('teachingplan', 'running')
                    }
                    else if (data.nodeName === 'teachingplan') {
                        // teachingplan节点处理中，更新第三个节点的details
                        updateNodeStatus('teachingplan', 'running', ['教案内容生成完成'])
                        console.log('teachingplan节点处理中，更新第三个节点的details', details)
                    }
                    else if (data.nodeName === '__END__') {
                        // 全部完成，最后一个节点完成，不更新details
                        updateNodeStatus('teachingplan', 'completed')
                    }

                    // 检查是否有错误状态
                    if (data.error || data.status === 'error') {
                        // 查找当前运行的节点并标记为错误
                        const runningNode = executionNodes.value.find(n => n.status === 'running')
                        if (runningNode) {
                            updateNodeStatus(runningNode.id, 'error')
                        }
                    }

                } catch (parseError) {
                    console.error('解析进度数据失败:', parseError)
                }
            }
        } catch (error) {
            console.error('处理进度响应失败:', error)
        }
    }

    // 提交表单
    const handleSubmit = async () => {
        try {
            await formRef.value?.validate()

            // 获取选项对应的名称
            const courseName = courseOptions.value.find(item => item.value === formData.value.course)?.label
            const topicName = topicOptions.value.find(item => item.value === formData.value.topic)?.label
            // 处理多选核心知识，转换为逗号隔开的字符串
            const coreKnowledgeNames = formData.value.coreKnowledge.map(value =>
                coreKnowledgeOptions.value.find(item => item.value === value)?.label
            ).filter(Boolean)
            const coreKnowledgeName = coreKnowledgeNames.join(',')
            // 处理多选认知策略，转换为逗号隔开的字符串
            const cognitiveStrategyNames = formData.value.cognitiveStrategy.map(value =>
                cognitiveStrategyOptions.value.find(item => item.value === value)?.label
            ).filter(Boolean)
            const cognitiveStrategyName = cognitiveStrategyNames.join(',')
            const chatController = new AbortController()

            // 重置执行节点状态
            resetExecutionNodes()

            // 构建保存数据
            const saveData = {
                coursesName: courseName || '',
                themeName: topicName || '',
                coreKnowledgeName: coreKnowledgeName || '',
                cognitiveStrategyName: cognitiveStrategyName || '',
                teachingObjectives: formData.value.teachingObjectives || '',
                signal: chatController.signal,
                onDownloadProgress: ({ event }) => {
                    handleProgressResponse(event)
                }
            }

            console.log('开始生成教案，保存数据:', saveData)

            // 显示进度条弹框
            showProgressModal.value = true

            // 调用保存接口
            const result = await saveTeachingPlanApi(saveData)
            console.log('教案保存成功:', result)
            message.success('教案数据保存成功')

        } catch (error) {
            // console.error('保存教案失败:', error)
            // showProgressModal.value = false

            // // 标记所有运行中的节点为错误状态
            // executionNodes.value.forEach(node => {
            //     if (node.status === 'running') {
            //         updateNodeStatus(node.id, 'error')
            //     }
            // })

            // if (error.response?.data?.message) {
            //     message.error(`保存失败: ${error.response.data.message}`)
            // } else {
            //     message.error('保存失败，请稍后重试')
            // }
        }
    }

    // 处理下载
    const handleDownload = async () => {
        const loadingMessage = message.loading('正在准备下载...', { duration: 0 })

        try {
            // 调用下载API
            const response = await exportTeachingPlanWordApi(downId)
            console.log(response);

            // 创建blob URL并下载
            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            })
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url

            // 生成文件名
            const courseName = courseOptions.value.find(item => item.value === formData.value.course)?.label || '教案'
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
            link.download = `${courseName}_${timestamp}.docx`

            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)

            loadingMessage.destroy()
            message.success('教案下载成功')

            // 关闭弹框
            showProgressModal.value = false
            // 重置执行节点状态
            resetExecutionNodes()

        } catch (error) {
            loadingMessage.destroy()
            console.error('下载教案失败:', error)
            message.error('下载失败，请稍后重试')
        }
    }

    // 处理进度条关闭
    const handleProgressClose = () => {
        showProgressModal.value = false
        // 重置执行节点状态
        resetExecutionNodes()
        message.info('教案生成已取消')
    }

    // 跳转页面
    function jumpPage(url) {
        if (url)
            router.push(url)
        else
            router.go(-1)
    }

    // 组件挂载时加载第一级数据
    onMounted(() => {
        loadCourseOptions()
    })
</script>

<style scoped lang="less">
    .teach-plan-container {
        min-height: 100vh;
        background: linear-gradient(180deg, #FFFFFF 0%, #F6F8FE 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 20px 20px 40px 20px;
        overflow-y: auto;
    }

    .chatbg {
        padding-top: 30px;
    }

    .headers {
        height: 40px;
        width: 100%;
        display: flex;
        justify-content: flex-start;
        margin-top: 30px;

        .left {
            color: #323233;
            font-size: 20px;
            font-weight: 500;
            line-height: 0;
            letter-spacing: 0;
            line-height: 40px;
            display: flex;
            align-items: center;
            margin-left: 14px;

            .gohome {
                width: 40px;
                height: 40px;
                background: #FAFBFF;
                border: 1px solid #E9ECF3;
                border-radius: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 11px;

                img {
                    width: 20px;
                    height: 20px;
                }
            }

            span {
                color: #909399;
                margin-left: 8px;

            }
        }

    }

    .icon-container {
        margin-bottom: 20px;
    }

    .icon-img {
        width: 74px;
        height: 74px;
    }

    .header-section {
        text-align: center;
        margin-bottom: 25px;
        max-width: 600px;
    }

    .main-title {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 18px;
        color: #323233;
        letter-spacing: 0;
        text-align: center;
        margin-bottom: 10px;
    }

    .subtitle {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #606266;
        letter-spacing: 0;
        text-align: center;
    }

    .form-container {
        width: 712px;
        /* height: 613px; */
        background: white;
        border-radius: 12px;
        padding: 35px 55px;
        /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08); */
        /* border: 1px solid #f0f0f0; */
        flex-shrink: 0;
    }

    :deep(.n-form-item) {
        margin-bottom: 16px;
    }

    :deep(.n-form-item-label) {
        font-weight: 600;
        color: #333;
        font-size: 15px;
        padding-bottom: 6px;
    }

    :deep(.n-input) {
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        transition: all 0.3s ease;
    }

    :deep(.n-input:hover) {
        border-color: #1263FF;
    }

    :deep(.n-input.n-input--focus) {
        border-color: #1263FF;
        box-shadow: 0 0 0 2px rgba(18, 99, 255, 0.1);
    }

    :deep(.n-select .n-base-selection) {
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        transition: all 0.3s ease;
    }

    :deep(.n-select .n-base-selection:hover) {
        border-color: #1263FF;
    }

    :deep(.n-select.n-select--focused .n-base-selection) {
        border-color: #1263FF;
        box-shadow: 0 0 0 2px rgba(18, 99, 255, 0.1);
    }

    .submit-container {
        text-align: center;
        margin-top: 20px;
    }

    .submit-button {
        width: 600px;
        height: 40px;
        background: #125EFF;
        background-image: linear-gradient(180deg, #449DFF 0%, #125EFF 100%);
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .submit-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(18, 99, 255, 0.4);
    }

    .submit-button:active {
        transform: translateY(0);
    }

    :deep(.n-button.n-button--primary-type.n-button--loading) {
        background: linear-gradient(135deg, #1263FF 0%, #764ba2 100%);
    }

    :deep(.n-form-item-feedback-wrapper) {
        display: none;
    }
</style>