<template>
  <!-- 右侧滑出配置面板 -->
  <div
    class="config-panel-overlay"
    v-show="visible"
    @click="handleOverlayClick"
  >
    <div class="config-panel" :class="{ 'panel-open': visible }" @click.stop>
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-content">
          <div class="node-info">
            <div class="node-name flex">
              <img class="w-[18px] h-[18px]" :src="formData.icon" />
              {{ formData.label }}
            </div>
          </div>
          <div class="header-actions">
            <!-- 运行节点按钮 -->
            <button
              v-if="showRunButton"
              class="run-btn"
              :class="{ running: isRunning }"
              :disabled="isRunning"
              @click="handleRunNode"
              :title="isRunning ? '运行中...' : '运行节点'"
            >
              <svg
                v-if="!isRunning"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polygon points="5,3 19,12 5,21"></polygon>
              </svg>
              <svg
                v-else
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                class="animate-spin"
              >
                <path d="M21 12a9 9 0 11-6.219-8.56"></path>
              </svg>
            </button>
            <!-- 更多操作下拉菜单 -->
            <n-dropdown
              :options="moreOptions"
              @select="handleMoreAction"
              trigger="click"
              placement="bottom-end"
            >
              <button class="more-btn" title="更多操作">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <circle cx="5" cy="12" r="1"></circle>
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="19" cy="12" r="1"></circle>
                </svg>
              </button>
            </n-dropdown>
            <!-- <button class="close-btn" @click="handleClose">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button> -->
          </div>
        </div>
      </div>
      <div class="divider"></div>
      <!-- 面板内容 -->
      <div class="panel-content" v-if="node">
        <n-scrollbar style="height: calc(100vh - 140px)">
          <n-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-placement="top"
            :show-feedback="true"
            :show-label="true"
          >
            <!-- 开始节点配置 -->
            <template v-if="node.type === 'start'">
              <n-form-item
                path="config.duolun"
                label-placement="left"
                class="setHeight mb-[9px]"
              >
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 多轮对话
                    <NPopover trigger="click">
                      <template #trigger>
                        <img src="@/assets/agentOrchestration/promptIcon.png" />
                      </template>
                      <span>多轮对话的描述</span>
                    </NPopover>
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <n-switch v-model:value="formData.config.duolun" />
                </div>
              </n-form-item>

              <n-form-item
                class="h-[38px] mb-[12px]"
                label=""
                path="config.lunshu"
                v-if="formData.config.duolun"
                label-placement="left"
              >
                <template #label>
                  <div class="histit">携带历史对话轮数</div>
                </template>
                <div class="flex justify-end w-full h-[38px]">
                  <div class="w-[114px]">
                    <n-input-number
                      min="0"
                      v-model:value="formData.config.lunshu"
                      placeholder="轮数"
                    />
                  </div>
                </div>
              </n-form-item>

              <n-form-item
                label-placement="left"
                class="setHeight mt-[12px] mb-[6px]"
              >
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span>输入</div>
                </template>
              </n-form-item>

              <NDataTable
                :bordered="false"
                single-column
                :columns="startcolumns"
                :data="startdata"
              />
              <div class="divider mt-[16px] mb-[16px]"></div>
              <NDataTable
                :bordered="false"
                single-column
                :columns="environmentVariableColumns"
                :data="environmentVariableData"
              />
              <n-form-item
                path="config.duolun"
                label-placement="left"
                class="setHeight mt-[17px] mb-[6px]"
              >
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 文件上传
                    <NPopover trigger="click">
                      <template #trigger>
                        <img src="@/assets/agentOrchestration/promptIcon.png" />
                      </template>
                      <span>文件上传的描述</span>
                    </NPopover>
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <n-switch v-model:value="formData.config.shangchuan" />
                </div>
              </n-form-item>

              <NDataTable
                v-if="formData.config.shangchuan"
                :bordered="false"
                single-column
                :columns="fileColumns"
                :data="fileData"
              />
            </template>

            <!-- 文本生成配置 -->
            <template v-if="node.type === 'llm'">
              <n-form-item path="config.model" class="setrowbottom">
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 模型选择
                  </div>
                </template>
               <n-select
                  v-model:value="formData.config.modelConfig.model"
                  :options="modelOptions"
                  placeholder="请选择模型"
                  filterable
                  @update:value="handleModelChange"
                />
                <img
                  @click="changemodelParameterShow(true)"
                  class="w-[20px] h-[20px] ml-[10px] cursor-pointer"
                  src="@/assets/agentOrchestration/yitupeizhi.png"
                />
              </n-form-item>
              <n-form-item
                label-placement="left"
                class="setHeight mb-[9px] mt-[21px]"
              >
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 提示词
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <div class="text-[#125EFF] cursor-pointer"  @click="optimizing">一键优化</div>
                </div>
              </n-form-item>
              <n-form-item path="config.tishici" class="seteditable">
                <div class="relative w-full">
                  <div
                    ref="editableDiv"
                    contenteditable="true"
                    class="editable-div"
                    @keydown="handleKeydown"
                    @input="handleInput"
                  ></div>
                  <button
                    class="add-variable-btn"
                    @click="showVariableSelector = true"
                    type="button"
                  >
                    + 添加变量
                  </button>
                </div>
              </n-form-item>

              <n-form-item
                label-placement="left"
                class="setHeight mt-[24px] mb-[10px]"
              >
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span>输出</div>
                </template>
              </n-form-item>

              <n-form-item label-placement="left">
                <div class="w-full flex text-[#C7C7C7]">
                  <div class="w-[50%]">名称</div>
                  <div>类型</div>
                </div>
              </n-form-item>

              <n-form-item
                path="config.wenbenshuchu"
                label-placement="left"
                class="outputrow"
              >
                <div class="w-full flex text-[#565756] items-center">
                  <div class="w-[50%]">
                    <div class="w-[234px] h-[38px]">
                      <n-input
                        v-model:value="formData.config.wenbenshuchu"
                        type="text"
                        placeholder="请输入输出名称"
                      >
                      </n-input>
                    </div>
                  </div>
                  <div>文本</div>
                </div>
              </n-form-item>
            </template>

            <!-- 结束节点配置 -->

            <template v-if="node.type === 'end'">
              <n-form-item label-placement="left" class="setHeight mb-[9px]">
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 回复内容
                  </div>
                </template>
              </n-form-item>
              <n-form-item path="config.huifuneirong" class="seteditable">
                <div class="relative w-full">
                  <div
                    ref="editableDiv"
                    contenteditable="true"
                    class="editable-div"
                    @keydown="handleKeydown"
                    @input="handleInput"
                  ></div>
                  <button
                    class="add-variable-btn"
                    @click="showVariableSelector = true"
                    type="button"
                  >
                    + 添加变量
                  </button>
                </div>
              </n-form-item>

              <n-form-item
                path="config.wentijianyi"
                label-placement="left"
                class="setHeight mt-[24px] mb-[17px]"
              >
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 问题建议
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <n-switch
                    v-model:value="formData.config.wentijianyi"
                    @update:value="handleChangewentijianyi"
                  />
                </div>
              </n-form-item>
              <div v-if="formData.config.wentijianyi">
                <n-form-item
                  path="config.yindaoci"
                  class="setrowbottom mb-[16px]"
                >
                  <template #label>
                    <div class="rowstit">引导词</div>
                  </template>
                  <n-input
                    v-model:value="formData.config.yindaoci"
                    type="text"
                    placeholder="请输入引导词"
                    maxlength="20"
                    show-count
                    default-value="试试这样问"
                  >
                  </n-input>
                </n-form-item>

                <n-form-item path="config.jianyitype" class="setrowbottom">
                  <template #label>
                    <div class="rowstit">问题建议</div>
                  </template>
                  <NRadioGroup
                    v-model:value="formData.config.jianyitype"
                    name="radiogroup"
                    default-checked="0"
                  >
                    <div class="flex items-center">
                      <div
                        @click="changejianyitypefun('0')"
                        class="w-[236px] h-[38px] bg-[#F5F5F6] flex items-center pl-[12px] rounded-lg mr-[16px]"
                      >
                        <NRadio key="0" value="0"> 默认 </NRadio>
                      </div>
                      <div
                        @click="changejianyitypefun('1')"
                        class="w-[236px] h-[38px] bg-[#F5F5F6] flex items-center pl-[12px] rounded-lg"
                      >
                        <NRadio key="1" value="1"> 自定义 </NRadio>
                      </div>
                    </div>
                  </NRadioGroup>
                </n-form-item>
                <div v-if="formData.config.jianyitype == '1'">
                  <div class="mb-[8px]">
                    <n-form-item
                      v-for="(item, index) in formData.config.jianyiwenti"
                      label-placement="left"
                      class="mt-[12px]"
                    >
                      <div class="flex items-center h-[38px]">
                        <div class="w-[114px] mr-[14px] h-full">
                          <NSelect
                            v-model:value="item.type"
                            :options="variableOptions"
                            filterable
                          />
                        </div>
                        <div class="w-[336px] mr-[6px] h-full">
                          <AggregationSelector
                            v-if="item.type == '0'"
                            v-model="item.value"
                            :options="aggregationOptions"
                            placeholder="请选择变量"
                            @change="handleAggregationChange"
                          />

                          <NInput
                            type="text"
                            placeholder="请输入"
                            maxlength="20"
                            show-count
                            v-if="item.type == '1'"
                            v-model:value="item.value"
                          ></NInput>
                        </div>
                        <img
                          @click="delproblemfun(index)"
                          class="w-[16px] h-[16px] cursor-pointer"
                          src="@/assets/agentOrchestration/delIcon2.png"
                        />
                      </div>
                    </n-form-item>
                  </div>

                  <div class="w-[488px] btnparent mt-[12px]">
                    <NButton @click="addproblemfun" dashed>
                      <img
                        class="w-[9px] mr-[6px]"
                        src="@/assets/agentOrchestration/addIcon.png"
                      />
                      添加问题建议
                    </NButton>
                  </div>
                </div>
              </div>
            </template>

            <!-- 意图识别配置 -->
            <template v-if="node.type === 'question-classifier'">
              <n-form-item path="config.modelConfig.model" class="setrowbottom">
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 模型选择
                  </div>
                </template>
                <n-select
                  v-model:value="formData.config.modelConfig.model"
                  :options="modelOptions"
                  placeholder="请选择模型"
                  filterable
                  @update:value="handleModelChange"
                />
                <img
                  @click="changemodelParameterShow(true)"
                  class="w-[20px] h-[20px] ml-[10px] cursor-pointer"
                  src="@/assets/agentOrchestration/yitupeizhi.png"
                />
              </n-form-item>
              <n-form-item
                label-placement="left"
                class="setHeight mt-[21px] mb-[13px]"
              >
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span> 输入</div>
                </template>
              </n-form-item>
              <n-form-item label-placement="left" class="setHeight mb-[6px]">
                <template #label>
                  <div class="text-[#565756]">用户问题</div>
                </template>
                <div class="flex justify-end w-full text-[#C7C7C7]">文本</div>
              </n-form-item>
              <n-form-item path="config.inputValue" label-placement="left">
                <div class="flex items-center w-full h-full">
                  <div class="w-[162px] mr-[14px] h-full">
                    <n-select
                      v-model:value="formData.config.inputType"
                      :options="variableOptions"
                      @update:value="handleInputKeyTypeChange"
                    />
                  </div>
                  <div class="w-full h-full">
                    <n-input
                      v-if="formData.config.inputType == '1'"
                      v-model:value="formData.config.inputValue"
                      type="text"
                      placeholder="请输入"
                      maxlength="20"
                      show-count
                    >
                    </n-input>
                    <div class="h-full" v-else>
                      <AggregationSelector
                        v-model="formData.config.inputValue"
                        :options="aggregationOptions"
                        placeholder="请选择变量"
                        @change="handleAggregationChange"
                      />
                    </div>
                  </div>
                </div>
              </n-form-item>

              <n-form-item
                label-placement="left"
                class="setHeight mt-[24px] mb-[9px]"
              >
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 问题意图
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <img
                    @click="addcategoriesfun"
                    class="w-[16px] h-[16px] cursor-pointer"
                    src="@/assets/agentOrchestration/yituadd.png"
                  />
                </div>
              </n-form-item>

              <div
                class="knowledgelist h-[85px] py-[14px] px-[19px] flex items-center"
                v-for="(item, index) in formData.config.classes"
                :key="index"
              >
                <div class="w-[400px]">
                  <NInput
                    class="setinputbg"
                    @blur="changeEditStatus(index, false)"
                    autofocus
                    v-if="item.isEditing"
                    v-model:value="item.name"
                    placeholder="请输入"
                  ></NInput>
                  <p
                    v-show="!item.isEditing"
                    class="h-[22px] mb-[14px] text-[#000000] leading-normal flex items-center text-[16px]"
                  >
                    {{ item.name
                    }}<img
                      v-if="!item.disable"
                      @click="changeEditStatus(index, true)"
                      class="ml-[10px] w-[14px] h-[14px] cursor-pointer"
                      src="@/assets/agentOrchestration/editIcon.png"
                    />
                  </p>

                  <NInput
                    class="setinputbg"
                    v-if="!item.disable"
                    v-model:value="item.des"
                    placeholder="请输入关于此意图的描述"
                  ></NInput>
                  <p v-else class="text-[#ADB3BB]">{{ item.des }}</p>
                </div>
                <img
                  v-if="!item.disable"
                  @click="delcategoriesfun(index)"
                  class="w-[16px] h-[16px] cursor-pointer"
                  src="@/assets/agentOrchestration/delIcon2.png"
                />
              </div>
              <!-- <n-form-item
                label-placement="left"
                class="setHeight mt-[24px] mb-[12px]"
              >
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span> 记忆</div>
                </template>
              </n-form-item>
              <n-form-item
                path="config.yitushibielishi"
                label-placement="left"
                class="setHeight mb-[9px]"
              >
                <template #label>
                  <div class="rowstit">历史对话数据</div>
                </template>
                <div class="flex justify-end w-full">
                  <n-switch v-model:value="formData.config.yitushibielishi" />
                </div>
              </n-form-item>

              <n-form-item
                class="mb-[20px]"
                label=""
                path="config.yitushibieluinshu"
                v-if="formData.config.yitushibielishi"
                label-placement="left"
              >
                <template #label>
                  <div class="histit">对话轮数</div>
                </template>
                <div class="flex justify-end w-full h-full">
                  <div class="w-[114px]">
                    <n-input-number
                      min="0"
                      v-model:value="formData.config.yitushibieluinshu"
                      placeholder="轮数"
                    />
                  </div>
                </div>
              </n-form-item>
              <n-form-item label-placement="left" class="setHeight mt-[3px]">
                <template #label>
                  <div class="rowstit">知识库</div>
                </template>
                <div class="flex justify-end w-full">
                  <img
                    class="w-[16px] h-[16px] cursor-pointer"
                    src="@/assets/agentOrchestration/yitupeizhi.png"
                  />
                  <img
                    class="w-[16px] h-[16px] ml-[10px] cursor-pointer"
                    src="@/assets/agentOrchestration/yituzhishi.png"
                    @click="changeknowledgeBaseShow(true)"
                  />
                </div>
              </n-form-item>

              <div
                class="knowledgelist flex justify-between mt-[17px]"
                v-for="(item, index) in formData.config.databases"
                :key="index"
              >
                {{ item.name }}
                <img
                  class="w-[12px]"
                  src="@/assets/agentOrchestration/yituzhishidel.png"
                  @click="delknowledgefun(index)"
                />
              </div>
              <div
                class="w-[488px] mt-[12px] btnparent"
                v-if="
                  !formData.config.databases ||
                  formData.config.databases.length == '0'
                "
              >
                <NButton @click="changeknowledgeBaseShow(true)" dashed>
                  + 添加知识库启用记忆
                </NButton>
              </div> -->
              <n-form-item label-placement="left" class="setHeight mt-[24px]">
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span>输出</div>
                </template>
              </n-form-item>

              <n-form-item label-placement="left" class="setHeight mt-[6px]">
                <div class="w-full flex text-[#C7C7C7]">
                  <div class="w-[50%]">变量名称</div>
                  <div>数据类型</div>
                </div>
              </n-form-item>

              <n-form-item label-placement="left" class="setHeight mt-[14px]">
                <div class="w-full flex text-[#565756] items-center">
                  <div class="w-[50%]">匹配结果</div>
                  <div>文本</div>
                </div>
              </n-form-item>
            </template>

            <!-- 知识检索配置 -->
            <template v-if="node.type === 'knowledge'">
              <n-form-item label-placement="left" class="setHeight mb-[13px]">
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span> 输入</div>
                </template>
              </n-form-item>
              <n-form-item label-placement="left" class="setHeight mb-[8px]">
                <template #label>
                  <div class="text-[#565756]">查询变量</div>
                </template>
                <div class="flex justify-end w-full text-[#C7C7C7]">文本</div>
              </n-form-item>
              <n-form-item path="config.jiansuoType" label-placement="left">
                <div class="w-[162px] mr-[14px] h-[38px]">
                  <n-select
                    v-model:value="formData.config.jiansuoType"
                    :options="variableOptions"
                    default-value="0"
                    filterable
                  />
                </div>
                <div class="w-full h-[38px]">
                  <n-input
                    v-if="formData.config.jiansuoType == '0'"
                    v-model:value="formData.config.jiansuotypevalue"
                    type="text"
                    placeholder="请输入变量"
                    maxlength="20"
                    show-count
                  >
                  </n-input>
                  <AggregationSelector
                    v-else
                    v-model="formData.config.jiansuotypevalue"
                    :options="aggregationOptions"
                    placeholder="请选择变量"
                    @change="handleAggregationChange"
                  />
                </div>
              </n-form-item>
              <n-form-item
                label-placement="left"
                class="setHeight mt-[24px] mb-[12px]"
              >
                <template #label>
                  <div class="rowstit">知识库</div>
                </template>
                <div class="flex justify-end w-full">
                  <img
                    class="w-[16px] h-[16px] cursor-pointer"
                    src="@/assets/agentOrchestration/yitupeizhi.png"
                  />
                  <img
                    class="w-[16px] h-[16px] ml-[10px] cursor-pointer"
                    src="@/assets/agentOrchestration/yituzhishi.png"
                    @click="changeknowledgeBaseShow(true)"
                  />
                </div>
              </n-form-item>

              <div
                class="knowledgelist flex justify-between"
                v-for="(item, index) in formData.config.databases"
                :key="index"
              >
                {{ item.name }}
                <img
                  class="w-[12px]"
                  src="@/assets/agentOrchestration/yituzhishidel.png"
                  @click="delknowledgefun(index)"
                />
              </div>
              <n-form-item label-placement="left" class="setHeight mt-[12px]">
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span>输出</div>
                </template>
              </n-form-item>

              <n-form-item label-placement="left">
                <div class="w-full flex text-[#C7C7C7]">
                  <div class="w-[50%]">名称</div>
                  <div>数据类型</div>
                </div>
              </n-form-item>

              <n-form-item path="config.wenbenshuchu" label-placement="left">
                <div class="w-full flex text-[#565756] items-center">
                  <div class="w-[50%]">
                    <div class="w-[234px] h-[38px]">
                      <n-input
                        v-model:value="formData.config.wenbenshuchu"
                        type="text"
                        placeholder="请输入输出名称"
                      >
                      </n-input>
                    </div>
                  </div>
                  <div>文本</div>
                </div>
              </n-form-item>
            </template>

            <!-- 变量赋值配置 -->

            <template v-if="node.type === 'variable'">
              <n-form-item label-placement="left" class="setHeight mb-[17px]">
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span>变量赋值
                  </div>
                </template>
              </n-form-item>
              <div class="flex items-center mb-[12px] text-[#565756]">
                <div class="w-[114px]">变量名</div>
                <div class="w-[114px] ml-[12px] mr-[12px]">操作</div>
                <div class="w-[212px] mr-[6px]">变量值</div>
              </div>
              <div
                class="flex items-center mb-[12px] text-[#565756]"
                v-for="(item, index) in formData.config.variablelist"
                :key="index"
              >
                <div class="w-[114px] h-[38px] outputrow">
                  <n-input
                    v-model:value="item.key"
                    type="text"
                    placeholder="变量名"
                  >
                  </n-input>
                </div>
                <div class="w-[114px] ml-[12px] mr-[12px] h-[38px]">
                  <n-select
                    v-model:value="item.operate"
                    :options="operateOptions"
                    default-value="0"
                    filterable
                    :update:value="clearvariablevaluefun(item, index)"
                  />
                </div>
                <div class="w-[212px] mr-[6px] h-[38px] outputrow">
                  <AggregationSelector
                    v-if="item.operate == '0'"
                    v-model="item.value"
                    :options="aggregationOptions"
                    placeholder="请选择变量"
                    @change="handleAggregationChange"
                  />
                  <n-input
                    v-else
                    :disabled="item.operate == '2'"
                    v-model:value="item.value"
                    type="text"
                    placeholder="变量值"
                  >
                  </n-input>
                </div>
                <img
                  @click="delvariablefun(index)"
                  class="w-[16px] h-[16px] cursor-pointer"
                  src="@/assets/agentOrchestration/delIcon2.png"
                />
              </div>
              <div class="w-[488px] mt-[12px] btnparent">
                <NButton @click="addvariablefun" dashed> + 添加变量 </NButton>
              </div>
            </template>
            <!-- 条件节点配置 -->
            <template v-if="node.type === 'condition'">
              <div
                v-for="(item, index) in formData.config.conditionBranchArr"
                :key="index"
              >
                <n-form-item label-placement="left">
                  <div class="flex justify-between items-center w-full">
                    <div class="flex items-center">
                      <span class="text-[#000000] text-[16px]">{{
                        index == 0
                          ? "如果"
                          : index ==
                            formData.config.conditionBranchArr.length - 1
                          ? "否则"
                          : "否则如果"
                      }}</span>
                      <span class="condition-branch-label">
                        {{ item.name }}
                      </span>
                    </div>
                    <div
                      v-if="
                        formData.config.conditionBranchArr.length > 2 &&
                        index != formData.config.conditionBranchArr.length - 1
                      "
                      class="ml-[10px]"
                    >
                      <img
                        @click="removeElseBranch(index)"
                        class="w-[16px] cursor-pointer"
                        src="@/assets/agentOrchestration/delIcon2.png"
                        title="删除"
                      />
                    </div>
                  </div>
                </n-form-item>

                <div class="condition-section">
                  <!-- 条件组容器 -->
                  <div
                    class="condition-group"
                    v-if="item.conditionArr && item.conditionArr.length > 0"
                  >
                    <!-- 左侧花括号和逻辑操作符 -->
                    <div
                      class="condition-bracket-container"
                      v-if="item.conditionArr.length > 1"
                    >
                      <div
                        class="condition-bracket"
                        :style="{
                          height:
                            (item.conditionArr.length - 2) * 116 +
                            (item.conditionArr.length - 1) * 12 +
                            116 +
                            'px',
                        }"
                      >
                        <div class="bracket-top"></div>
                        <div class="bracket-middle">
                          <n-select
                            v-model:value="item.conditionLogic"
                            :options="logicOperatorOptions"
                            placeholder="且"
                            size="small"
                            class="group-logic-selector"
                            @update:value="handleLogicOperatorChange"
                          />
                        </div>
                        <div class="bracket-bottom"></div>
                      </div>
                    </div>

                    <!-- 条件列表 -->
                    <div class="condition-list">
                      <div
                        v-for="(condition, childrenindex) in item.conditionArr"
                        :key="childrenindex"
                        class="condition-item-wrapper"
                      >
                        <!-- 条件配置行 -->
                        <div class="flex items-center">
                          <div class="flex-1">
                            <div class="flex h-[38px]">
                              <div class="flex-1">
                                <AggregationSelector
                                  v-model="condition.variable"
                                  :options="aggregationOptions"
                                  placeholder="请选择变量"
                                />
                              </div>
                              <div class="w-[114px] ml-[12px]">
                                <n-select
                                  v-model:value="condition.operator"
                                  :options="conditionOperatorOptions"
                                  placeholder="等于"
                                  size="small"
                                />
                              </div>
                            </div>
                            <div class="flex mt-[12px] h-[38px]">
                              <div class="w-[114px]">
                                <n-select
                                  v-model:value="condition.field"
                                  :options="variableOptions"
                                  placeholder="0"
                                  size="small"
                                />
                              </div>
                              <div class="flex-1 ml-[12px] h-[38px] outputrow">
                                <AggregationSelector
                                  v-if="condition.field == '0'"
                                  v-model="condition.value"
                                  :options="aggregationOptions"
                                  placeholder="请选择变量"
                                />
                                <n-input
                                  v-else
                                  v-model:value="condition.value"
                                  type="text"
                                  placeholder="变量值"
                                >
                                </n-input>
                              </div>
                            </div>
                          </div>
                          <div
                            v-if="item.conditionArr.length > 1"
                            class="ml-[10px]"
                          >
                            <img
                              @click="removeCondition(index, childrenindex)"
                              class="action-icon"
                              src="@/assets/agentOrchestration/delIcon2.png"
                              title="删除"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 添加条件按钮 -->
                  <div
                    class="add-condition-wrapper"
                    v-if="
                      index != formData.config.conditionBranchArr.length - 1
                    "
                  >
                    <n-button
                      @click="addCondition(index)"
                      dashed
                      class="add-condition-button"
                    >
                      + 添加条件
                    </n-button>
                  </div>
                </div>
                <!-- 添加否则分支按钮 -->
                <div
                  class="add-else-branch-wrapper"
                  v-if="index == formData.config.conditionBranchArr.length - 1"
                >
                  <n-button
                    @click="addElseBranch"
                    type="info"
                    color="#125EFF"
                    class="add-else-branch-button"
                  >
                    + 添加分支
                  </n-button>
                </div>
              </div>
            </template>

            <!-- 聚合节点配置 -->
            <template v-if="node.type === 'aggregation'">
              <n-form-item label-placement="left">
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span>
                    变量聚合
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <span class="text-[#565756]">文本</span>
                </div>
              </n-form-item>

              <!-- 聚合变量选择下拉框 -->
              <n-form-item
                path="config.aggregationVariable"
                label-placement="left"
                class="mt-[12px]"
              >
                <AggregationSelector
                  v-model="formData.config.aggregationVariable"
                  :options="aggregationOptions"
                  placeholder="当前对话信息"
                  @change="handleAggregationChange"
                />
              </n-form-item>
            </template>

            <!-- 节点特定配置 -->
            <div class="config-section" v-if="showSpecificConfig">
         
              <!-- API节点配置 -->
              <template v-if="node.type === 'api'">
                <n-form-item label="请求方法" path="config.method">
                  <n-select
                    v-model:value="formData.config.method"
                    :options="methodOptions"
                    placeholder="选择请求方法"
                  />
                </n-form-item>

                <n-form-item label="接口地址" path="config.url">
                  <n-input
                    v-model:value="formData.config.url"
                    placeholder="请输入API接口地址"
                  />
                </n-form-item>

                <n-form-item label="超时时间(秒)" path="config.timeout">
                  <n-input-number
                    v-model:value="formData.config.timeout"
                    placeholder="请求超时时间"
                    :min="1"
                    :max="300"
                    :step="1"
                    style="width: 100%"
                  />
                </n-form-item>
              </template>

              <!-- 问题分类器配置 -->
              <template v-if="node.type === 'questionClassifier'">
                <n-form-item label="问题分类" path="config.categories">
                  <n-dynamic-input
                    v-model:value="formData.config.categories"
                    :on-create="createCategory"
                    #="{ index, value }"
                  >
                    <div class="flex flex-col space-y-2">
                      <div class="flex space-x-2">
                        <n-input
                          v-model:value="value.name"
                          placeholder="分类名称"
                          style="width: 30%"
                        />
                        <n-input
                          v-model:value="value.keywords"
                          placeholder="关键词(逗号分隔)"
                          style="width: 40%"
                        />
                        <n-select
                          v-model:value="value.matchRule"
                          :options="matchRuleOptions"
                          placeholder="匹配规则"
                          style="width: 30%"
                        />
                      </div>
                      <n-input
                        v-model:value="value.description"
                        placeholder="分类描述(可选)"
                        type="textarea"
                        :rows="1"
                      />
                    </div>
                  </n-dynamic-input>
                </n-form-item>

                <n-form-item label="默认分类" path="config.defaultCategory">
                  <n-input
                    v-model:value="formData.config.defaultCategory"
                    placeholder="当没有匹配的分类时使用的默认分类名称"
                  />
                </n-form-item>
              </template>
            </div>

            <!-- 执行日志部分 -->
            <!-- <div class="config-section" v-if="node && executionLogs.length > 0">
              <div class="section-header">
                <h4 class="section-title">
                  <span class="mr-2">📋</span>
                  执行日志
                  <span class="log-count-badge">{{ executionLogs.length }}</span>
                </h4>
                <button
                  class="clear-logs-btn"
                  @click="handleClearLogs"
                  title="清除所有日志"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m3 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6"/>
                  </svg>
                </button>
              </div>

              <div class="execution-logs">
                <div
                  v-for="log in executionLogs.slice(0, 5)"
                  :key="log.id"
                  class="log-entry"
                  :class="log.status"
                >
                  <div class="log-header">
                    <div class="log-status">
                      <span class="status-dot" :class="log.status"></span>
                      <span class="status-text">{{ getStatusText(log.status) }}</span>
                    </div>
                    <div class="log-meta">
                      <span class="duration">{{ formatDuration(log.duration) }}</span>
                      <span class="timestamp">{{ formatTimestamp(log.timestamp) }}</span>
                    </div>
                  </div>

                  <div class="log-details" v-if="log.output || log.error">
                    <div v-if="log.output" class="log-output">
                      <div class="detail-label">输出:</div>
                      <div class="detail-content">{{ formatOutput(log.output) }}</div>
                    </div>

                    <div v-if="log.error" class="log-error">
                      <div class="detail-label">错误:</div>
                      <div class="detail-content error-text">{{ log.error }}</div>
                    </div>
                  </div>
                </div>

                <div v-if="executionLogs.length > 5" class="more-logs">
                  还有 {{ executionLogs.length - 5 }} 条历史记录...
                </div>
              </div>
            </div> -->
          </n-form>
        </n-scrollbar>
      </div>

      <!-- 面板底部 -->
      <div class="panel-footer">
        <div class="footer-actions">
          <n-button @click="handleClose">取消</n-button>
          <n-button type="primary" @click="handleSave">保存配置</n-button>
        </div>
      </div>
    </div>
  </div>

  <!-- 模型配置弹窗 -->
  <NModal v-model:show="modelParameterShow">
    <NCard
      class="modelParametercard"
      style="width: 644px"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div
        class="flex items-center justify-between h-[70px] text-[17px] font-medium text-[#1F1F1F] pl-[26px] pr-[23px] border-b-[1px] border-[#E5E5E5]"
      >
        <span>模型参数</span><span>×</span>
      </div>
      <NForm ref="modelParameterformRef" :model="modelParameterformData">
        <n-form-item path="style">
          <template #label>
            <div
              class="h-[21px] text-[#1F1F1F] text-[15px] mt-[28px] mb-[7px] pl-[26px] pr-[23px] font-medium"
            >
              模型风格
            </div>
          </template>
          <NRadioGroup
            v-model:value="modelParameterformData.style"
            name="style"
            @update:value="updatemodelParameterfun"
          >
            <div
              class="w-[592px] h-[38px] bg-[#F9FAFC] mx-auto rounded-lg pl-[18px] pr-[18px] flex items-center justify-between"
            >
              <div v-for="(item, index) in modelParameterOptions" :key="index">
                <NRadio :key="item.dictKey" :value="item.dictKey">
                  {{ item.dictValue }}
                </NRadio>
              </div>
            </div>
          </NRadioGroup>
        </n-form-item>

        <n-form-item
          path="temperature"
          label-placement="left"
          class="mt-[26px]"
        >
          <template #label>
            <div
              class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center"
            >
              模型温度
            </div>
          </template>
          <div class="w-full flex justify-end pr-[23px]">
            <div class="w-[112px] h-[38px]">
              <NInput
                v-model:value="modelParameterformData.temperature"
              ></NInput>
            </div>
          </div>
        </n-form-item>
        <n-form-item path="topP" label-placement="left" class="mt-[24px]">
          <template #label>
            <div
              class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center"
            >
              Top P
            </div>
          </template>
          <div class="w-full flex justify-end pr-[23px]">
            <div class="w-[112px] h-[38px]">
              <NInput v-model:value="modelParameterformData.topP"></NInput>
            </div>
          </div>
        </n-form-item>
        <n-form-item path="maxTokens" label-placement="left" class="mt-[24px]">
          <template #label>
            <div
              class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center"
            >
              最大输出长度
            </div>
          </template>
          <div class="w-full flex justify-end pr-[23px]">
            <div class="w-[112px] h-[38px]">
              <NInput v-model:value="modelParameterformData.maxTokens"></NInput>
            </div>
          </div>
        </n-form-item>
      </NForm>
      <template #footer>
        <div
          class="flex w-full justify-end pl-[26px] pr-[23px] pb-[27px] mt-[47px]"
        >
          <div class="btnparent w-[80px] h-[36px]">
            <NButton @click="changemodelParameterShow(false)">取消</NButton>
          </div>
          <div class="btnparent w-[80px] h-[36px] ml-[16px]">
            <NButton
              @click="changemodelParameterfun()"
              type="info"
              color="#125EFF"
              >保存</NButton
            >
          </div>
        </div>
      </template>
    </NCard>
  </NModal>

  <!-- 知识库添加弹窗 -->
  <NModal v-model:show="knowledgeBaseShow">
    <NCard
      class="modelParametercard"
      style="width: 864px"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div
        class="flex items-center justify-between h-[70px] text-[17px] font-medium text-[#1F1F1F] pl-[26px] pr-[23px] border-b-[1px] border-[#E5E5E5]"
      >
        <span>添加知识库</span><span>×</span>
      </div>
      <div
        class="pl-[26px] pr-[23px] flex h-[38px] items-center mb-[20px] mt-[25.83px]"
      >
        <div class="w-[678px]">
          <NInput
            v-model:value="searchknowledgeBase"
            placeholder="搜索知识库"
          ></NInput>
        </div>
        <div class="btnparent w-[112px] h-[38px] ml-[16px]">
          <NButton type="info" color="#125EFF">新建知识库</NButton>
        </div>
      </div>
      <NCheckboxGroup v-model:value="knowledgeBasechecklist">
        <div
          class="flex justify-between bg-[#F6F6F6] w-[814px] mx-auto rounded-lg h-[38px] items-center pl-[20px] pr-[25px] mb-[10px]"
          v-for="(item, index) in knowledgeBaseshow"
          :key="index"
        >
          <NCheckbox :value="item.value" :label="item.name" />
          <div class="text-[#A4A6AB] text-[13px]">{{ item.size }}个文件</div>
        </div>
      </NCheckboxGroup>
      <template #footer>
        <div
          class="flex w-full justify-between items-center pl-[26px] pr-[23px] pb-[27px] mt-[147px]"
        >
          <div class="text-[13px] text-[#A4A6AB]">
            已选 {{ knowledgeBaseCheckNum }} 个
          </div>
          <div class="flex">
            <div class="btnparent w-[80px] h-[36px]">
              <NButton @click="changeknowledgeBaseShow(false)">取消</NButton>
            </div>
            <div class="btnparent w-[80px] h-[36px] ml-[16px]">
              <NButton @click="saveknowledgeBasefun" type="info" color="#125EFF"
                >保存</NButton
              >
            </div>
          </div>
        </div>
      </template>
    </NCard>
  </NModal>

  <!-- 变量选择器弹窗 -->
  <n-modal v-model:show="showVariableSelector">
    <n-card style="width: 600px" title="选择变量" :bordered="false" size="huge">
      <div class="variable-selector-content">
        <div class="selector-description">
          选择要插入到提示词中的变量，变量将以
          <code>{{ 变量名 }}</code> 的格式插入。
        </div>

        <VariableSelector
          v-model="selectedVariableForInsert"
          placeholder="选择要插入的变量"
          variable-type="all"
          :variable-option-arr="aggregationOptions"
          :show-syntax-hint="true"
          :show-usage-stats="true"
          @variable-select="handleVariableSelect"
          @open-variable-management="handleOpenVariableManagement"
        />

        <div v-if="selectedVariableForInsert" class="insert-preview">
          <div class="preview-title">插入预览：</div>
          <div class="preview-content">
            <code>{{ selectedVariableForInsert }}</code>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <n-button @click="showVariableSelector = false">取消</n-button>
          <n-button
            type="primary"
            @click="insertVariableToPrompt"
            :disabled="!selectedVariableForInsert"
          >
            插入变量
          </n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted, h } from "vue";
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NSlider,
  NButton,
  NInputNumber,
  NScrollbar,
  NDropdown,
  NDynamicInput,
  useMessage,
  useDialog,
  NPopover,
  NDataTable,
  NCard,
  NDivider,
  NRadio,
  NRadioGroup,
  NModal,
  NCheckbox,
  NCheckboxGroup,
  SelectGroupOption,
  SelectOption,
} from "naive-ui";
import type { FlowNode } from "@/store/modules/orchestration";
import { useOrchestrationStore } from "@/store";
import VariableSelector from "./VariableSelector.vue";
import AggregationSelector from "./AggregationSelector.vue";
import { generateId } from "@/store/modules/orchestration";
import { agentParam, museModels,optimize } from "@/api/workShop";
const props = defineProps<{
  show: boolean;
  node: FlowNode | null;
}>();

const emit = defineEmits<{
  "update:show": [value: boolean];
  save: [config: any];
}>();

const message = useMessage();
const dialog = useDialog();
const formRef = ref();
const modelParameterformRef = ref();
const orchestrationStore = useOrchestrationStore();

// 运行状态
const isRunning = ref(false);

// 变量选择器相关
const showVariableSelector = ref(false);
const selectedVariableForInsert = ref("");
const selectedVariableObj = ref({});

// 聚合节点选择处理
const handleAggregationChange = (value: string, variable: any, group: any) => {
  console.log("选择的聚合变量:", { value, variable, group });
  formData.value.config.inputKey = value;
};

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 表单数据
const formData = ref({
  label: "",
  description: "",
  config: {
    // knowledgeList: [{ name: "知识库名称1" }, { name: "知识库名称2" }],
  },
});

const modelParameterformData = ref({
  style: "",
  temperature: "",
  topP: "",
  maxTokens: "",
});
//模型配置弹窗是否展示
var modelParameterShow = ref(false);
//知识库弹窗是否展示
var knowledgeBaseShow = ref(false);
//知识库已选中列表数据
var knowledgeBasechecklist = ref([]);
//知识库列表搜索
var searchknowledgeBase = ref("");
//知识库列表
var knowledgeBaseList = ref([
  { name: "知识库名称1", size: "34", value: "001" },
  { name: "知识库名称2", size: "23", value: "002" },
]);
//知识库展示列表
var knowledgeBaseshow = computed(() => {
  if (searchknowledgeBase.value) {
    return knowledgeBaseList.value.filter((item) => {
      return item.name.includes(searchknowledgeBase.value);
    });
  } else {
    return knowledgeBaseList.value;
  }
});
//知识库展示已选中数量
var knowledgeBaseCheckNum = computed(() => {
  return knowledgeBasechecklist.value.length;
});
// 监听节点变化，更新表单数据
watch(
  () => props.node,
  (newNode) => {
    if (newNode) {
      const config = { ...newNode.data.config } || {};
      // 结束节点初始化以及编辑逻辑
          if(newNode.type === "end"){
        console.log(newNode.data.config);
        // 新增：检查huifuneirong是否有值并回显
        if (newNode.data.config?.huifuneirong) {
          nextTick(() => {
            rendereditableDiv(newNode.data.config?.huifuneirong);
          });
        }
      }

      // 意图识别节点初始化以及编辑逻辑
      if (newNode.type === "question-classifier") {
        if (!config.modelConfig) {
          config.modelConfig = {};
          config.modelConfig.model = null;
          config.modelConfig.modelName = "";
          config.modelConfig.completionParams = {};
          nextTick(() => {
            if (agentParams.value) {
              config.modelConfig.completionParams = {
                temperature: agentParams.value.model_style[0].dictKey,
                maxTokens: agentParams.value.model_max_length,
                topP: agentParams.value.model_topp,
                style: agentParams.value.model_style[0].dictKey,
              };
            }
          });
        }
        if (!config.inputType) {
          config.inputType = "0";
        }
        if (!config.classes) {
             config.classes = [
          {
            id: generateId() + "-output",
            name: "意图",
            des: "",
            isEditing: false,
          },
          {
            id: generateId() + "-output",
            name: "其他",
            des: "未命中以上意图",
            disable: true,
          },
        ];
        }
    
      }
      // 文本生成节点初始化以及编辑逻辑
    if(newNode.type === "llm"){
        config.tishici = config.tishici?config.tishici:"";
        config.wenbenshuchu = config.wenbenshuchu?config.wenbenshuchu:"文本输出";
           if (newNode.data.config?.tishici) {
          nextTick(() => {
            rendereditableDiv(newNode.data.config?.tishici);
          });
        }
         if (!config.modelConfig) {
          config.modelConfig = {};
          config.modelConfig.model = null;
          config.modelConfig.modelName = "";
          config.modelConfig.completionParams = {};
          nextTick(() => {
            if (agentParams.value) {
              config.modelConfig.completionParams = {
                temperature: agentParams.value.model_style[0].dictKey,
                maxTokens: agentParams.value.model_max_length,
                topP: agentParams.value.model_topp,
                style: agentParams.value.model_style[0].dictKey,
              };
            }
          });
        }
      }

      // 为问题分类器节点初始化默认分类
      if (newNode.type === "questionClassifier" && !config.categories) {
        config.categories = [
          {
            id: "tech_" + Date.now(),
            name: "技术问题",
            keywords: "bug,错误,技术,代码",
            description: "技术相关的问题",
            matchRule: "contains",
          },
          {
            id: "business_" + Date.now(),
            name: "业务问题",
            keywords: "需求,业务,流程,规则",
            description: "业务相关的问题",
            matchRule: "contains",
          },
        ];
        config.defaultCategory = "其他问题";
      }

      // 为条件节点初始化默认条件
      if (newNode.type === "condition" && !config.conditions) {
        config.conditionBranchArr = [
          {
            name: "分支1",
            disabled: false,
            conditionArr: [
              { variable: "", operator: "等于", field: "0", value: "" },
            ],
            conditionLogic: "",
          },
          {
            name: "分支2",
            disabled: true,
            conditionArr: [],
            conditionLogic: "",
          },
        ];
        config.conditionLogic = "且"; // 如果条件组的逻辑操作符
      }

      // 为聚合节点初始化默认配置
      if (newNode.type === "aggregation" && !config.aggregationVariable) {
        config.aggregationVariable = "";
      }
  
      console.log(newNode);

      formData.value = {
        label: newNode.data.label || "",
        icon: newNode.data.icon,
        description: newNode.data.description || "",
        config,
      };
   


    }
  },
  { immediate: true, deep: true }
);
var startcolumns = ref([
  {
    title: "会话数据变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);
var environmentVariableColumns = ref([
  {
    title: "环境变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);
var fileColumns = ref([
  {
    title: "会话数据变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);
var intentionRecognitionColumns = ref([
  {
    title: "变量名称",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);
var startdata = computed(() => {
  let data = [
    {
      name: "历史对话信息",
      type: "数组 [结构化数据]",
      isreturn: formData.value.config.duolun ? true : false,
    },
    {
      name: "当前对话信息",
      type: "文本",
      isreturn: true,
    },
    {
      name: "当前对话文件",
      type: "数组[文件]",
      isreturn: formData.value.config.shangchuan ? true : false,
    },
    {
      name: "用户ID",
      type: "文本",
      isreturn: true,
    },
    {
      name: "会话ID",
      type: "文本",
      isreturn: formData.value.config.duolun ? true : false,
    },
    {
      name: "对话轮次",
      type: "数值",
      isreturn: formData.value.config.duolun ? true : false,
    },
  ];

  return data.filter((item) => item.isreturn);
});
var environmentVariableData = ref([
  {
    name: "智能体ID",
    type: "文本",
  },
  {
    name: "触发时间",
    type: "文本",
  },
]);
var fileData = ref([
  {
    name: "文件信息",
    type: "结构化数据",
  },
]);
var intentionRecognitionData = ref([
  {
    name: "匹配结果",
    type: "文本",
  },
]);
// 计算属性


const statusClass = computed(() => {
  if (!props.node) return "status-idle";
  switch (props.node.data.status) {
    case "running":
      return "status-running";
    case "success":
      return "status-success";
    case "error":
      return "status-error";
    default:
      return "status-idle";
  }
});

const statusText = computed(() => {
  if (!props.node) return "空闲";
  const statusMap = {
    idle: "空闲",
    running: "运行中",
    success: "成功",
    error: "错误",
  };
  return statusMap[props.node.data.status as keyof typeof statusMap] || "未知";
});

const optimizing = async () => {
  // optimizingLogding.value = true;
  try {
    const res = await optimize({
      modelTemp: formData.value.config.modelConfig.completionParams.temperature ,
      promptTemplate: formData.value.config.tishici,
      modelId:formData.value.config.modelConfig.model,
      maxLength:formData.value.config.modelConfig.completionParams.maxTokens,
    });
    // model.value.promptTemplate = res.data;
    console.log(res);
  } catch (e) {
    message.error("优化失败");
  } finally {
    // optimizingLogding.value = false;
  }
};

// 是否显示运行按钮
const showRunButton = computed(() => {
  return (
    props.node &&
    ["llm", "api", "condition", "questionClassifier"].includes(props.node.type)
  );
});

// 执行日志相关
const executionLogs = computed(() => {
  return props.node?.data.executionLogs || [];
});

// 选项数据
const modelOptions = ref([
  { label: "GPT-3.5-turbo", value: "gpt-3.5-turbo" },
  { label: "GPT-4", value: "gpt-4" },
  { label: "Claude-3-sonnet", value: "claude-3-sonnet" },
]);
const variableOptions = [
  { label: "变量", value: "0" },
  { label: "固定", value: "1" },
];
const huanjingOptions = ref([
  { label: "历史对话信息", type: "结构化数据", value: "0" },
]);
// 获取数据类型标签文本
const getDataTypeLabel = (dataType: string) => {
  const labelMap: Record<string, string> = {
    string: "文本",
    number: "数字",
    boolean: "布尔",
    array: "数组",
    object: "对象",
  };
  return labelMap[dataType] || dataType;
};
orchestrationStore.getVariablesByType("nodeVariable").map((variable) => ({
  id: variable.id,
  name: variable.name,
  identifier: variable.name, // 使用name作为identifier
  value: variable.value || "",
  type: getDataTypeLabel(variable.valueType),
  nodeId: variable.nodeId,
  nodeName: variable.nodeName,
  nodeType: variable.nodeType,
  expanded: false,
}));
// 聚合节点选项数据
// 获取节点的直接前置节点（连接到当前节点的节点）
const getDirectPreviousNodes = (nodeId: string) => {
  if (!orchestrationStore.currentFlow) return [];
  return orchestrationStore.currentFlow.edges
    .filter((edge) => edge.target === nodeId)
    .map((edge) => edge.source);
};

// 递归获取所有前置节点（包括间接连接的节点）
const getAllPreviousNodes = (
  nodeId: string,
  visited: Set<string> = new Set()
): string[] => {
  if (!orchestrationStore.currentFlow) return [];

  // 避免循环依赖
  if (visited.has(nodeId)) return [];
  visited.add(nodeId);

  const directPrevious = getDirectPreviousNodes(nodeId);
  const allPrevious = new Set(directPrevious);

  // 递归获取每个直接前置节点的前置节点
  directPrevious.forEach((prevNodeId) => {
    const indirectPrevious = getAllPreviousNodes(prevNodeId, visited);
    indirectPrevious.forEach((node) => allPrevious.add(node));
  });

  return Array.from(allPrevious);
};

// 检查节点是否与其他节点连接
const isNodeConnected = (nodeId: string) => {
  const previousNodes = getDirectPreviousNodes(nodeId);
  const nextNodes = orchestrationStore.getNextNodes(nodeId);
  return previousNodes.length > 0 || nextNodes.length > 0;
};

const aggregationOptions = ref([]);
watch(visible, () => {
  if (visible.value && props.node) {
    // 获取当前节点的所有前置节点（包括间接连接的）
    const allPreviousNodes = getAllPreviousNodes(props.node.id);
    const isConnected = isNodeConnected(props.node.id);

    // 如果没有连接其他节点，只获取环境变量
    if (!isConnected) {
      var variables = orchestrationStore
        .getVariablesByType("envVariable")
        .map((variable) => ({
          name: variable.name,
          type: getDataTypeLabel(variable.valueType),
          id: variable.id,
        }));
      var envVariablesList: any[] = [];
      if (variables.length > 0) {
        envVariablesList = [
          {
            label: "环境变量",
            value: "envVariable",
            variables: variables,
          },
        ];
      }
      aggregationOptions.value = envVariablesList;
    } else {
      // 如果连接了其他节点，获取所有前置节点的输出变量
      var nodeVariableList = orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => {
          // 获取所有前置节点的变量
          return (
            allPreviousNodes.includes(variable.nodeId || "") &&
            variable.nodeId !== props.node?.id &&
            variable.nodeType !== "start"
          );
        })
        .map((variable) => ({
          label: variable.nodeName || "",
          value: variable.nodeId || "",
          variables: [
            {
              name: variable.name,
              type: getDataTypeLabel(variable.valueType),
              id: variable.id,
            },
          ],
        }));

      var startnodevariable = orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => {
          // 获取所有前置节点中的开始节点变量
          return (
            allPreviousNodes.includes(variable.nodeId || "") &&
            variable.nodeId !== props.node?.id &&
            variable.nodeType === "start"
          );
        })
        .map((variable) => ({
          name: variable.name,
          type: getDataTypeLabel(variable.valueType),
          id: variable.id,
        }));

      var startnodeVariableList: any[] = [];
      if (startnodevariable.length > 0) {
        startnodeVariableList = [
          {
            label: "开始节点",
            value: "nodeVariable",
            variables: startnodevariable,
          },
        ];
      }

      var variables = orchestrationStore
        .getVariablesByType("envVariable")
        .map((variable) => ({
          name: variable.name,
          type: getDataTypeLabel(variable.valueType),
          id: variable.id,
        }));
      var envVariablesList: any[] = [];
      if (variables.length > 0) {
        envVariablesList = [
          {
            label: "环境变量",
            value: "envVariable",
            variables: variables,
          },
        ];
      }

      console.log(
        "节点连接状态:",
        isConnected,
        "所有前置节点:",
        allPreviousNodes
      );
      console.log(
        "环境变量:",
        envVariablesList,
        "开始节点变量:",
        startnodeVariableList,
        "节点变量:",
        nodeVariableList
      );

      aggregationOptions.value = envVariablesList
        .concat(startnodeVariableList)
        .concat(nodeVariableList);
    }
  }
  if(!visible.value && props.node){
console.log("关闭配置弹窗");

  }
});
// 条件节点相关选项

const conditionOperatorOptions = [
  { label: "等于", value: "等于" },
  { label: "不等于", value: "不等于" },
  { label: "大于", value: "大于" },
  { label: "小于", value: "小于" },
  { label: "包含", value: "包含" },
  { label: "不包含", value: "不包含" },
];

// 逻辑操作符选项
const logicOperatorOptions = [
  { label: "且", value: "且" },
  { label: "或", value: "或" },
  { label: "非", value: "非" },
];

const operateOptions = [
  { label: "覆盖", value: "0" },
  { label: "设置", value: "1" },
  { label: "清空", value: "2" },
];
const modelParameterOptions = ref([
  { key: "创意", value: "0" },
  { key: "平衡", value: "1" },
  { key: "严肃", value: "2" },
  { key: "自定义", value: "3" },
]);
var agentParams = ref(null);
const methodOptions = [
  { label: "GET", value: "GET" },
  { label: "POST", value: "POST" },
  { label: "PUT", value: "PUT" },
  { label: "DELETE", value: "DELETE" },
];

// 问题分类器匹配规则选项
const matchRuleOptions = [
  { label: "包含匹配", value: "contains" },
  { label: "精确匹配", value: "exact" },
  { label: "正则匹配", value: "regex" },
];

// 更多操作菜单选项
const moreOptions = [
  {
    label: "复制节点",
    key: "duplicate",
    icon: () => "📋",
  },
  {
    label: "删除节点",
    key: "delete",
    icon: () => "🗑️",
  },
];

// 表单验证规则
const formRules = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  "config.modelConfig.model": [
    { required: true, message: "请选择模型", trigger: "blur" },
  ],
  "config.inputValue": [
    { required: true, message: "请输入变量名称", trigger: "blur" },
  ],
};

// 创建新分类的函数
const createCategory = () => {
  return {
    id: `category_${Date.now()}`,
    name: "",
    keywords: "",
    description: "",
    matchRule: "contains",
  };
};

// 关键词字符串转数组
const keywordsToArray = (keywords: string) => {
  return keywords
    .split(",")
    .map((k) => k.trim())
    .filter((k) => k.length > 0);
};

// 关键词数组转字符串
const arrayToKeywords = (keywords: string[]) => {
  return keywords.join(", ");
};

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleOverlayClick = () => {
  handleClose();
};

const handleSave = async () => {
  // 处理editableDiv中的按钮内容
  if (editableDiv.value) {
    // 获取所有按钮元素
    const buttons = editableDiv.value.querySelectorAll('div[contenteditable="false"]');
    // 遍历按钮并处理
    buttons.forEach(btn => {
      const btnContent = btn.dataset.id;
      // 创建文本节点，将按钮内容用{{}}包含
      const textNode = document.createTextNode(`{{${btnContent}}}`);
      // 替换按钮元素
      btn.parentNode?.replaceChild(textNode, btn);
    });
    
    // 获取处理后的完整内容
    const processedContent = editableDiv.value.innerHTML;
    // 根据节点类型更新对应的表单数据
        if (props.node.type === "start") {
      if (formData.value.config.duolun) {
        handleChangeStart(true, "0");
      } else {
        handleChangeStart(false, "0");
      }
      if (formData.value.config.shangchuan) {
        handleChangeStart(true, "1");
      } else {
        handleChangeStart(false, "1");
      }
    }
    if (props.node.type === "llm") {
     const variables = orchestrationStore.getVariablesByNodeId(props.node.id)[0] || [];
     try {
    orchestrationStore.updateVariable(variables.id, {
        name: formData.value.config.wenbenshuchu,
        code: variables.code,
        valueType: variables.type,
        value: variables.value
      })
     } catch (error) {
       message.error(error.message);
      return
     }
      
    
      formData.value.config.tishici = processedContent;
    } else if (props.node.type === "end") {
      formData.value.config.huifuneirong = processedContent;
    }
  }
  
  try {
    await formRef.value?.validate();

    emit("save", formData.value);
    handleClose();
    message.success("节点配置保存成功");
  } catch (error) {
    message.error("请检查表单输入");
  }
};

// 运行节点
const handleRunNode = async () => {
  if (!props.node || isRunning.value) return;

  try {
    isRunning.value = true;
    await orchestrationStore.runNode(props.node.id);
    message.success("节点运行成功");
  } catch (error) {
    console.error("节点运行失败:", error);
    message.error("节点运行失败");
  } finally {
    isRunning.value = false;
  }
};

// 处理更多操作菜单
const handleMoreAction = (key: string) => {
  if (!props.node) return;

  switch (key) {
    case "delete":
      handleDeleteNode();
      break;
    case "duplicate":
      handleDuplicateNode();
      break;
  }
};

// 删除节点
const handleDeleteNode = () => {
  if (!props.node) return;

  // 检查是否为开始/结束节点
  if (props.node.type === "start" || props.node.type === "end") {
    message.warning("开始节点和结束节点不能删除");
    return;
  }

  dialog.warning({
    title: "删除确认",
    content: `确定要删除节点 "${props.node.data.label}" 吗？此操作不可撤销。`,
    positiveText: "确定删除",
    negativeText: "取消",
    onPositiveClick: () => {
      if (props.node) {
        orchestrationStore.removeNode(props.node.id);
        handleClose();
        message.success("节点删除成功");
      }
    },
  });
};

// 复制节点
const handleDuplicateNode = () => {
  if (!props.node) return;
  try {
    orchestrationStore.duplicateNode(props.node.id);
    message.success("节点复制成功");
  } catch (error) {
    console.error("节点复制失败:", error);
    message.error("节点复制失败");
  }
};

// 清除执行日志
const handleClearLogs = () => {
  if (!props.node) return;

  dialog.warning({
    title: "清除确认",
    content: "确定要清除所有执行日志吗？此操作不可撤销。",
    positiveText: "确定清除",
    negativeText: "取消",
    onPositiveClick: () => {
      if (props.node) {
        orchestrationStore.clearNodeExecutionLogs(props.node.id);
        message.success("执行日志已清除");
      }
    },
  });
};

// 日志相关工具方法
const getStatusText = (status: string): string => {
  const statusMap = {
    idle: "空闲",
    running: "运行中",
    success: "成功",
    error: "失败",
  };
  return statusMap[status as keyof typeof statusMap] || "未知";
};

const formatDuration = (duration: number): string => {
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }
};

const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

const formatOutput = (output: any): string => {
  if (typeof output === "string") return output;
  if (typeof output === "object") {
    const str = JSON.stringify(output);
    return str.length > 200 ? str.substring(0, 200) + "..." : str;
  }
  return String(output);
};

const delknowledgefun = (index: number) => {
  console.log("意图识别节点删除知识库函数+");
  if (formData.value.config.databases) {
    formData.value.config.databases.splice(index, 1);
  }
};
const delproblemfun = (index: number) => {
  console.log("意图识别节点删除问题函数+");
  if (formData.value.config.jianyiwenti) {
    formData.value.config.jianyiwenti.splice(index, 1);
  }
};
const addproblemfun = () => {
  if (!formData.value.config.jianyiwenti) {
    formData.value.config.jianyiwenti = [];
  }
  formData.value.config.jianyiwenti.push({
    type: "0",
    value: "",
  });
};
const handleChangewentijianyi = (value: any) => {
  if (value) {
    formData.value.config.jianyitype = "0";
  }
};
var wentiyituname = ref("");
const addcategoriesfun = () => {
  formData.value.config.classes.unshift({
    id: generateId() + "-output",
    name: "意图",
    des: "",
  });
};
const delcategoriesfun = (index: number) => {
  formData.value.config.classes.splice(index, 1);
};
const addvariablefun = () => {
  if (!formData.value.config.variablelist) {
    formData.value.config.variablelist = [];
  }
  formData.value.config.variablelist.push({
    key: "",
    value: "",
    operate: "",
  });
};
const delvariablefun = (index: number) => {
  formData.value.config.variablelist.splice(index, 1);
};
const changeEditStatus = (index: number, status: boolean) => {
  console.log(status);

  formData.value.config.classes[index].isEditing = status;
  if (status) {
    nextTick(() => {});
  }
};
const changejianyitypefun = (type: string) => {
  formData.value.config.jianyitype = type;
};

const changemodelParameterShow = (status: boolean) => {
  if (status) {
    modelParameterformData.value.temperature =
      formData.value.config.modelConfig.completionParams.temperature;
    modelParameterformData.value.maxTokens =
      formData.value.config.modelConfig.completionParams.maxTokens;
    modelParameterformData.value.topP =
      formData.value.config.modelConfig.completionParams.topP;
    modelParameterformData.value.style =
      formData.value.config.modelConfig.completionParams.style;
  }
  modelParameterShow.value = status;
};
const changemodelParameterfun = async () => {
  await modelParameterformRef.value?.validate();
  formData.value.config.modelConfig.completionParams.temperature =
    modelParameterformData.value.temperature;
  formData.value.config.modelConfig.completionParams.maxTokens =
    modelParameterformData.value.maxTokens;
  formData.value.config.modelConfig.completionParams.topP =
    modelParameterformData.value.topP;
  formData.value.config.modelConfig.completionParams.style =
    modelParameterformData.value.style;
  changemodelParameterShow(false);
};
const changeknowledgeBaseShow = (status: boolean) => {
  knowledgeBaseShow.value = status;
};
const saveknowledgeBasefun = () => {
  formData.value.config.databases = [];
  knowledgeBasechecklist.value.forEach((item: any) => {
    console.log(item);
    let data = knowledgeBaseList.value.find((items) => {
      return items.value === item;
    });
    formData.value.config.databases.push(data);
  });
  changeknowledgeBaseShow(false);
};
const clearvariablevaluefun = (item: any, index: number) => {
  if (item.operate == "2") {
    formData.value.config.variablelist[index].value = "";
  }
};

// 条件节点相关方法
const addCondition = (index: number) => {
  formData.value.config.conditionBranchArr[index].conditionArr.push({
    variable: "",
    operator: "等于",
    field: "0",
    value: "",
  });
};
const removeElseBranch = (index: number) => {
  if (formData.value.config.conditionBranchArr) {
    formData.value.config.conditionBranchArr.splice(index, 1);
  }
};
const removeCondition = (index: number, childrenindex: number) => {
  if (formData.value.config.conditionBranchArr) {
    formData.value.config.conditionBranchArr[index].conditionArr.splice(
      childrenindex,
      1
    );
  }
};

// 处理逻辑操作符变更
const handleLogicOperatorChange = (value: string) => {
  console.log("主条件逻辑操作符变更为:", value);
  // 这里可以添加额外的逻辑处理
};

const handleElseIfLogicOperatorChange = (value: string) => {
  console.log("否则如果逻辑操作符变更为:", value);
  // 这里可以添加额外的逻辑处理
};
const addElseBranch = () => {
  formData.value.config.conditionBranchArr.splice(
    formData.value.config.conditionBranchArr.length - 1,
    0,
    {
      name: "分支" + formData.value.config.conditionBranchArr.length,
      disabled: false,
      conditionArr: [{ variable: "", operator: "等于", field: "0", value: "" }],
      conditionLogic: "",
    }
  );
};
const initdatafun = async () => {
  const models = await museModels();
  const res = await agentParam();
  console.log(res);
  modelParameterOptions.value = res.data.model_style;
  agentParams.value = res.data;
  modelOptions.value = models.data.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
};
onMounted(() => {
  initdatafun();
});

// ==================== 变量选择器相关方法 ====================

// 处理变量选择
const handleVariableSelect = (variable: any) => {
  console.log("选中变量:", variable);
  selectedVariableObj.value = variable;
};

// 打开变量管理面板
const handleOpenVariableManagement = () => {
  showVariableSelector.value = false;
  // 这里可以触发打开变量管理面板的事件
  // emit('openVariableManagement')
};

// 插入变量到提示词
const insertVariableToPrompt =async () => {
  if (!selectedVariableForInsert.value) return;
  const variableSyntax = selectedVariableForInsert.value;

  // 根据当前节点类型插入到对应的字段
  if (props.node?.type === "llm" || props.node?.type === "end") {
    nextTick(() => {
      focusEditableDiv();
      // 调用insertVariable函数，传入选中的变量名,变量id
      insertVariable(variableSyntax,selectedVariableObj.value.id);
      
  // 关闭弹窗并清空选择
  showVariableSelector.value = false;
  selectedVariableForInsert.value = "";
  selectedVariableObj.value = {};

  message.success("变量已插入");
    });
  }

};

const editableDiv = ref<HTMLElement | null>(null);
// 记录光标位置的变量
const savedRange = ref<Range | null>(null);

// 保存光标位置
function saveCursorPosition() {
  const selection = window.getSelection();
  if (selection && selection.rangeCount > 0) {
    savedRange.value = selection.getRangeAt(0).cloneRange();
  }
}

// 恢复光标位置
function restoreCursorPosition() {
  if (savedRange.value) {
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(savedRange.value);
    }
  }
}

// 获取焦点的辅助函数
function focusEditableDiv() {
  if (editableDiv.value) {
    editableDiv.value.focus();
    // 确保光标在内容末尾
    const selection = window.getSelection();
    const range = document.createRange();
    range.selectNodeContents(editableDiv.value);
    range.collapse(false); // false表示光标在末尾
    selection?.removeAllRanges();
    selection?.addRange(range);
  }
}

function handleKeydown(e: KeyboardEvent) {
  console.log(e.key);
  // contenteditable 默认支持删除，无需特殊处理
}

// 处理输入事件，检测{}
function handleInput(e: Event) {
  const target = e.target as HTMLElement;
  const text = target.textContent || "";
  console.log(target);
  console.log(text);

  // 检查是否包含{}
  if (text.includes("{}")) {
    // 延迟执行，确保DOM更新完成
    nextTick(() => {
      // 获取当前光标位置
      const selection = window.getSelection();
      if (!selection || !selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      const startOffset = range.startOffset;

      // 只删除光标位置附近的{}，而不是整个文本内容
      const textNode = range.startContainer;
      if (textNode.nodeType === Node.TEXT_NODE) {
        const textContent = textNode.textContent || "";
        const beforeCursor = textContent.substring(0, startOffset);
        const afterCursor = textContent.substring(startOffset);

        // 检查光标前后是否有{}
        let newBeforeCursor = beforeCursor;
        let newAfterCursor = afterCursor;
        let hasReplacement = false;

        // 检查光标前是否有{}
        if (beforeCursor.endsWith("{}")) {
          newBeforeCursor = beforeCursor.slice(0, -2);
          hasReplacement = true;
        }

        // 检查光标后是否有{}
        if (afterCursor.startsWith("{}")) {
          newAfterCursor = afterCursor.slice(2);
          hasReplacement = true;
        }

        // 检查光标位置是否有{}
        if (beforeCursor.endsWith("{") && afterCursor.startsWith("}")) {
          newBeforeCursor = beforeCursor.slice(0, -1);
          newAfterCursor = afterCursor.slice(1);
          hasReplacement = true;
        }

        if (hasReplacement) {
          // 更新文本节点内容
          textNode.textContent = newBeforeCursor + newAfterCursor;

          // 重新设置光标位置
          const newRange = document.createRange();
          newRange.setStart(textNode, newBeforeCursor.length);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          // 保存当前光标位置，用于后续插入变量
          saveCursorPosition();

          // 打开变量选择器
          showVariableSelector.value = true;
        }
      }
    });
  }
}
function rendereditableDiv(content: string) {
  if (!content || !editableDiv.value) return;
  
  // 清空editableDiv
  editableDiv.value.innerHTML = '';
  focusEditableDiv();
  
  // 创建文档片段，用于暂存所有元素，确保顺序正确
  const fragment = document.createDocumentFragment();
  
  // 处理{{}}包裹的内容
  const regex = /\{\{([^{}]+)\}\}/g;
  let lastIndex = 0;
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    // 添加{{}}前面的文本
    if (match.index > lastIndex) {
      const textNode = document.createTextNode(content.substring(lastIndex, match.index));
      fragment.appendChild(textNode);
    }
    
    // 添加{{}}内容作为div
    const variableId = match[1].trim();
    let variableobj = orchestrationStore.getVariableById(variableId);
    // 创建变量div但不直接插入，而是添加到文档片段
    const variableDiv = createVariableDiv(variableobj?.name,variableId);
    fragment.appendChild(variableDiv);
    
    lastIndex = match.index + match[0].length;
  }
  
  // 添加剩余文本
  if (lastIndex < content.length) {
    const textNode = document.createTextNode(content.substring(lastIndex));
    fragment.appendChild(textNode);
  }
  
  // 一次性将所有元素添加到editableDiv，确保顺序正确
  editableDiv.value.appendChild(fragment);
}

// 新增：创建变量div的辅助函数
function createVariableDiv(variableName?: string,variableId?:string) {
  // 创建一个div元素
  const btn = document.createElement("div");
  btn.innerText = variableName || "变量名称"; // 使用传入的变量名或默认值
  btn.setAttribute("contenteditable", "false");
  btn.setAttribute("data-id",variableId);

  // 参考图片优化样式
  btn.style.display = "inline-block";
  btn.style.padding = "2px 14px";
  btn.style.margin = "0 4px";
  btn.style.background = "#125EFF";
  btn.style.border = "none";
  btn.style.color = "#fff";
  btn.style.borderRadius = "8px";
  btn.style.fontSize = "15px";
  btn.style.fontWeight = "500";
  btn.style.cursor = "pointer";
  btn.style.outline = "none";
  btn.style.lineHeight = "22px";
  btn.style.height = "28px";
  btn.style.boxShadow = "none";
  btn.style.transition = "background 0.2s";

  // 悬浮时略微加深
  btn.onmouseenter = () => {
    btn.style.background = "#0d47a1";
  };
  btn.onmouseleave = () => {
    btn.style.background = "#125EFF";
  };
  
  return btn;
}
function insertVariable(variableName?: string,variableId?:string) {
  const el = editableDiv.value;
  if (!el) return;
  // 使用辅助函数创建变量div
  const btn = createVariableDiv(variableName,variableId);

  // 悬浮时略微加深
  btn.onmouseenter = () => {
    btn.style.background = "#0d47a1";
  };
  btn.onmouseleave = () => {
    btn.style.background = "#125EFF";
  };

  // 如果有保存的光标位置，使用保存的位置；否则使用当前光标位置
  if (savedRange.value) {
    insertNodeAtSavedPosition(btn);
  } else {
    insertNodeAtCursor(btn);
  }
}

function insertNodeAtSavedPosition(node: Node) {
  if (!savedRange.value) return;

  // 恢复保存的光标位置
  const selection = window.getSelection();
  if (selection) {
    selection.removeAllRanges();
    selection.addRange(savedRange.value);
  }

  // 在保存的位置插入节点
  savedRange.value.insertNode(node);

  // 将光标移到插入的节点后面
  savedRange.value.setStartAfter(node);
  savedRange.value.collapse(true);

  // 更新选择
  if (selection) {
    selection.removeAllRanges();
    selection.addRange(savedRange.value);
  }

  // 清空保存的位置
  savedRange.value = null;

  // 让 contenteditable div 保持 focus
  if (editableDiv.value) editableDiv.value.focus();
  // 某些浏览器下异步 focus 更稳妥
  setTimeout(() => {
    editableDiv.value && editableDiv.value.focus();
  }, 0);
}

function insertNodeAtCursor(node: Node) {
  let sel = window.getSelection();
  if (!sel || !sel.rangeCount) return;
  let range = sel.getRangeAt(0);
  range.collapse(false);
  range.insertNode(node);
  // 关键：将光标移到按钮后面
  range.setStartAfter(node);
  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
  // 让 contenteditable div 保持 focus
  if (editableDiv.value) editableDiv.value.focus();
  // 某些浏览器下异步 focus 更稳妥
  setTimeout(() => {
    editableDiv.value && editableDiv.value.focus();
  }, 0);
}
function handleModelChange(value: string) {
  console.log(value);
  let model = modelOptions.value.find((item: any) => {
    return item.value === value;
  });
  formData.value.config.modelConfig.modelName = model.label;
}
function handleInputKeyTypeChange(value: string) {
  console.log(value);
  formData.value.config.inputValue = "";
  if (value == "1") {
    formData.value.config.inputKey = generateId();
  } else {
    formData.value.config.inputKey = "";
  }
}
function updatemodelParameterfun(value: string) {
  modelParameterformData.value.temperature = value;
}
function handleChangeStart(value: boolean, type: string) {
  let multiwheelVariable = [
    { name: "历史对话信息", type: "array" },
    { name: "会话ID", type: "string" },
    { name: "对话轮次", type: "number" },
  ];
  let uploadVariable = [{ name: "当前对话文件", type: "array" }];
  if (value) {
    if (type === "0") {
      multiwheelVariable.forEach((item: any) => {
        orchestrationStore.addVariable({
          id: generateId(),
          name: item.name,
          type: "nodeVariable",
          valueType: item.type,
          value: "",
          readonly: true,
          nodeId: props.node.id,
          nodeName: props.node.data.label,
          nodeType: props.node.type,
        });
      });
    } else {
      uploadVariable.forEach((item: any) => {
        orchestrationStore.addVariable({
          id: generateId(),
          name: item.name,
          type: "nodeVariable",
          valueType: item.type,
          value: "",
          readonly: true,
          nodeId: props.node.id,
          nodeName: props.node.data.label,
          nodeType: props.node.type,
        });
      });
    }
  } else {
    if (type === "0") {
      orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => variable.nodeType === "start")
        .forEach((item: any) => {
          if (
            item.name === "历史对话信息" ||
            item.name === "会话ID" ||
            item.name === "对话轮次"
          ) {
            orchestrationStore.deleteVariable(item.id);
          }
        });
    } else {
      orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => variable.nodeType === "start")
        .forEach((item: any) => {
          if (item.name === "当前对话文件") {
            orchestrationStore.deleteVariable(item.id);
          }
        });
    }
  }
}
</script>

<style scoped lang="less">
.config-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.config-panel {
  position: fixed;
  top: 0;
  right: -500px;
  width: 540px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  width: 540px;
  padding: 0 16px;
}

.config-panel.panel-open {
  right: 0;
}

.panel-header {
  /* padding: 20px 24px 16px; */
  /* border-bottom: 1px solid #e2e8f0; */
  /* background: #fafbfc; */
  height: 40px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.node-info {
  flex: 1;
  display: flex;
  align-items: center;
}
.nodeIcon {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}
.node-name {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  img {
    margin-right: 11px;
  }
}
.node-type-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.node-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-idle {
  background: #d9d9d9;
}
.status-running {
  background: #faad14;
  animation: pulse 2s infinite;
}
.status-success {
  background: #52c41a;
}
.status-error {
  background: #ff4d4f;
}

.run-btn,
.more-btn,
.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.run-btn:hover,
.more-btn:hover,
.close-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.run-btn {
  color: #125eff;
}

.run-btn:hover {
  background: #e6f0ff;
  color: #0d47a1;
}

.run-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.run-btn.running {
  color: #faad14;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.panel-content {
  flex: 1;
  /* padding: 24px; */
  overflow: hidden;
}

.config-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  display: flex;
  align-items: center;
}

.panel-footer {
  padding: 20px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafbfc;
  flex-shrink: 0;
  border-radius: 0 0 0 12px; // 左下角圆角
  margin: 0 -16px; // 抵消父容器的padding
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  :deep(.n-button) {
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    min-height: 36px;

    // 取消按钮样式
    &:not([type]) {
      min-width: 88px;
      border: 1px solid #e0e0e0;
      background: #ffffff;
      color: #666666;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #125eff;
        color: #125eff;
        box-shadow: 0 2px 6px rgba(18, 94, 255, 0.15);
      }

      &:active {
        transform: translateY(1px);
      }
    }

    // 保存按钮样式
    &[type="info"] {
      min-width: 108px;
      box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

/* 执行日志样式 */
.log-count-badge {
  background: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  margin-left: 8px;
}

.clear-logs-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-logs-btn:hover {
  background: #f1f5f9;
  color: #ff4d4f;
}

.execution-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-entry {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.log-entry:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-entry.success {
  border-left: 3px solid #52c41a;
}

.log-entry.error {
  border-left: 3px solid #ff4d4f;
}

.log-entry.running {
  border-left: 3px solid #faad14;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-dot.success {
  background: #52c41a;
}

.status-dot.error {
  background: #ff4d4f;
}

.status-dot.running {
  background: #faad14;
}

.status-text {
  font-weight: 500;
  font-size: 12px;
}

.log-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #64748b;
}

.log-details {
  margin-top: 8px;
}

.log-output,
.log-error {
  margin-bottom: 6px;
}

.detail-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
}

.detail-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 11px;
  font-family: "Courier New", monospace;
  word-break: break-all;
  max-height: 80px;
  overflow-y: auto;
}

.log-error .detail-content {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.error-text {
  color: #dc2626;
}

.more-logs {
  text-align: center;
  color: #64748b;
  font-size: 12px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  margin-top: 8px;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
.rowstit {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 22px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #000000e0;
  width: 100%;
  .rowicon {
    width: 5px;
    height: 16px;
    background: #abc6ff;
    background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
    border-radius: 3px;
    margin-right: 9px;
  }
  img {
    width: 16px;
    height: 16px;
    margin-left: 8px;
  }
}
/deep/ .n-data-table-thead {
  background-color: #ffffff;
  .n-data-table-th {
    background-color: #ffffff;
    .n-data-table-th__title {
      color: #bebebe;
    }
  }
}
.knowledgelist {
  align-items: center;
  min-height: 38px;
  justify-content: space-between;
  padding-top: 10px;
  padding-right: 16px;
  padding-bottom: 10px;
  padding-left: 16px;
  border-radius: 8px;
  background-color: #f5f5f5;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #3b3b3b;
  margin-bottom: 8px;
}

.divider {
  width: 100%;
  height: 0.92px;
  background: #0000000f;
  margin-bottom: 20px;
}
/deep/ .n-form-item-feedback-wrapper {
  display: none;
}
/deep/ .n-form-item.n-form-item--left-labelled .n-form-item-label {
  height: 100%;
}
.rowtitle {
  margin-bottom: 9px;
}
/deep/ .n-data-table .n-data-table-th {
  border: 0px;
}
/deep/
  .n-data-table.n-data-table--bottom-bordered
  .n-data-table-td.n-data-table-td--last-row {
  border: 0px;
}
/deep/ .n-data-table-thead .n-data-table-th .n-data-table-th__title {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #c7c7c7;
}
/deep/ .n-data-table-td {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #565756;
  letter-spacing: 0;
}
.histit {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #565756;
  letter-spacing: 0;
}
/deep/ .n-radio-group {
  width: 100%;
}
.btnparent {
  /deep/ .n-button {
    width: 100%;
  }
}
/deep/ .setinputbg {
  background-color: #f5f5f5;
  .n-input__border {
    border: 0px;
  }
}
.modelParametercard {
  /deep/ .n-card__content {
    padding: 0px;
  }
  /deep/ .n-card__footer {
    padding: 0px;
  }
}

/* 条件节点配置样式 - 严格按照UI设计图 */
.condition-section {
  margin-bottom: 16px;
}

.condition-item-wrapper {
  margin-bottom: 12px;
  background: #f5f5f6;
  border-radius: 8px;
  padding: 14px 12px;
  transition: all 0.2s ease;
}
.condition-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.condition-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.condition-branch-label {
  background: #125eff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 400;
  line-height: 1;
  margin-left: 8px;
  flex-shrink: 0;
}

.condition-delete-btn {
  width: 16px;
  height: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.condition-delete-btn:hover {
  opacity: 1;
}

.condition-config-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.condition-select-group {
  flex: 0 0 120px;
}

.condition-input-group {
  flex: 1;
}

.condition-actions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.action-icon:hover {
  opacity: 1;
}

.condition-field-select,
.condition-operator-select,
.condition-value-input {
  width: 100%;
}

.add-condition-wrapper,
.add-else-branch-wrapper {
  margin-top: 12px;
  width: 100%;
}

.add-condition-button,
.add-else-branch-button {
  width: 100%;
  height: 32px;
  border: 1px dashed #d9d9d9;
  background: transparent;
  color: #666;
  font-size: 14px;
}

.add-condition-button:hover,
.add-else-branch-button:hover {
  border-color: #125eff;
}

.add-else-branch-button {
  width: 104px;
  height: 33px;
  background: #125eff;
  border-radius: 7px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 13px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  border: 0;
}

/* 花括号连接器样式 - 严格按照UI设计图 */
.condition-group {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.condition-bracket-container {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  height: 100%;
  // min-height: 120px;
  position: relative;
  padding-top: 58px;
}

.condition-bracket {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 60px;
}

.bracket-top,
.bracket-bottom {
  width: 2px;
  background-color: #d9d9d9;
  flex: 1;
  min-height: 20px;
}

.bracket-top {
  border-top-left-radius: 8px;
  border-left: 1px solid #d3d7df;
  border-top: 1px solid #d3d7df;
  background: transparent;
  width: 28px;
  height: 20px;
  margin-bottom: 8px;
}

.bracket-bottom {
  border-bottom-left-radius: 8px;
  border-left: 1px solid #d3d7df;
  border-bottom: 1px solid #d3d7df;
  background: transparent;
  width: 28px;
  height: 20px;
  margin-top: 8px;
}
.bracket-middle {
  /deep/ .n-base-selection-input__content {
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    font-size: 15px;
    color: #125eff;
    letter-spacing: 0;
  }
}
.bracket-middle {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8px 0;
  width: 58px;
}

// .group-logic-selector {
//   width: 70px !important;
//   min-width: 70px;
// }

.group-logic-selector .n-base-selection {
  background: #f5f5f6 !important;
  border: 1px solid #e5e5e5 !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  text-align: center;
  padding: 0 8px !important;
}

.group-logic-selector .n-base-selection .n-base-selection-label {
  justify-content: center;
  font-weight: 500;
  color: #333 !important;
  padding: 0 !important;
}

.group-logic-selector:hover .n-base-selection {
  border-color: #125eff !important;
  background: #f0f7ff !important;
}

.condition-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ .n-select {
    .n-base-selection-label {
      background: #ffffff !important;
    }
  }
  /deep/ .selector-trigger {
    background: #ffffff;
  }
}

/* 逻辑连接符样式 */
.condition-logic-connector {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.logic-operator-select {
  width: 80px;
  text-align: center;
}

/* 变量选择器弹窗样式 */
.variable-selector-content {
  .selector-description {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    font-size: 14px;
    color: #6b7280;

    code {
      background: #e5e7eb;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      color: #374151;
    }
  }

  .insert-preview {
    margin-top: 16px;
    padding: 12px;
    background: #f0f9ff;
    border-radius: 6px;
    border-left: 3px solid #0ea5e9;

    .preview-title {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 6px;
    }

    .preview-content {
      code {
        background: #e0f2fe;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        color: #0c4a6e;
        font-weight: 500;
      }
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.editable-div {
  min-height: 130px;
  width: 100%;
  background: #f5f5f6;
  border-radius: 8px;
  padding: 12px;
  outline: none;
  position: relative;
}

.add-variable-btn {
  position: absolute;
  right: 16px;
  bottom: 16px;
  z-index: 2;
  background: #125eff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 14px;
}
/deep/ .n-form-item {
  .n-form-item-label {
    height: 22px;
    min-height: 22px;
    padding-bottom: 0px;
  }
  .n-form-item-blank {
    height: 38px;
    .n-input {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      .n-input__border {
        display: none;
      }
    }
    .n-input__input-el {
      height: 100%;
      background: #f5f5f6;
    }
    .n-input-number {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;
    }
  }
}
/deep/ .n-select {
  height: 100%;
  background: #f5f5f6;
  border-radius: 8px;

  .n-base-selection__border {
    display: none;
  }
  .n-base-selection {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;

    .n-base-selection-label {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px !important;
    }
  }
}
.setrowbottom {
  /deep/ .n-form-item-label {
    margin-bottom: 9px;
  }
}
.setHeight {
  // height: 22px;
  /deep/ .n-form-item-label {
    height: 22px !important;
    min-height: 22px;
  }
  /deep/ .n-form-item-blank {
    height: 22px;
    min-height: 22px;
  }
}
/deep/.n-data-table-tr {
  height: 22px;
  min-height: 22px;
  // padding-bottom: 16px;
  .n-data-table-th {
    height: 30px;
    padding: 0px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #c7c7c7;
  }
  .n-data-table-td {
    height: 38px;
    padding: 0px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #565756;
    letter-spacing: 0;
  }
}
.seteditable {
  grid-template-rows: 1fr 130px;
  /deep/ .n-form-item-blank {
    height: 130px;
  }
}
.outputrow {
  .n-input {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;
    /deep/ .n-input__border {
      display: none;
    }
  }
  .n-input__input-el {
    height: 100%;
    border: 0;
  }
  .n-input-number {
    height: 100%;
    border: 0;
  }
}
</style>
