{"id": "1752222023619-8sktsgs28", "name": "情感分析工作流", "description": "基于Dify工作流转换的情感分析流程，支持单情感和多情感分析", "nodes": [{"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "description": "情感分析工作流的起始点", "status": "idle", "config": {"variables": [{"name": "input_text", "type": "string", "description": "待分析的文本内容", "required": true, "defaultValue": ""}, {"name": "Multisentiment", "type": "string", "description": "是否进行多情感分析", "required": true, "defaultValue": "False", "options": ["True", "False"]}, {"name": "Categories", "type": "string", "description": "情感分类类别", "required": false, "defaultValue": "正面,负面,中性"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "llm-single-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 699.1642714592357, "y": 98.32854291847144, "z": 0}, "handleBounds": {"source": [{"id": "llm-single-sentiment-output", "type": "source", "nodeId": "llm-single-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-single-sentiment-input", "type": "target", "nodeId": "llm-single-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 699.1642714592357, "y": 98.32854291847144}, "data": {"label": "LLM节点", "description": "执行单情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本的情感倾向，并给出一个主要的情感标签。\n\n文本：{{input_text}}\n\n请从以下类别中选择最符合的情感：{{Categories}}\n\n请直接回答情感标签，不需要解释。", "systemPrompt": "你是一个专业的情感分析助手，能够准确识别文本的情感倾向。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "llm-multi-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 700, "y": 300, "z": 0}, "handleBounds": {"source": [{"id": "llm-multi-sentiment-output", "type": "source", "nodeId": "llm-multi-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-multi-sentiment-input", "type": "target", "nodeId": "llm-multi-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 700, "y": 300}, "data": {"label": "LLM节点", "description": "执行多情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本可能包含的多种情感，并给出每种情感的强度评分（0-1之间）。\n\n文本：{{input_text}}\n\n请从以下类别中分析：{{Categories}}\n\n请以JSON格式返回结果，例如：\n{\n  \"正面\": 0.8,\n  \"负面\": 0.2,\n  \"中性\": 0.1\n}", "systemPrompt": "你是一个专业的情感分析助手，能够识别文本中的多种情感并给出强度评分。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "end-single", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 986.6283433477715, "y": 120.0574849783427, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-single-input-0", "type": "target", "nodeId": "end-single", "position": "left", "x": -2.0000572278552684, "y": 25.50001553117635, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 986.6283433477715, "y": 120.0574849783427}, "data": {"label": "结束节点", "description": "输出单情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_result", "type": "string", "source": "llm-single-sentiment.output", "description": "单情感分析结果"}, {"name": "analysis_type", "type": "string", "value": "single", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, {"id": "end-multi", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 993.3141716738858, "y": 319.22175643757845, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-multi-input-0", "type": "target", "nodeId": "end-multi", "position": "left", "x": -2.0000572278552684, "y": 25.50004103560415, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 993.3141716738858, "y": 319.22175643757845}, "data": {"label": "结束节点", "description": "输出多情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_scores", "type": "object", "source": "llm-multi-sentiment.output", "description": "多情感分析评分结果"}, {"name": "analysis_type", "type": "string", "value": "multi", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}], "edges": [{"id": "1752222047366-pa1bdf8g9", "type": "default", "source": "start-node", "target": "condition-node", "sourceHandle": "start-node-output", "targetHandle": "condition-node-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "description": "情感分析工作流的起始点", "status": "idle", "config": {"variables": [{"name": "input_text", "type": "string", "description": "待分析的文本内容", "required": true, "defaultValue": ""}, {"name": "Multisentiment", "type": "string", "description": "是否进行多情感分析", "required": true, "defaultValue": "False", "options": ["True", "False"]}, {"name": "Categories", "type": "string", "description": "情感分类类别", "required": false, "defaultValue": "正面,负面,中性"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "targetNode": {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 230.00007330584654, "sourceY": 229.49999002674855, "targetX": 396.50721797414946, "targetY": 236.07974904580254}, {"id": "1752222050385-38gwo2b1p", "type": "default", "source": "condition-node", "target": "llm-single-sentiment", "sourceHandle": "condition-node-true", "targetHandle": "llm-single-sentiment-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "llm-single-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 699.1642714592357, "y": 98.32854291847144, "z": 0}, "handleBounds": {"source": [{"id": "llm-single-sentiment-output", "type": "source", "nodeId": "llm-single-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-single-sentiment-input", "type": "target", "nodeId": "llm-single-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 699.1642714592357, "y": 98.32854291847144}, "data": {"label": "LLM节点", "description": "执行单情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本的情感倾向，并给出一个主要的情感标签。\n\n文本：{{input_text}}\n\n请从以下类别中选择最符合的情感：{{Categories}}\n\n请直接回答情感标签，不需要解释。", "systemPrompt": "你是一个专业的情感分析助手，能够准确识别文本的情感倾向。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 567.5072850283507, "sourceY": 250.37663550443187, "targetX": 693.164201793381, "targetY": 155.2192172134295}, {"id": "1752222053068-9xpq23p2d", "type": "default", "source": "condition-node", "target": "llm-multi-sentiment", "sourceHandle": "condition-node-false", "targetHandle": "llm-multi-sentiment-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "llm-multi-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 700, "y": 300, "z": 0}, "handleBounds": {"source": [{"id": "llm-multi-sentiment-output", "type": "source", "nodeId": "llm-multi-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-multi-sentiment-input", "type": "target", "nodeId": "llm-multi-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 700, "y": 300}, "data": {"label": "LLM节点", "description": "执行多情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本可能包含的多种情感，并给出每种情感的强度评分（0-1之间）。\n\n文本：{{input_text}}\n\n请从以下类别中分析：{{Categories}}\n\n请以JSON格式返回结果，例如：\n{\n  \"正面\": 0.8,\n  \"负面\": 0.2,\n  \"中性\": 0.1\n}", "systemPrompt": "你是一个专业的情感分析助手，能够识别文本中的多种情感并给出强度评分。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 567.5072850283507, "sourceY": 281.9703984647619, "targetX": 693.9999303341453, "targetY": 356.89067429495805}, {"id": "1752222062362-73xfl9iby", "type": "default", "source": "llm-single-sentiment", "target": "end-single", "sourceHandle": "llm-single-sentiment-output", "targetHandle": "end-single-input-0", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "llm-single-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 699.1642714592357, "y": 98.32854291847144, "z": 0}, "handleBounds": {"source": [{"id": "llm-single-sentiment-output", "type": "source", "nodeId": "llm-single-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-single-sentiment-input", "type": "target", "nodeId": "llm-single-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 699.1642714592357, "y": 98.32854291847144}, "data": {"label": "LLM节点", "description": "执行单情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本的情感倾向，并给出一个主要的情感标签。\n\n文本：{{input_text}}\n\n请从以下类别中选择最符合的情感：{{Categories}}\n\n请直接回答情感标签，不需要解释。", "systemPrompt": "你是一个专业的情感分析助手，能够准确识别文本的情感倾向。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "end-single", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 986.6283433477715, "y": 120.0574849783427, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-single-input-0", "type": "target", "nodeId": "end-single", "position": "left", "x": -2.0000572278552684, "y": 25.50001553117635, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 986.6283433477715, "y": 120.0574849783427}, "data": {"label": "结束节点", "description": "输出单情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_result", "type": "string", "source": "llm-single-sentiment.output", "description": "单情感分析结果"}, {"name": "analysis_type", "type": "string", "value": "single", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "sourceX": 869.164316118509, "sourceY": 155.2192172134295, "targetX": 985.4640146606805, "targetY": 157.91478591716185}, {"id": "1752222065095-8pqqjnfda", "type": "default", "source": "llm-multi-sentiment", "target": "end-multi", "sourceHandle": "llm-multi-sentiment-output", "targetHandle": "end-multi-input-0", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "llm-multi-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 700, "y": 300, "z": 0}, "handleBounds": {"source": [{"id": "llm-multi-sentiment-output", "type": "source", "nodeId": "llm-multi-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-multi-sentiment-input", "type": "target", "nodeId": "llm-multi-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 700, "y": 300}, "data": {"label": "LLM节点", "description": "执行多情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本可能包含的多种情感，并给出每种情感的强度评分（0-1之间）。\n\n文本：{{input_text}}\n\n请从以下类别中分析：{{Categories}}\n\n请以JSON格式返回结果，例如：\n{\n  \"正面\": 0.8,\n  \"负面\": 0.2,\n  \"中性\": 0.1\n}", "systemPrompt": "你是一个专业的情感分析助手，能够识别文本中的多种情感并给出强度评分。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "end-multi", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 993.3141716738858, "y": 319.22175643757845, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-multi-input-0", "type": "target", "nodeId": "end-multi", "position": "left", "x": -2.0000572278552684, "y": 25.50004103560415, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 993.3141716738858, "y": 319.22175643757845}, "data": {"label": "结束节点", "description": "输出多情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_scores", "type": "object", "source": "llm-multi-sentiment.output", "description": "多情感分析评分结果"}, {"name": "analysis_type", "type": "string", "value": "multi", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "sourceX": 870.0000446592733, "sourceY": 356.89067429495805, "targetX": 1000.5071283944376, "targetY": 358.75053996235397}], "createdAt": "2025-07-11T08:20:23.619Z", "updatedAt": "2025-07-11T08:24:08.807Z", "metadata": {"originalSource": "dify-workflow", "version": "1.0.0", "convertedAt": "2024-01-15T10:00:00.000Z", "description": "从Dify工作流YAML转换而来的情感分析流程", "tags": ["情感分析", "条件分支", "LLM", "多路输出"]}}