<template>
  <div class="question-classifier-node-config">
    <!-- 模型选择 -->
    <n-form-item path="config.modelConfig.model" class="setrowbottom">
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 模型选择
        </div>
      </template>
      <n-select
        v-model:value="formData.config.modelConfig.model"
        :options="modelOptions"
        placeholder="请选择模型"
        filterable
        @update:value="handleModelChange"
      />
      <img
        @click="showModelParameterDialog"
        class="w-[20px] h-[20px] ml-[10px] cursor-pointer"
        src="@/assets/agentOrchestration/yitupeizhi.png"
      />
    </n-form-item>

    <!-- 输入配置 -->
    <n-form-item
      label-placement="left"
      class="setHeight mt-[21px] mb-[13px]"
    >
      <template #label>
        <div class="rowstit"><span class="rowicon"></span> 输入</div>
      </template>
    </n-form-item>

    <n-form-item label-placement="left" class="setHeight mb-[6px]">
      <template #label>
        <div class="text-[#565756]">用户问题</div>
      </template>
      <div class="flex justify-end w-full text-[#C7C7C7]">文本</div>
    </n-form-item>

    <!-- 输入值配置 -->
    <n-form-item path="config.inputValue" label-placement="left">
      <div class="flex items-center w-full h-full">
        <div class="w-[162px] mr-[14px] h-full">
          <n-select
            v-model:value="formData.config.inputType"
            :options="variableOptions"
            @update:value="handleInputKeyTypeChange"
          />
        </div>
        <div class="w-full h-full">
          <n-input
            v-if="formData.config.inputType == '1'"
            v-model:value="formData.config.inputValue"
            type="text"
            placeholder="请输入"
            maxlength="20"
            show-count
          />
          <div class="h-full" v-else>
            <AggregationSelector
              v-model="formData.config.inputValue"
              :options="aggregationOptions"
              placeholder="请选择变量"
              @change="handleAggregationChange"
            />
          </div>
        </div>
      </div>
    </n-form-item>

    <!-- 问题意图配置 -->
    <n-form-item
      label-placement="left"
      class="setHeight mt-[24px] mb-[9px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 问题意图
        </div>
      </template>
      <div class="flex justify-end w-full">
        <img
          @click="addCategories"
          class="w-[16px] h-[16px] cursor-pointer"
          src="@/assets/agentOrchestration/yituadd.png"
        />
      </div>
    </n-form-item>

    <!-- 问题意图列表 -->
    <div
      class="knowledgelist h-[85px] py-[14px] px-[19px] flex items-center"
      v-for="(item, index) in formData.config.classes"
      :key="index"
    >
      <div class="w-[400px]">
        <!-- 编辑状态的输入框 -->
        <n-input
          class="setinputbg"
          @blur="changeEditStatus(index, false)"
          autofocus
          v-if="item.isEditing"
          v-model:value="item.name"
          placeholder="请输入"
        />
        
        <!-- 显示状态的文本 -->
        <p
          v-show="!item.isEditing"
          class="h-[22px] mb-[14px] text-[#000000] leading-normal flex items-center text-[16px]"
        >
          {{ item.name }}
          <img
            v-if="!item.disable"
            @click="changeEditStatus(index, true)"
            class="ml-[10px] w-[14px] h-[14px] cursor-pointer"
            src="@/assets/agentOrchestration/editIcon.png"
          />
        </p>

        <!-- 描述输入框 -->
        <n-input
          class="setinputbg"
          v-if="!item.disable"
          v-model:value="item.des"
          placeholder="请输入关于此意图的描述"
        />
        <p v-else class="text-[#ADB3BB]">{{ item.des }}</p>
      </div>
      
      <!-- 删除按钮 -->
      <img
        v-if="!item.disable"
        @click="deleteCategories(index)"
        class="w-[16px] h-[16px] cursor-pointer"
        src="@/assets/agentOrchestration/delIcon2.png"
      />
    </div>

    <!-- 输出配置 -->
    <n-form-item label-placement="left" class="setHeight mt-[24px]">
      <template #label>
        <div class="rowstit"><span class="rowicon"></span>输出</div>
      </template>
    </n-form-item>

    <n-form-item label-placement="left" class="setHeight mt-[6px]">
      <div class="w-full flex text-[#C7C7C7]">
        <div class="w-[50%]">变量名称</div>
        <div>数据类型</div>
      </div>
    </n-form-item>

    <n-form-item label-placement="left" class="setHeight mt-[14px]">
      <div class="w-full flex text-[#565756] items-center">
        <div class="w-[50%]">匹配结果</div>
        <div>文本</div>
      </div>
    </n-form-item>

    <!-- 模型参数配置弹窗 -->
    <n-modal v-model:show="modelParameterShow">
      <n-card
        class="modelParametercard"
        style="width: 644px"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div class="flex items-center justify-between h-[70px] text-[17px] font-medium text-[#1F1F1F] pl-[26px] pr-[23px] border-b-[1px] border-[#E5E5E5]">
          <span>模型参数</span>
          <span @click="modelParameterShow = false" class="cursor-pointer">×</span>
        </div>
        
        <n-form ref="modelParameterformRef" :model="modelParameterformData">
          <!-- 模型风格 -->
          <n-form-item path="style">
            <template #label>
              <div class="h-[21px] text-[#1F1F1F] text-[15px] mt-[28px] mb-[7px] pl-[26px] pr-[23px] font-medium">
                模型风格
              </div>
            </template>
            <n-radio-group
              v-model:value="modelParameterformData.style"
              name="style"
              @update:value="updateModelParameter"
            >
              <div class="w-[592px] h-[38px] bg-[#F9FAFC] mx-auto rounded-lg pl-[18px] pr-[18px] flex items-center justify-between">
                <div v-for="(item, index) in modelParameterOptions" :key="index">
                  <n-radio :key="item.value" :value="item.value">
                    {{ item.label }}
                  </n-radio>
                </div>
              </div>
            </n-radio-group>
          </n-form-item>

          <!-- 模型温度 -->
          <n-form-item path="temperature" label-placement="left" class="mt-[26px]">
            <template #label>
              <div class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center">
                模型温度
              </div>
            </template>
            <div class="w-full flex justify-end pr-[23px]">
              <div class="w-[112px] h-[38px]">
                <n-input v-model:value="modelParameterformData.temperature" />
              </div>
            </div>
          </n-form-item>

          <!-- Top P -->
          <n-form-item path="topP" label-placement="left" class="mt-[24px]">
            <template #label>
              <div class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center">
                Top P
              </div>
            </template>
            <div class="w-full flex justify-end pr-[23px]">
              <div class="w-[112px] h-[38px]">
                <n-input v-model:value="modelParameterformData.topP" />
              </div>
            </div>
          </n-form-item>

          <!-- 最大输出长度 -->
          <n-form-item path="maxTokens" label-placement="left" class="mt-[24px]">
            <template #label>
              <div class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center">
                最大输出长度
              </div>
            </template>
            <div class="w-full flex justify-end pr-[23px]">
              <div class="w-[112px] h-[38px]">
                <n-input v-model:value="modelParameterformData.maxTokens" />
              </div>
            </div>
          </n-form-item>
        </n-form>

        <template #footer>
          <div class="flex w-full justify-end pl-[26px] pr-[23px] pb-[27px] mt-[47px]">
            <div class="btnparent w-[80px] h-[36px]">
              <n-button @click="modelParameterShow = false">取消</n-button>
            </div>
            <div class="btnparent w-[80px] h-[36px] ml-[16px]">
              <n-button @click="saveModelParameter" type="info" color="#125EFF">
                保存
              </n-button>
            </div>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { watch, nextTick, onMounted } from 'vue';
import { 
  NFormItem, 
  NSelect, 
  NInput, 
  NModal, 
  NCard, 
  NForm,
  NRadioGroup,
  NRadio,
  NButton 
} from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import { useNodeConfig } from './composables/useNodeConfig';
import { useModelConfig } from './composables/useModelConfig';
import { generateId } from '@/store/modules/orchestration';
import type { NodeConfigProps, NodeConfigEvents, QuestionCategory } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const { 
  variableOptions,
  aggregationOptions, 
  updateAggregationOptions, 
  handleAggregationChange 
} = useNodeConfig(props);

const {
  modelOptions,
  modelParameterOptions,
  modelParameterShow,
  modelParameterformData,
  modelParameterformRef,
  handleModelChange: baseHandleModelChange,
  changemodelParameterShow,
  changemodelParameterfun,
  updatemodelParameterfun,
  initializeModelConfig,
  initModelData,
} = useModelConfig();

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'question-classifier') {
      const config = newNode.data.config;
      
      // 初始化模型配置
      initializeModelConfig(config);
      
      // 初始化输入类型
      if (!config.inputType) {
        config.inputType = "0";
      }
      
      // 初始化问题分类
      if (!config.classes) {
        config.classes = [
          {
            id: generateId() + "-output",
            name: "意图",
            des: "",
            isEditing: false,
          },
          {
            id: generateId() + "-output",
            name: "其他",
            des: "未命中以上意图",
            disable: true,
          },
        ];
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听显示状态，更新聚合选项
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.node) {
      updateAggregationOptions();
    }
  },
  { immediate: true }
);

// 处理模型变更
const handleModelChange = (value: string) => {
  baseHandleModelChange(value, props.formData);
};

// 处理输入类型变更
const handleInputKeyTypeChange = (value: string) => {
  props.formData.config.inputValue = "";
  if (value == "1") {
    props.formData.config.inputKey = generateId();
  } else {
    props.formData.config.inputKey = "";
  }
};

// 显示模型参数对话框
const showModelParameterDialog = () => {
  changemodelParameterShow(true, props.formData);
};

// 保存模型参数
const saveModelParameter = async () => {
  await changemodelParameterfun(props.formData);
};

// 更新模型参数
const updateModelParameter = (value: string) => {
  updatemodelParameterfun(value);
};

// 添加分类
const addCategories = () => {
  props.formData.config.classes.unshift({
    id: generateId() + "-output",
    name: "意图",
    des: "",
    isEditing: false,
  });
};

// 删除分类
const deleteCategories = (index: number) => {
  props.formData.config.classes.splice(index, 1);
};

// 改变编辑状态
const changeEditStatus = (index: number, status: boolean) => {
  props.formData.config.classes[index].isEditing = status;
  if (status) {
    nextTick(() => {
      // 可以在这里添加聚焦逻辑
    });
  }
};

// 组件挂载时初始化
onMounted(() => {
  initModelData();
  if (props.visible && props.node) {
    updateAggregationOptions();
  }
});
</script>

<style scoped lang="less">
.question-classifier-node-config {
  .rowstit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
    width: 100%;

    .rowicon {
      width: 5px;
      height: 16px;
      background: #abc6ff;
      background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
      border-radius: 3px;
      margin-right: 9px;
    }
  }

  .setrowbottom {
    :deep(.n-form-item-label) {
      margin-bottom: 9px;
    }
  }

  .setHeight {
    :deep(.n-form-item-label) {
      height: 22px !important;
      min-height: 22px;
    }

    :deep(.n-form-item-blank) {
      height: 22px;
      min-height: 22px;
    }
  }

  .knowledgelist {
    align-items: center;
    min-height: 38px;
    justify-content: space-between;
    padding-top: 10px;
    padding-right: 16px;
    padding-bottom: 10px;
    padding-left: 16px;
    border-radius: 8px;
    background-color: #f5f5f5;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 13px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #3b3b3b;
    margin-bottom: 8px;
  }

  .setinputbg {
    background-color: #f5f5f5;

    :deep(.n-input__border) {
      border: 0px;
    }
  }

  .modelParametercard {
    :deep(.n-card__content) {
      padding: 0px;
    }

    :deep(.n-card__footer) {
      padding: 0px;
    }
  }

  .btnparent {
    :deep(.n-button) {
      width: 100%;
    }
  }
}
</style>
