<script setup lang='ts'>
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { NAutoComplete, NButton, NInput, NSpin, useDialog, useMessage } from 'naive-ui'
import { toPng } from 'html-to-image'
import { Message } from './components'
import { useScroll } from './hooks/useScroll'
import { useChat } from './hooks/useChat'
import { useUsingContext } from './hooks/useUsingContext'
import HeaderComponent from './components/Header/index.vue'
// import audioView from '@/views/chat/components/audio/index.vue'
import Recorder from './recorder.js'
import AgentExecutionPreviewModal from '@/components/common/AgentExecutionPreviewModal/index.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useChatStore, usePromptStore, useToolsStore } from '@/store'
import { fetchChatAPIProcess, fetchWeaknessAnalysisProcess, recognition_stop, voiceToText } from '@/api'
import { t } from '@/locales'
/*******************************************************/
import { debounce } from '@/utils/functions/debounce'
// import { addHistory } from '@/api/sider'
import { addHistory } from '@/api/index'
import { infoStore } from '@/store/modules/info'
import icon1 from '@/assets/teachPlanGen/step1.png'
import icon2 from '@/assets/teachPlanGen/step2.png'
import icon3 from '@/assets/teachPlanGen/step3.png'
import icon4 from '@/assets/teachPlanGen/step4.png'

const emit = defineEmits(['loadTap'])

let controller = new AbortController()

const openLongReply = import.meta.env.VITE_GLOB_OPEN_LONG_REPLY === 'true'

const route = useRoute()
const router = useRouter()
const dialog = useDialog()
const ms = useMessage()
const info = infoStore()
const chatStore = useChatStore()
const ToolsStore = useToolsStore()

const { isMobile } = useBasicLayout()
const { addChat, updateChat, updateChatSome, getChatByUuidAndIndex } = useChat()
const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll()
const { usingContext, toggleUsingContext } = useUsingContext()

let uuid = route.params.uuid as string || ''
console.log(uuid)

// watch(() =>
//   router.currentRoute.value.path,
// (toPath) => {
//   // 要执行的方法
//   if (!route.params.uuid) {
//     uuid = route.params.uuid
//     console.log(1)
//   }
// }, { immediate: true, deep: true })

const dataSources = computed(() => chatStore.getChatByUuid(uuid))

const conversationList = computed(() => dataSources.value.filter(item => (!item.inversion && !!item.conversationOptions)))

const prompt = ref < string > ('')
const loading = ref < boolean > (false)
const inputRef = ref < any | null > (null)
const netFlag = ref < boolean > (false)
// 添加PromptStore
const promptStore = usePromptStore()

// 使用storeToRefs，保证store修改后，联想部分能够重新渲染
const { promptList: promptTemplate } = storeToRefs < any > (promptStore)

// 未知原因刷新页面，loading 状态不会重置，手动重置
dataSources.value.forEach((item, index) => {
  if (item.loading)
    updateChatSome(uuid, index, { loading: false })
})

function handleSubmit(flag?: string) {
  onConversation(flag)
}
async function fetchWeaknessAnalysisProcessfun() {
  //  var examrequest=JSON.parse(sessionStorage.getItem('examrequest'));
  const examRequestStr = sessionStorage.getItem('examrequest')
  let examrequest = {
    conversationContentId: '',
  }
  if (examRequestStr)
    examrequest = JSON.parse(examRequestStr)

  if (loading.value)
    return

  controller = new AbortController()

  scrollToBottom()

  loading.value = true

  let options: Chat.ConversationRequest = {}
  const lastContext = conversationList.value[conversationList.value.length - 1]?.conversationOptions

  if (lastContext && usingContext.value)
    options = { ...lastContext }
  addChat(
    uuid,
    {
      dateTime: new Date().toLocaleString(),
      text: t('chat.thinking'),
      loading: true,
      answerList: [],
      endstatus: 1,
      inversion: false,
      error: false,
      conversationOptions: null,
      requestOptions: { prompt: null, options: { ...options } },
    },
  )
  scrollToBottom()

  try {
    let lastText = ''
    const fetchChatAPIOnce = async () => {
      await fetchWeaknessAnalysisProcess < Chat.fetchWeaknessResponse > ({
        ...examrequest,
        onDownloadProgress: ({ event }) => {
          const xhr = event.target
          const { responseText } = xhr
          // if (chatStore.netFlag) {
          // 深度思考
          // 按行分割响应文本
          const lines = responseText.split('\n').filter(line => line.trim() !== '')

          // 重置文本,避免重复累加
          lastText = ''
          // 处理每一行数据
          for (const line of lines) {
            const trimmedLine = line.replace(/^data: /, '').trim()
            // console.log(trimmedLine)
            // info.setloading(false)
            try {
              // const data = JSON.parse(trimmedLine)
              const data = JSON.parse(trimmedLine?.substring(5))
              // console.log(data)

              // 停止回答用
              // currectemitterId.value = data.emitterId

              info.setloading(false)
              // 直接使用当前响应文本,不进行累加
              const deltaContent = data.choices[0].delta.content || ''
              lastText += deltaContent
              console.log(data, '=========')
              updateChat(
                uuid,
                dataSources.value.length - 1,
                {
                  dateTime: new Date().toLocaleString(),
                  text: lastText, // 使用完整的lastText,不再和之前的文本拼接
                  // answerList: data.answerList,
                  inversion: false,
                  error: false,
                  loading: true,
                  conversationOptions: {
                    conversationContentId: '',
                    conversationId: '',
                    parentMessageId: '',
                  },
                  requestOptions: { prompt: '', options: { ...options } },
                },
              )

              if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
                // options.parentMessageId = data.id
                // message = ''
                return fetchChatAPIOnce()
              }

              scrollToBottomIfAtBottom()

              if (data.choices[0].finish_reason === 'stop')

                updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
            }
            catch (error) {
              updateChat(
                uuid,
                dataSources.value.length - 1,
                {
                  dateTime: new Date().toLocaleString(),
                  text: '内容中不可存在违规字符', // 使用完整的lastText,不再和之前的文本拼接
                  // answerList: data.answerList,
                  inversion: false,
                  error: false,
                  loading: true,
                  conversationOptions: {
                    conversationContentId: '',
                    conversationId: '',
                    parentMessageId: '',
                  },
                  requestOptions: { prompt: '', options: { ...options } },
                },
              )
              updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
            }
          }
        },
      })
    }

    await fetchChatAPIOnce()
  }
  catch (error: any) {
    // console.log(error)
    const errorMessage = error?.message ?? t('common.wrong')

    if (error.message === 'canceled') {
      updateChatSome(
        uuid,
        dataSources.value.length - 1,
        {
          loading: false,
        },
      )
      scrollToBottomIfAtBottom()
      return
    }

    const currentChat = getChatByUuidAndIndex(uuid, dataSources.value.length - 1)
    // console.log(currentChat)
    if (currentChat?.text && currentChat.text !== '') {

    }
    else {
      updateChat(
        uuid,
        dataSources.value.length - 1,
        {
          dateTime: new Date().toLocaleString(),
          text: errorMessage,
          answerList: [],
          endstatus: 1,
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: '', options: { ...options } },
        },
      )
    }

    scrollToBottomIfAtBottom()
  }
  finally {
    loading.value = false
  }
}
defineExpose({
  fetchWeaknessAnalysisProcessfun,
})
const link = computed(() => {
  const val = ToolsStore.ToolInfo.category

  if (val == 8)
    return '/emind/graph/teaching_plan'
  if (val == 7)
    return '/emind/graph/exam_question'

  return `/eaide${ToolsStore.ToolInfo.link}`
})
async function onConversation(val?: string) {
  // 2代表新对话
  let flag = 1
  let message = prompt.value
  if (loading.value)
    return

  if (!message || message.trim() === '')
    return
  controller = new AbortController()
  if (!uuid) {
    console.log({
      category: ToolsStore.ToolInfo.category,
      title: message || '新的对话',
    })
    flag = 2
    const { data } = await addHistory({
      category: ToolsStore.ToolInfo.category,
      title: message || '新的对话',
    })
    console.log(data)
    uuid = data.id
    chatStore.setnewAddHistory(true)
    // chatStore.active = uuid as any;
    chatStore.activeHistory(uuid)
    // chatStore.history.unshift({ uuid, thinkTankId: data.thinkTankId || "", id: data.id, title: message, isEdit: false, data: [] })
  }
  console.log(uuid, '=========uuid')

  addChat(
    uuid,
    {
      dateTime: new Date().toLocaleString(),
      text: message,
      answerList: [],
      endstatus: 1,
      inversion: true,
      error: false,
      conversationOptions: {
        conversationId: uuid,
      },
      requestOptions: { prompt: message, options: null },
    },
  )
  scrollToBottom()

  loading.value = true
  prompt.value = ''

  let options: Chat.ConversationRequest = {}
  const lastContext = conversationList.value[conversationList.value.length - 1]?.conversationOptions

  if (lastContext && usingContext.value)
    options = { ...lastContext }

  addChat(
    uuid,
    {
      dateTime: new Date().toLocaleString(),
      text: t('chat.thinking'),
      loading: true,
      answerList: [],
      endstatus: 1,
      inversion: false,
      error: false,
      conversationOptions: {
        conversationId: uuid,
      },
      requestOptions: { prompt: message, options: { ...options } },
    },
  )
  scrollToBottom()

  if (ToolsStore.ToolInfo?.category == '7') {
    info.clearToolLoding()
    info.updateToolLoding({ loding: true, current: 1 })
    // 显示进度条
    showProgressModal.value = true
    resetNodeStatuses()
    updateNodeStatus('knowledge_classifier', 'running')
  }

  const topicList = ['判断题', '填空题', '选择题']
  let topic: any = null
  try {
    let lastText = ''
    const fetchChatAPIOnce = async () => {
      await fetchChatAPIProcess < Chat.ConversationResponse > ({
        conversationId: uuid,
        prompt: message,
        // networkingStatus: chatStore.netFlag ? '1' : '0',
        // options,
        signal: controller.signal,
        url: `${link.value}`,
        capacityCode: ToolsStore.ToolInfo.capacityCode,
        onDownloadProgress: ({ event }) => {
          const xhr = event.target
          const { responseText } = xhr
          // if (chatStore.netFlag) {
          // 深度思考
          // 按行分割响应文本
          const lines = responseText.split('\n').filter(line => line.trim() !== '')
          // console.log(lines)
          // 重置文本,避免重复累加
          lastText = ''
          // 处理每一行数据
          for (const line of lines) {
            const trimmedLine = line.replace(/^data: /, '').trim()
            // console.log(trimmedLine)
            // info.setloading(false)
            try {
              // const data = JSON.parse(trimmedLine)
              const data = JSON.parse(trimmedLine?.substring(5))
              // console.log(data)

              // 提取节点内容details
              const content = data.choices?.[0]?.message?.content
              let details = null
              if (content) {
                // 将content按行分割作为details数组
                details = content.split('/n').filter(line => line.trim() !== '').slice(0, 3)
              }

              if (data.nodeName === '__START__' && !info.ToolLoding?.start) {
                info.updateToolLoding({ start: true, current: 2 })
                updateNodeStatus('knowledge_classifier', 'running')
              }
              if (data.nodeName === 'knowledge_classifier' && !info.ToolLoding?.knowledge_classifier) {
                info.updateToolLoding({ knowledge_classifier: true, current: 3 })
                updateNodeStatus('knowledge_classifier', 'completed', details)
                updateNodeStatus('code', 'running')
              }
              if (data.nodeName === 'code' && !info.ToolLoding?.code) {
                info.updateToolLoding({ code: true, current: 4 })
                updateNodeStatus('code', 'completed', details)
                updateNodeStatus('exam_classifier', 'running')
              }
              if (data.nodeName === 'exam_classifier' && !info.ToolLoding?.exam_classifier) {
                info.updateToolLoding({ exam_classifier: true, current: 5 })
                updateNodeStatus('exam_classifier', 'completed', details)
                updateNodeStatus('collector', 'running')
                // 用来判断试题智能体显示哪个按钮
                topic = topicList.some(keyword => data.choices[0]?.message?.content?.includes(keyword))
                // console.log(data.choices[0]?.message?.content, topic)
              }
              if (data.nodeName === 'collector' && !info.ToolLoding?.collector) {
                info.updateToolLoding({ collector: true, current: 6 })
                updateNodeStatus('collector', 'completed', details)
                updateNodeStatus('llm', 'running')
                showProgressModal.value = false
                handleProgressClose()
              }
              // if (data.nodeName === 'llm' && !info.ToolLoding?.llm) {
              //   info.updateToolLoding({ llm: true, current: 7 })
              //   updateNodeStatus('llm', 'completed', details)
              // }

              info.setloading(false)

              let deltaContent = ''
              if (data.nodeName === 'llmNode' || !data.nodeName)
                deltaContent = data.choices[0]?.delta?.content || data.choices[0]?.message?.content || ''
                // console.log(deltaContent)

              lastText += deltaContent

              if (lastText) {
                updateChat(
                  uuid,
                  dataSources.value.length - 1,
                  {
                    dateTime: new Date().toLocaleString(),
                    text: lastText, // 使用完整的lastText,不再和之前的文本拼接
                    // answerList: data.answerList,
                    inversion: false,
                    error: false,
                    loading: true,
                    topic: topic ? '1' : '2',
                    conversationOptions: {
                      conversationId: data.conversationContentId,
                      parentMessageId: data.id,
                    },
                    requestOptions: { prompt: message, options: { ...options } },
                  },
                )
              }

              if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
                options.parentMessageId = data.id
                message = ''
                return fetchChatAPIOnce()
              }

              scrollToBottomIfAtBottom()

              if (data.choices[0].finish_reason === 'stop' || data.nodeName === '__END__') {
                updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
                // 完成最后一个节点
                if (showProgressModal.value)
                  updateNodeStatus('llm', 'completed')
              }
              // info.updateToolLoding({ loding: false })
            }
            catch (error) {

            }
          }
          // }
          // else {
          //   // Always process the final line
          //   const lastIndex = responseText.lastIndexOf('\n', responseText.length - 3)
          //   let chunk = responseText
          //   if (lastIndex !== -1)
          //     chunk = responseText.substring(lastIndex)
          //   try {
          //     const data = JSON.parse(chunk?.substring(6))
          //     currectemitterId.value = data.emitterId
          //     info.setloading(false)
          //     updateChat(
          //       uuid,
          //       dataSources.value.length - 1,
          //       {
          //         dateTime: new Date().toLocaleString(),
          //         text: lastText + (data.answer ?? ''),
          //         answerList: data.answerList,
          //         endstatus: data.status,
          //         inversion: false,
          //         error: false,
          //         loading: true,
          //         conversationOptions: { conversationId: data.conversationId, parentMessageId: data.id },
          //         requestOptions: { prompt: message, options: { ...options } },
          //       },
          //     )
          //
          //     if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
          //       options.parentMessageId = data.id
          //       lastText = data.message
          //       message = ''
          //       return fetchChatAPIOnce()
          //     }
          //
          //     scrollToBottomIfAtBottom()
          //     if (data.status === '1')
          //       updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
          //   }
          //   catch (error) {
          //     //
          //   }
          // }
        },
      })
    }

    await fetchChatAPIOnce()
  }
  catch (error: any) {
    const errorMessage = error?.message ?? t('common.wrong')

    if (error.message === 'canceled') {
      updateChatSome(
        uuid,
        dataSources.value.length - 1,
        {
          loading: false,
        },
      )
      scrollToBottomIfAtBottom()
      return
    }

    const currentChat = getChatByUuidAndIndex(uuid, dataSources.value.length - 1)
    // console.log(currentChat)
    if (currentChat?.text && currentChat.text !== '') {
      // updateChatSome(
      //   uuid,
      //   dataSources.value.length - 1,
      //   {
      //     text: `${currentChat.text}\n[${errorMessage}]`,
      //     error: false,
      //     loading: false,
      //   },
      // )
      // return
    }
    else {
      updateChat(
        uuid,
        dataSources.value.length - 1,
        {
          dateTime: new Date().toLocaleString(),
          text: errorMessage,
          answerList: [],
          endstatus: 1,
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: message, options: { ...options } },
        },
      )
    }

    scrollToBottomIfAtBottom()
  }
  finally {
    loading.value = false
  }

  // if (flag == 2 || val == '2')
  //   chatStore.activeHistory(uuid)
}

async function onRegenerate(index: number) {
  if (loading.value)
    return

  controller = new AbortController()

  const { requestOptions } = dataSources.value[index]

  let message = requestOptions.prompt

  let options: Chat.ConversationRequest = {}

  if (requestOptions.options)
    options = { ...requestOptions.options }

  loading.value = true

  updateChat(
    uuid,
    index,
    {
      dateTime: new Date().toLocaleString(),
      text: '',
      answerList: [],
      endstatus: 1,
      inversion: false,
      error: false,
      loading: true,
      conversationOptions: null,
      requestOptions: { prompt: message, options: { ...options } },
    },
  )

  try {
    console.log(ToolsStore.ToolInfo)
    let lastText = ''
    const fetchChatAPIOnce = async () => {
      await fetchChatAPIProcess < Chat.ConversationResponse > ({
        conversationId: uuid,
        prompt: message,
        signal: controller.signal,
        url: `/eaide${ToolsStore.ToolInfo.apiUrl}`,
        capacityCode: ToolsStore.ToolInfo.capacityCode,
        onDownloadProgress: ({ event }) => {
          const xhr = event.target
          const { responseText } = xhr
          // if (chatStore.netFlag) {
          // 深度思考
          // 按行分割响应文本
          const lines = responseText.split('\n').filter(line => line.trim() !== '')

          // 重置文本,避免重复累加
          lastText = ''
          // 处理每一行数据
          for (const line of lines) {
            const trimmedLine = line.replace(/^data: /, '').trim()
            // console.log(trimmedLine)
            // info.setloading(false)
            try {
              // const data = JSON.parse(trimmedLine)
              const data = JSON.parse(trimmedLine?.substring(5))
              // console.log(data)

              // 停止回答用
              // currectemitterId.value = data.emitterId

              info.setloading(false)
              // 直接使用当前响应文本,不进行累加
              const deltaContent = data.choices[0].delta.content || ''
              lastText += deltaContent
              updateChat(
                uuid,
                index,
                {
                  dateTime: new Date().toLocaleString(),
                  text: lastText, // 使用完整的lastText,不再和之前的文本拼接
                  // answerList: data.answerList,
                  inversion: false,
                  error: false,
                  loading: true,
                  conversationOptions: {
                    conversationId: data.conversationContentId,
                    parentMessageId: data.id,
                  },
                  requestOptions: { prompt: message, options: { ...options } },
                },
              )

              if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
                options.parentMessageId = data.id
                message = ''
                return fetchChatAPIOnce()
              }

              scrollToBottomIfAtBottom()

              if (data.choices[0].finish_reason === 'stop')
                updateChatSome(uuid, index, { loading: false })
            }
            catch (error) {

            }
          }
          // }
          // else {
          //   // Always process the final line
          //   const lastIndex = responseText.lastIndexOf('\n', responseText.length - 3)
          //   let chunk = responseText
          //   if (lastIndex !== -1)
          //     chunk = responseText.substring(lastIndex)
          //   try {
          //     const data = JSON.parse(chunk?.substring(6))
          //     currectemitterId.value = data.emitterId
          //     info.setloading(false)
          //     updateChat(
          //       uuid,
          //       dataSources.value.length - 1,
          //       {
          //         dateTime: new Date().toLocaleString(),
          //         text: lastText + (data.answer ?? ''),
          //         answerList: data.answerList,
          //         endstatus: data.status,
          //         inversion: false,
          //         error: false,
          //         loading: true,
          //         conversationOptions: { conversationId: data.conversationId, parentMessageId: data.id },
          //         requestOptions: { prompt: message, options: { ...options } },
          //       },
          //     )
          //
          //     if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
          //       options.parentMessageId = data.id
          //       lastText = data.message
          //       message = ''
          //       return fetchChatAPIOnce()
          //     }
          //
          //     scrollToBottomIfAtBottom()
          //     if (data.status === '1')
          //       updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
          //   }
          //   catch (error) {
          //     //
          //   }
          // }
        },
      })
    }
    await fetchChatAPIOnce()
  }
  catch (error: any) {
    if (error.message === 'canceled') {
      updateChatSome(
        uuid,
        index,
        {
          loading: false,
        },
      )
      return
    }

    const errorMessage = error?.message ?? t('common.wrong')
    return
    updateChat(
      uuid,
      index,
      {
        dateTime: new Date().toLocaleString(),
        text: errorMessage,
        answerList: [],
        endstatus: 1,
        inversion: false,
        error: true,
        loading: false,
        conversationOptions: null,
        requestOptions: { prompt: message, options: { ...options } },
      },
    )
  }
  finally {
    loading.value = false
  }
}

function handleExport() {
  if (loading.value)
    return

  const d = dialog.warning({
    title: t('chat.exportImage'),
    content: t('chat.exportImageConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: async () => {
      try {
        d.loading = true
        const ele = document.getElementById('image-wrapper')
        const imgUrl = await toPng(ele as HTMLDivElement)
        const tempLink = document.createElement('a')
        tempLink.style.display = 'none'
        tempLink.href = imgUrl
        tempLink.setAttribute('download', 'chat-shot.png')
        if (typeof tempLink.download === 'undefined')
          tempLink.setAttribute('target', '_blank')
        document.body.appendChild(tempLink)
        tempLink.click()
        document.body.removeChild(tempLink)
        window.URL.revokeObjectURL(imgUrl)
        d.loading = false
        ms.success(t('chat.exportSuccess'))
        Promise.resolve()
      }
      catch (error: any) {
        ms.error(t('chat.exportFailed'))
      }
      finally {
        d.loading = false
      }
    },
  })
}

function handleDelete(index: number) {
  if (loading.value)
    return

  dialog.warning({
    title: t('chat.deleteMessage'),
    content: t('chat.deleteMessageConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: () => {
      chatStore.deleteChatByUuid(uuid, index)
    },
  })
}

function handleClear() {
  if (loading.value)
    return

  dialog.warning({
    title: t('chat.clearChat'),
    content: t('chat.clearChatConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: () => {
      chatStore.clearChatByUuid(uuid)
    },
  })
}

function handleEnter(event: KeyboardEvent) {
  if (!isMobile.value) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSubmit()
    }
  }
  else {
    if (event.key === 'Enter' && event.ctrlKey) {
      event.preventDefault()
      handleSubmit()
    }
  }
}

function handleStop() {
  if (loading.value) {
    controller.abort()
    loading.value = false
    recognition_stop({ emitterId: currectemitterId.value, conversationId: uuid })
    chatStore.activeHistory(uuid)
  }
}

// 可优化部分
// 搜索选项计算，这里使用value作为索引项，所以当出现重复value时渲染异常(多项同时出现选中效果)
// 理想状态下其实应该是key作为索引项,但官方的renderOption会出现问题，所以就需要value反renderLabel实现
const searchOptions = computed(() => {
  if (prompt.value.startsWith('/')) {
    return promptTemplate.value.filter((item: { key: string }) => item.key.toLowerCase().includes(prompt.value.substring(1).toLowerCase())).map((obj: { value: any }) => {
      return {
        label: obj.value,
        value: obj.value,
      }
    })
  }
  else {
    return []
  }
})

// value反渲染key
const renderOption = (option: { label: string }) => {
  for (const i of promptTemplate.value) {
    if (i.value === option.label)
      return [i.key]
  }
  return []
}

const placeholder = computed(() => {
  if (isMobile.value)
    return t('chat.placeholderMobile')
  return t('chat.placeholder')
})

const buttonDisabled = computed(() => {
  return loading.value || !prompt.value || prompt.value.trim() === ''
})

const footerClass = computed(() => {
  let classes = ['p-4']
  if (isMobile.value)
    classes = ['sticky', 'left-0', 'bottom-0', 'right-0', 'p-2', 'pr-3', 'overflow-hidden']
  return classes
})

onMounted(() => {
  // gethotTopic().then((res) => {
  //   hotList.value = res.data
  // })
  scrollToBottom()
  if (inputRef.value && !isMobile.value)
    inputRef.value?.focus()
})

onUnmounted(() => {
  chatStore.setState()
  info.clearToolLoding()
  if (loading.value)
    controller.abort()
})
const hotList = ref([{ title: '师范类大学的所有专业都需要学习《教育学》吗？' }, { title: '要学好人工智能专业哪些基础课比较关键？' }, { title: '"数据结构中的最短路径"有什么作用？' }, { title: '帮我总结《高等数学》中关于微积分的基础公式？' }, { title: '帮我总结《欧拉图》教案的内容' }])
const hotTopicClick = (item) => {
  prompt.value = item
  handleSubmit()
}

// 进度条相关数据
const showProgressModal = ref(false)
const executionNodes = ref([
  {
    id: 'knowledge_classifier',
    label: '意图识别',
    description: '识别用户意图和问题类型',
    details: [],
    status: 'pending',
    icon: icon1,
  },
  {
    id: 'code',
    label: '查询知识库',
    description: '从知识库中检索相关信息',
    details: [],
    status: 'pending',
    icon: icon2,
  },
  {
    id: 'exam_classifier',
    label: '意图分析',
    description: '深度分析用户意图',
    details: [],
    status: 'pending',
    icon: icon3,
  },
  {
    id: 'collector',
    label: '知识点聚合分析',
    description: '聚合和分析相关知识点',
    details: [],
    status: 'pending',
    icon: icon4,
  },
  {
    id: 'llm',
    label: '考题内容生成',
    description: '生成最终的考题内容',
    details: [],
    status: 'pending',
    icon: icon1,
  },
])

// 更新节点状态的函数
const updateNodeStatus = (nodeId, status, details = null) => {
  const node = executionNodes.value.find(n => n.id === nodeId)
  if (node) {
    node.status = status
    if (status === 'running')
      node.startTime = Date.now()
    else if (status === 'completed' || status === 'error')
      node.endTime = Date.now()

    // 如果有details内容，则更新节点的details
    if (details && details.length > 0)
      node.details = details
  }
}

// 重置所有节点状态
const resetNodeStatuses = () => {
  executionNodes.value.forEach((node) => {
    node.status = 'pending'
    node.details = []
  })
}

// 处理进度条关闭
const handleProgressClose = () => {
  showProgressModal.value = false
  resetNodeStatuses()
}

const isVoice = ref(false)
const isVoiceing = ref(false)
const recorder = ref()
const interval = ref()
const order = ref()
const currectemitterId = ref()
let longPressTimer: any = null
const sign = ref()
const getuuid = (len, radix) => {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  const uuid = []; let i
  radix = radix || chars.length

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]
  }
  else {
    // rfc4122, version 4 form
    let r

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
    uuid[14] = '4'

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16
        uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r]
      }
    }
  }

  return uuid.join('')
}

const initRecoder = () => {
  recorder.value = new Recorder({
    sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
    sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，
    numChannels: 1, // 声道，支持 1 或 2， 默认是1
    compiling: true,
  })
}
const openVideo = () => {
  //  开始识别语音
  initRecoder()
  nextTick(() => {
    startRecord()
  })
}
const startRecord = () => {
  recorder.value.start()
  order.value = 0
  sign.value = getuuid(8, 16)
  // interval.value = setInterval(() => {
  //   stopRecord('0')
  // }, 1500)
}
const stopRecord = async (receiveStatus) => {
  // sendSock(recorder.value.getNextData())
  // sendSock(recorder.value.getPCMBlob())
  // 2是新对话
  return
  let flag = 1
  if (!uuid) {
    flag = 2
    const { data } = await addHistory({
      userId: info.userInfo.userId,
      title: '新的对话',
    })
    uuid = data.id
    chatStore.history.unshift({ uuid, title: '新的对话', isEdit: false, data: [] })
    chatStore.active = uuid as any
  }
  order.value = order.value + 1
  const formData = new FormData()
  formData.append('userId', info.userInfo.userId)
  formData.append('conversationId', uuid)
  formData.append('msgType', '1')
  formData.append('file', recorder.value.getPCMBlob())
  formData.append('receiveStatus', receiveStatus)
  formData.append('sign', sign.value)
  formData.append('order', order.value)
  const fetchChatAPIOnce = async () => {
    await voiceToText({
      data: formData,
      signal: controller.signal,
      onDownloadProgress: ({ event }) => {
        const xhr = event.target
        const { responseText } = xhr
        // Always process the final line
        const lastIndex = responseText.lastIndexOf('\n', responseText.length - 3)
        let chunk = responseText
        if (lastIndex !== -1)
          chunk = responseText.substring(lastIndex)
        try {
          const data = JSON.parse(chunk?.substring(6))
          if (data.receiveStatus === 1) {
            prompt.value = data.answer
            if (flag === 2)
              chatStore.history[0].title = data.answer

            handleSubmit('2')
          }
        }
        catch (error) {
          //
        }
      },
    })
  }
  await fetchChatAPIOnce()
  isVoiceing.value = false
  recorder.value && recorder.value.destroy()
  recorder.value = null
}

const handleTouchStart = () => {
  // 设置长按定时器
  longPressTimer = setTimeout(() => {
    // 处理长按事件
    console.log('长按事件触发')
    isVoiceing.value = true
    openVideo()
  }, 1000) // 长按时间阈值，1000毫秒
}

const handleTouchEnd = () => {
  clearTimeout(longPressTimer)
  longPressTimer = null
  clearInterval(interval.value)
  if (isVoiceing.value) {
    // 处理释放事件
    isVoiceing.value = false
    stopRecord('1')
    console.log('长按释放事件触发')
  }
}

const handleTouchMove = () => {
  isVoiceing.value = false
  clearTimeout(longPressTimer)
  longPressTimer = null
  console.log('取消')
  isVoiceing.value = false
  recorder.value && recorder.value.destroy()
  recorder.value = null
}
const handleTouchMoveDebounce = debounce(handleTouchMove, 600)

function jumpPage(url: string) {
  if (url)
    router.push(url)

  else
    router.go(-1)
}
function sendQuestionitem(title: string) {
  prompt.value = title
  handleSubmit()
}
/*******************************************************/
</script>

<template>
  <!--	<audioView @change="audioViewFn"></audioView> -->
  <div :class="isMobile ? 'flex flex-col w-full h-full bg-[#fff]' : 'flex flex-col w-full h-full bg-[#F7F9FF]'">
    <HeaderComponent v-if="isMobile" :using-context="usingContext" @export="handleExport" @handle-clear="handleClear" />
    <main class="flex-1 overflow-hidden">
      <!-- <header class="headers">
          <div class="left"><div class="gohome" @click="jumpPage()"><img src="@/assets/workShopPage/leftarrow.png"></div>{{ ToolsStore.ToolInfo.name }}助手</div>
          </header>  -->
      <div id="scrollRef" ref="scrollRef" class="h-full overflow-hidden overflow-y-auto">
        <NSpin :show="info.loading">
          <div v-if="!dataSources.length" class="w-full  m-auto kaichangbai headpadding" style="min-height: 60vh;">
            <img :src="ToolsStore.ToolInfo.icon">
            <div class="title">
              {{ ToolsStore.ToolInfo.title }}
            </div>
            <div class="des">
              {{ ToolsStore.ToolInfo.des }}
            </div>
            <div class="openingQuestionrow">
              <div
                v-for="(content, index) in ToolsStore.ToolInfo.openingQuestionArrList" :key="index"
                class="openingQuestionitem" @click="sendQuestionitem(content)"
              >
                <div class="context">
                  <img class="openingQuestionicon" src="@/assets/openingicon.png">{{ content }}
                </div>
                <div><img src="@/assets/workShopPage/leftarrow.png"></div>
              </div>
            </div>
          </div>

          <div class="w-full max-w-screen-xl m-auto ">
            <div id="image-wrapper" class="relative">
              <template v-if="!dataSources.length">
                <!--              <div class="flex items-center justify-center mt-4 text-center text-neutral-300"> -->
                <!--                <SvgIcon icon="ri:bubble-chart-fill" class="mr-2 text-3xl" /> -->
                <!--                <span>{{ t('chat.newChatTitle') }}</span> -->
                <!--              </div> -->
                <div class="w-full " :class="[isMobile ? 'mt-40' : 'mt-[10vh] mt-[12vh]']">
                  <!-- <img class=" mx-auto" :class="[isMobile ? 'w-[226px] h-[58px] mb-[18px]' : 'w-[617px] h-[67px] mb-[38px]']" src="../../assets/logo3.png" alt="">
                  <div class="flex justify-center">
                    <p :class="[isMobile ? 'text-[16px]' : 'text-2xl']" class="font-semibold text-[#323233]">
                      你好！我是联通智教大模型，教学相长，不亦说乎？
                    </p>
                  </div> -->
                  <div
                    v-show="!isVoice || !isMobile" class="relative mx-auto"
                    :class="[isMobile ? 'w-[327px] h-[134px] mt-7 rounded-[12px]' : ' h-[64px] max-w-[960px] mt-11']"
                  >
                    <div class="gradient-border-cen  h-[64px] max-w-[960px]">
                      <NAutoComplete
                        v-show="!isVoice || !isMobile" v-model:value="prompt" :options="searchOptions"
                        :render-label="renderOption" class=""
                      >
                        <template #default="{ handleInput, handleBlur, handleFocus }">
                          <NInput
                            ref="inputRef" v-model:value="prompt" type="text" :bordered="false"
                            :placeholder="`向${ToolsStore.ToolInfo.name}助手提问`" @input="handleInput" @focus="handleFocus"
                            @blur="handleBlur" @keypress="handleEnter"
                          >
                            <!-- <template #prefix>
                            <NTooltip trigger="hover">
                                <template #trigger>
                                    附件
                                </template>
                                上传教材
                              </NTooltip>
                            </template> -->
                          </NInput>
                        </template>
                      </NAutoComplete>
                      <div style="display:none" class="flex absolute left-0 bottom-[19px]">
                        <div
                          v-if="false" class="flex justify-center items-center  cursor-pointer"
                          :class="[isMobile ? 'ml-[18px] mr-[12px]' : 'w-[125px] h-[36px] mx-2']"
                        >
                          <img
                            src="../../assets/chat/sdsk.png" alt=""
                            :class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']"
                          >
                          <p
                            class=" text-[#CA0D00] font-medium"
                            :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']"
                          >
                            深度思考
                          </p>
                        </div>
                        <div
                          v-show="!chatStore.netFlag"
                          class="ml-[12px] flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer"
                          :class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"
                          @click="chatStore.netFlag = !chatStore.netFlag"
                        >
                          <img
                            src="../../assets/chat/lwd.png" alt=""
                            :class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']"
                          >
                          <p
                            class=" font-medium text-[#606266]"
                            :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']"
                          >
                            联网搜索
                          </p>
                        </div>
                        <div
                          v-show="chatStore.netFlag"
                          class="ml-[12px] flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer bg-[#125EFF1a]"
                          :class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"
                          @click="chatStore.netFlag = !chatStore.netFlag"
                        >
                          <img
                            src="../../assets/chat/lwa.png"
                            :class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']"
                          >
                          <p
                            class=" font-medium text-[#125EFF]"
                            :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']"
                          >
                            联网搜索
                          </p>
                        </div>
                      </div>
                      <!-- 发送 -->
                      <div v-if="prompt" class="sendicon send" @click="handleSubmit">
                        <img src="@/assets/sendicon.png">
                      </div>
                      <div v-else class="sendicon">
                        <img src="@/assets/tiwen.png">
                      </div>
                      <!-- <NButton v-show="!isMobile" style="background-image: linear-gradient(135deg, #FDE6FE 0%, #C3F9FF 100%);" class="!absolute right-[23px] bottom-5 !w-[38px] !h-[38px] !rounded-lg" type="primary" :disabled="buttonDisabled" color="#CA0D00" @click="handleSubmit">
                        <template #icon>
                          <span class="dark:text-black">
                            <img class="w-[20px] h-[20px]" src="../../assets/chat/fill.png" alt="">
                          </span>
                        </template>
                      </NButton> -->
                      <NButton
                        v-show="isMobile && prompt !== ''"
                        style="background-image: linear-gradient(135deg, #FDE6FE 0%, #C3F9FF 100%);" type="primary"
                        :disabled="buttonDisabled" color="#CA0D00"
                        class="!absolute right-[23px] bottom-5 !w-[38px] !h-[38px] !rounded-lg" @click="handleSubmit"
                      >
                        <template #icon>
                          <span class="dark:text-black">
                            <img class="w-[20px] h-[20px]" src="../../assets/chat/fill.png" alt="">
                          </span>
                        </template>
                      </NButton>
                      <!-- 语音 -->
                      <!-- <NButton
											 v-show="!isVoice && isMobile && prompt === ''" size="large" type="primary" circle color="#CA0D00"
											 class="!absolute right-[23px] bottom-5 !w-[38px] !h-[38px] !rounded-lg"
											 @click="isVoice = true"
										 >
											 <img class="w-[24px] h-[24px]" src="../../assets/chat/voiced2.png" alt="">
										 </NButton> -->
                    </div>
                  </div>

                  <!-- 按住说话 -->
                  <div v-show="isVoiceing && isMobile" class="mt-7 w-80  h-14 mx-auto relative">
                    <!--									v-show="isVoiceing && isMobile" -->
                    <div
                      v-show="isVoiceing && isMobile"
                      class="w-[100vw] h-[147px] mb-5 mx-auto text-center absolute centers top-[-147px] linear"
                    >
                      <img
                        class="w-[32px] h-[32px] mx-auto mb-2 mt-[79px]" src="../../assets/chat/canleVoice.png"
                        alt=""
                      >
                      <span class="text-[12px] text-[#606266]">松手发送，上移取消</span>
                    </div>
                    <!--									v-show="isVoiceing && isMobile && isVoice" -->
                    <div
                      v-show="isVoiceing && isMobile && isVoice" class="mt-7 w-80  h-14 mx-auto"
                      @touchstart="handleTouchStart" @touchend="handleTouchEnd" @touchcancel="handleTouchEnd"
                      @touchmove="handleTouchMoveDebounce"
                    >
                      <img src="../../assets/chat/voiceing.png" alt="">
                    </div>
                  </div>

                  <div v-show="!isVoiceing && isMobile && isVoice" class="relative w-80 mt-7 h-14 mx-auto">
                    <div
                      class="bg-[#fff] w-80  flex items-center justify-center h-14 rounded-2xl"
                      @touchstart="handleTouchStart" @touchend="handleTouchEnd" @touchcancel="handleTouchEnd"
                      @touchmove="handleTouchMoveDebounce"
                    >
                      <div>按住说话</div>
                    </div>
                    <NButton
                      v-show="isVoice && isMobile && prompt === ''" size="large" type="primary" circle
                      color="#F6F6F7"
                      class="!absolute right-[12px] top-[50%] !mt-[-19px] !w-[38px] !h-[38px] !rounded-lg"
                      @click="isVoice = false"
                    >
                      <img class="w-[38px] h-[38px]" src="../../assets/chat/text2.png" alt="">
                    </NButton>
                  </div>
                </div>
              </template>
              <template v-else>
                <div style="min-height: 60vh;">
                  <Message
                    v-for="(item, index) of dataSources" :key="index" :answer-list="item.answerList"
                    :category="item.category" :conversation-id="chatStore.active"
                    :topic="item.topic"
                    :conversation-content-id="item.conversationOptions?.conversationId" :date-time="item.dateTime"
                    :endstatus="item.endstatus" :error="item.error" :inversion="item.inversion" :loading="item.loading"
                    :text="item.text" @delete="handleDelete(index)" @regenerate="onRegenerate(index)"
                    @load-tap="(flag:boolean) => $emit('loadTap', flag)"
                  />
                  <!-- <div class="sticky bottom-0 left-0 flex justify-center">
                    <NButton v-if="loading" type="warning" @click="handleStop">
                      <template #icon>
                        <SvgIcon icon="ri:stop-circle-line" />
                      </template>
                      {{ t('common.stopResponding') }}
                    </NButton>
                  </div> -->
                </div>
              </template>
            </div>
          </div>
        </NSpin>
      </div>
    </main>

    <footer :class="footerClass">
      <div v-if="dataSources.length" class="max-w-screen-xl m-auto" :class="[isMobile ? 'w-5/6' : '']">
        <div v-show="isVoiceing && isMobile" class="w-full mb-5 mx-auto text-center">
          <img class="w-[32px] h-[32px] mx-auto mb-2" src="../../assets/chat/canleVoice.png" alt="">
          <span class="text-[12px] text-[#606266]">松手发送，上移取消</span>
        </div>
        <div class="flex items-center justify-center space-x-2">
          <!--          <HoverButton v-if="!isMobile" @click="handleClear"> -->
          <!--            <span class="text-xl text-[#4f555e] dark:text-white"> -->
          <!--              <SvgIcon icon="ri:delete-bin-line" /> -->
          <!--            </span> -->
          <!--          </HoverButton> -->
          <!--          <HoverButton v-if="!isMobile" @click="handleExport"> -->
          <!--            <span class="text-xl text-[#4f555e] dark:text-white"> -->
          <!--              <SvgIcon icon="ri:download-2-line" /> -->
          <!--            </span> -->
          <!--          </HoverButton> -->
          <!--          <HoverButton @click="toggleUsingContext"> -->
          <!--            <span class="text-xl" :class="{ 'text-[#4b9e5f]': usingContext, 'text-[#a8071a]': !usingContext }"> -->
          <!--              <SvgIcon icon="ri:chat-history-line" /> -->
          <!--            </span> -->
          <!--          </HoverButton> -->
          <div
            v-show="isVoiceing && isMobile && isVoice" @touchstart="handleTouchStart" @touchend="handleTouchEnd"
            @touchcancel="handleTouchEnd" @touchmove="handleTouchMoveDebounce"
          >
            <img src="../../assets/chat/voiceing.png" alt="">
          </div>

          <div v-show="!isVoiceing && isMobile && isVoice" class="relative bg-[#F6F6F7] w-full h-10">
            <div
              class=" bg-[#F6F6F7] w-full flex items-center justify-center h-10 rounded-2xl"
              @touchstart="handleTouchStart" @touchend="handleTouchEnd" @touchcancel="handleTouchEnd"
              @touchmove="handleTouchMoveDebounce"
            >
              <div>按住说话</div>
            </div>
            <NButton
              v-show="isVoice && isMobile && prompt === ''"
              class="!absolute !right-3 !top-[50%] !mt-[-15px] !w-[32px] !h-[32px] !rounded-lg" size="large"
              type="primary" circle color="#CA0D00" @click="isVoice = false"
            >
              <img class="w-[32px] h-[32px]" src="../../assets/chat/text2.png" alt="">
            </NButton>
          </div>
          <div
            v-show="!isVoice || !isMobile" class="relative mx-auto"
            :class="[isMobile ? 'w-[327px] h-[134px] rounded-[12px]' : 'w-[100%] h-[67px] max-w-[1150px]']"
          >
            <div class="gradient-border-cen  h-[67px] max-w-[960px]">
              <NAutoComplete
                v-show="!isVoice || !isMobile" v-model:value="prompt" :options="searchOptions"
                :render-label="renderOption" class=""
              >
                <template #default="{ handleInput, handleBlur, handleFocus }">
                  <NInput
                    ref="inputRef" v-model:value="prompt" type="text" :bordered="false"
                    :placeholder="`向${ToolsStore.ToolInfo.name}助手提问`" @input="handleInput" @focus="handleFocus"
                    @blur="handleBlur" @keypress="handleEnter"
                  />
                </template>
              </NAutoComplete>
              <div style="display:none" class="flex absolute left-0 bottom-[19px]">
                <div
                  v-if="false" class="flex justify-center items-center  cursor-pointer"
                  :class="[isMobile ? 'ml-[18px] mr-[12px]' : 'w-[125px] h-[36px] mx-2']"
                >
                  <img
                    src="../../assets/chat/sdsk.png" alt=""
                    :class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']"
                  >
                  <p class=" text-[#CA0D00] font-medium" :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']">
                    深度思考
                  </p>
                </div>
                <div
                  v-show="!chatStore.netFlag"
                  class="ml-[12px] flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer"
                  :class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"
                  @click="chatStore.netFlag = !chatStore.netFlag"
                >
                  <img
                    src="../../assets/chat/lwd.png" alt=""
                    :class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']"
                  >
                  <p class=" font-medium text-[#606266]" :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']">
                    联网搜索
                  </p>
                </div>
                <div
                  v-show="chatStore.netFlag"
                  class="ml-[12px] flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer bg-[#125EFF1a]"
                  :class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"
                  @click="chatStore.netFlag = !chatStore.netFlag"
                >
                  <img
                    src="../../assets/chat/lwa.png" alt=""
                    :class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']"
                  >
                  <p class=" font-medium text-[#125EFF]" :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']">
                    联网搜索
                  </p>
                </div>
              </div>
              <!-- 发送 -->
              <div v-if="prompt" class="sendicon send" @click="handleSubmit">
                <img src="@/assets/sendicon.png">
              </div>
              <div v-else class="sendicon">
                <img src="@/assets/tiwen.png">
              </div>
              <NButton
                v-show="isMobile && prompt !== ''"
                style="background-image: linear-gradient(135deg, #FDE6FE 0%, #C3F9FF 100%);" type="primary"
                :disabled="buttonDisabled" color="#CA0D00"
                class="!absolute right-[23px] bottom-5 !w-[38px] !h-[38px] !rounded-lg" @click="handleSubmit"
              >
                <template #icon>
                  <span class="dark:text-black">
                    <img class="w-[20px] h-[20px]" src="../../assets/chat/fill.png" alt="">
                  </span>
                </template>
              </NButton>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="text-center mt-2 mb-[4px] text-xs mx-auto text-[#C2C2C2]">
        © 中国联通  辽ICP备XXXXXXXXXXXX号-X    辽公网备XXXXXXXXXXXXXXXXX
      </div>
      <div class="text-center mt-2 mb-[4px] text-xs mx-auto text-[#C2C2C2]">
        Powered by 中国联通智慧教育军团
      </div> -->
      <!--      <div class="text-center mt-2 mb-[4px] text-xs mx-auto text-[#C2C2C2]"> -->
      <!--        AI算力服务由中国联通提供支持 -->
      <!--      </div> -->
      <!--      <div class="text-center mt-2 mb-[4px] text-xs mx-auto text-[#C2C2C2]"> -->
      <!--        © 四川大学 信息化建设与管理办公室 技术支持: 028-85410000 -->
      <!--      </div> -->
      <!-- <p class="text-[#606266] text-xs mx-auto text-center mb-4">
      </p> -->
    </footer>

    <!-- 进度条弹窗 -->
    <AgentExecutionPreviewModal
      v-model:show="showProgressModal" :allow-close="false" title="正在生成考题内容"
      :nodes="executionNodes" :hide-download-button="true" :auto-close-on-complete="true" :auto-close-delay="2000"
      @close="handleProgressClose"
    />
  </div>
</template>

<style lang="less">
  .back {
    background: url("@/assets/chat/back.png");
    background-size: 100% 100%;
  }

  .gradient-border {
    border-radius: 17px;
    background-image: linear-gradient(114deg, #CA82FF 0%, #5479F5 100%);

    .gradient-border-cen {
      position: absolute;
      width: calc(100% - 4px);
      background: #fff;
      border-radius: 16px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      border: 1px solid #EDEDED;
      box-shadow: 0 0 15px 0 #d8d8d866;
      border-radius: 16px;
      display: flex;
      overflow: hidden;

      .n-input-wrapper {
        padding-left: 30px;
      }
    }
  }

  .gradient-border-cen {
    position: absolute;
    width: calc(100% - 4px);
    background: #fff;
    border-radius: 16px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid #EDEDED;
    box-shadow: 0 0 15px 0 #d8d8d866;
    border-radius: 16px;
    display: flex;
    overflow: hidden;
    align-items: center;
    padding-right: 20px;

    .n-input-wrapper {
      padding-left: 20px;
    }
  }

  .hottext {
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 隐藏超出的内容 */
    text-overflow: ellipsis;
    /* 用省略号表示被隐藏的部分 */
    height: 16px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #4A5159;
    letter-spacing: 0;
    line-height: 16px;
  }

  .hottextpc {
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 隐藏超出的内容 */
    text-overflow: ellipsis;
    /* 用省略号表示被隐藏的部分 */
    height: 16px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #4A5159;
    letter-spacing: 0;
    line-height: 16px;
    margin-right: 8px;
  }

  .inputRef {
    width: 327px;
    height: 75px;
    background: #FFFFFF;
    border-radius: 12px;

    .n-input__border {
      border: none;
    }
  }

  .centers {
    left: 50%;
    transform: translateX(-50%);
  }

  .linear {
    background-image: linear-gradient(to bottom,
        rgba(255, 255, 255, 0) 30%,
        #F1F1F1 70%);
  }

  .kaichangbai {
    img {
      width: 74px;
      height: 74px;
      margin: 0 auto;
    }

    .title {
      height: 25px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 18px;
      color: #323233;
      letter-spacing: 0;
      text-align: center;
      margin-top: 19px;
      margin-bottom: 10px;
    }

    .des {
      height: 22px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #606266;
      letter-spacing: 0;
      text-align: center;
      margin-bottom: 30px;
    }

    .openingQuestionrow {
      max-width: 752px;
      // background: #FFFFFF;
      margin: 0 auto;

      .openingQuestionitem {
        height: 46px;
        border: 1px solid #EAEDF0;
        border-radius: 12px;
        display: flex;
        justify-content: space-between;
        background: #FFFFFF;
        align-items: center;
        padding-right: 21.72px;
        padding-left: 16px;
        margin-bottom: 12px;

        .context {
          display: flex;
          align-items: center;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 16px;
          color: #323233;
          letter-spacing: 0;

          .openingQuestionicon {
            margin-right: 8.72px;
            width: 19px;
            height: 12px;
          }
        }

        img {
          width: 18px;
          height: 21px;
          transform: rotate(180deg);
        }
      }

      .openingQuestionitem:hover {
        cursor: pointer;
        border: 1px solid #125EFF;
      }
    }
  }

  .sendicon {
    width: 40px;
    height: 40px;
    background: #D6D5DE;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 21px;
      height: 21px;
    }
  }

  .send {
    background-image: linear-gradient(135deg, #FDE6FE 0%, #C3F9FF 100%);
  }

  .send:hover {
    cursor: pointer;
  }

  .headpadding {
    position: relative;
    padding-top: 56px;
    // height: 75vh;
    max-height: 75px;
    min-height: 65vh;

    header {
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .gohome:hover {
    cursor: pointer;
  }
</style>
