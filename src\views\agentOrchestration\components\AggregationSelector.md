# AggregationSelector 聚合变量选择器

一个用于选择聚合变量的下拉选择组件，支持分组显示和变量选择。

## 功能特性

- ✅ 分组显示：节点作为分组，变量作为可选项
- ✅ 层级显示：选择后显示"分组 > 变量"的格式
- ✅ 视觉反馈：选中项显示蓝色背景和文字
- ✅ 双向绑定：支持 v-model
- ✅ 事件回调：提供详细的选择事件信息
- ✅ 点击外部关闭：自动关闭下拉框
- ✅ 响应式设计：适配不同屏幕尺寸

## 基本用法

```vue
<template>
  <AggregationSelector
    v-model="selectedVariable"
    :options="aggregationOptions"
    placeholder="请选择变量"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import AggregationSelector from './AggregationSelector.vue'

const selectedVariable = ref('')

const aggregationOptions = ref([
  {
    label: "节点名称1",
    value: "node1",
    variables: [
      { name: "历史对话信息", type: "结构化数据" },
      { name: "当前对话信息", type: "文本" },
      { name: "用户ID", type: "文本" }
    ]
  },
  {
    label: "节点名称2", 
    value: "node2",
    variables: [
      { name: "历史对话信息", type: "结构化数据" },
      { name: "当前对话信息", type: "文本" }
    ]
  }
])

const handleChange = (value, variable, group) => {
  console.log('选择的值:', value)
  console.log('选择的变量:', variable)
  console.log('所属分组:', group)
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | string | '' | 当前选中的值，支持 v-model |
| placeholder | string | '请选择变量' | 占位符文本 |
| options | OptionGroup[] | [] | 选项数据 |

### OptionGroup 数据结构

```typescript
interface Variable {
  name: string    // 变量名称
  type: string    // 变量类型
}

interface OptionGroup {
  label: string      // 分组名称
  value: string      // 分组值
  variables: Variable[]  // 变量列表
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: string) | 选中值变化时触发 |
| change | (value: string, variable: Variable, group: OptionGroup) | 选择变量时触发 |

## 样式定制

组件使用 scoped 样式，主要的 CSS 类名：

```css
.aggregation-selector        /* 组件根容器 */
.selector-trigger           /* 触发器 */
.selected-text              /* 选中文本 */
.selected-text.has-selection /* 有选中项时的样式 */
.selector-dropdown          /* 下拉框 */
.selector-group             /* 分组容器 */
.group-header               /* 分组头部 */
.group-title                /* 分组标题 */
.group-variables            /* 变量列表容器 */
.variable-option            /* 变量选项 */
.variable-option.selected   /* 选中的变量选项 */
```

## 使用示例

### 在表单中使用

```vue
<template>
  <n-form-item label="聚合变量" path="aggregationVariable">
    <AggregationSelector
      v-model="formData.aggregationVariable"
      :options="aggregationOptions"
      placeholder="请选择要聚合的变量"
      @change="handleAggregationChange"
    />
  </n-form-item>
</template>
```

### 动态数据

```vue
<script setup>
// 从API获取选项数据
const loadOptions = async () => {
  const response = await api.getAggregationOptions()
  aggregationOptions.value = response.data
}

onMounted(() => {
  loadOptions()
})
</script>
```

## 注意事项

1. **数据格式**：确保 options 数据符合 OptionGroup 接口定义
2. **唯一性**：每个分组的 value 应该是唯一的
3. **变量名称**：同一分组内的变量名称应该是唯一的
4. **选中值格式**：组件内部使用 `${group.value}-${variable.name}` 作为选中值
5. **样式覆盖**：如需自定义样式，请使用深度选择器 `:deep()`

## 更新日志

### v1.0.0
- 初始版本
- 支持分组显示和变量选择
- 支持双向绑定和事件回调
- 添加视觉反馈和交互效果
