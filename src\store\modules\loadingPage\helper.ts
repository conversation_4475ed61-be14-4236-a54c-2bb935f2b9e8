import { ss } from '@/utils/storage'

const LOCAL_NAME = 'loadingPageStorage'

// 定义数据结构
export interface loadingPageData {
  // 可根据实际返回数据添加字段，示例如下
  uploadStatus: boolean,
  fileId: string,
  size: number
  fileUrl: string
  previewUrl: string
}

// 定义state结构
export interface loadingPageState {
  loadingPageData: loadingPageData
}

// 默认设置
export function defaultSetting(): loadingPageState {
  return {
    loadingPageData: {
      uploadStatus: false,
      fileId: '',
      size: 0,
      fileUrl: '',
      previewUrl: ''
    }
  }
}

// 获取本地状态
export function getLocalState(): loadingPageState {
  const localSetting: loadingPageState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

// 设置本地状态
export function setLocalState(setting: loadingPageState): void {
  ss.set(LOCAL_NAME, setting)
}