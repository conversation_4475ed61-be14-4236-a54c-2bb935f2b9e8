<template>
    <div class="iframebox">
        <iframe :src="linkurl" width="100%" height="100%" class="border rounded-md shadow-md"></iframe>
    </div>
</template>
<script setup>
import { onMounted,ref } from 'vue';
import { useRoute } from 'vue-router';
import {getlinksApi} from '@/api/tools'
const route = useRoute();
var linkurl=ref('')
var linkArr=ref([
    {name:"资源检索",pageLink:"http://aigc.sunmooc.cn:11000/resourceView?queryData=eyJpbWdTcmMiOiIvYXNzZXRzL%2Bi1hOa6kOajgOe0oi0yMDVlZmUwOS5wbmciLCJuYW1lIjoi6LWE5rqQ5qOA57SiIiwidHlwZSI6MSwiaWNvbiI6Ii9hc3NldHMv6LWE5rqQ5qOA57SiLTIwNWVmZTA5LnBuZyIsImJ0blRleHQiOiLmo4DntKIiLCJyZXF1ZXN0UHJvY2VzcyI6Iui1hOa6kOajgOe0ouS4rSIsInJlcXVlc3RTdWNjZXNzIjoi6LWE5rqQ5qOA57Si5a6M5oiQIiwicmVxdWVzdEVycm9yIjoi6LWE5rqQ5qOA57Si5byC5bi4Iiwic3BlY2lhbF9yZXF1ZXN0VXJsIjoiL3N1bi1haS9vcGVuL3NlYXJjaCIsInNwZWNpYWxfcmVxdWVzdE1ldGhvZCI6IkdFVCIsInNwZWNpYWxfcmVxdWVzdFBhciI6eyJrZXl3b3JkIjoiIn0sIm5vbmVEYXRhSW5mbyI6IuaKseatie%2B8jOayoeacieaJvuWIsOebuOWFs%2Bi1hOa6kCJ9"},
    {name:"行业岗位任职能力分析",pageLink:"http://aigc.sunmooc.cn:11000/searchView?queryData=eyJpbWdTcmMiOiIvYXNzZXRzL%2BihjOS4muWyl%2BS9jeS7u%2BiBjOiDveWKm%2BWIhuaekC0wOWZhMzA0Yy5wbmciLCJuYW1lIjoi6KGM5Lia5bKX5L2N5Lu76IGM6IO95Yqb5YiG5p6QIiwidHlwZSI6NCwiaWNvbiI6Ii9hc3NldHMv6KGM5Lia5bKX5L2N5Lu76IGM6IO95Yqb5YiG5p6QLTA5ZmEzMDRjLnBuZyIsImJ0blRleHQiOiLliIbmnpAiLCJodG1sQ2xhc3NOYW1lIjoiam9iSHRtbENsYXNzIiwiaXNSZW5kZXJUYWJsZSI6dHJ1ZSwicmVxdWVzdFByb2Nlc3MiOiLooYzkuJrlspfkvY3ku7vogYzog73lipvliIbmnpDkuK0iLCJyZXF1ZXN0U3VjY2VzcyI6IuihjOS4muWyl%2BS9jeS7u%2BiBjOiDveWKm%2BWIhuaekOWujOaIkCIsInJlcXVlc3RFcnJvciI6IuihjOS4muWyl%2BS9jeS7u%2BiBjOiDveWKm%2BWIhuaekOW8guW4uCIsInJlcXVlc3RVcmwiOiIvc3VuLWFpL29wZW4vam9iLWFuYWx5c2lzIiwicmVxdWVzdE1ldGhvZCI6IkdFVCIsInJlcXVlc3RQYXIiOnsidXNlcklkIjoiIiwiY29udGVudCI6IiJ9fQ=="},
    {name:"智能生成教学环节",pageLink:"http://aigc.sunmooc.cn:11000/searchView?queryData=eyJpbWdTcmMiOiIvYXNzZXRzL%2BaZuuiDveeUn%2BaIkOaVmeWtpueOr%2BiKgi05NmExNjJhMi5wbmciLCJuYW1lIjoi5pm66IO955Sf5oiQ5pWZ5a2m546v6IqCIiwidHlwZSI6MywiaWNvbiI6Ii9hc3NldHMv5pm66IO955Sf5oiQ5pWZ5a2m546v6IqCLTk2YTE2MmEyLnBuZyIsImJ0blRleHQiOiLnlJ/miJAiLCJodG1sQ2xhc3NOYW1lIjoiQW5zd2VyVmFsdWVDbGFzcyB0ZWFjaGVyUHJvY2Vzc0NsYXNzIiwicmVxdWVzdFByb2Nlc3MiOiLmlZnlrabnjq/oioLnlJ/miJDkuK0iLCJyZXF1ZXN0U3VjY2VzcyI6IuaVmeWtpueOr%2BiKgueUn%2BaIkOWujOaIkCIsInJlcXVlc3RFcnJvciI6IuaVmeWtpueOr%2BiKgueUn%2BaIkOW8guW4uCIsInJlcXVlc3RVcmwiOiIvc3VuLWFpL29wZW4vdGVhY2hpbmctZmxvdyIsInJlcXVlc3RNZXRob2QiOiJHRVQiLCJyZXF1ZXN0UGFyIjp7InVzZXJJZCI6IiIsImtub3dsZWRnZSI6IiJ9fQ=="},
    {name:"智能编写思政案例",pageLink:"http://aigc.sunmooc.cn:11000/searchView?queryData=eyJpbWdTcmMiOiIvYXNzZXRzL%2BaZuuiDvee8luWGmeaAneaUv%2BahiOS%2Biy04NTVjMzM4OC5wbmciLCJuYW1lIjoi5pm66IO957yW5YaZ5oCd5pS/5qGI5L6LIiwidHlwZSI6MiwiaWNvbiI6Ii9hc3NldHMv5pm66IO957yW5YaZ5oCd5pS/5qGI5L6LLTg1NWMzMzg4LnBuZyIsImJ0blRleHQiOiLnvJblhpkiLCJodG1sQ2xhc3NOYW1lIjoiQW5zd2VyVmFsdWVDbGFzcyBwb2xpdGljc0h0bWxDbGFzcyIsInJlcXVlc3RQcm9jZXNzIjoi5qGI5L6L57yW5YaZ5LitIiwicmVxdWVzdFN1Y2Nlc3MiOiLmoYjkvovnvJblhpnlrozmiJAiLCJyZXF1ZXN0RXJyb3IiOiLmoYjkvovnvJblhpnlvILluLgiLCJyZXF1ZXN0VXJsIjoiL3N1bi1haS9vcGVuL3RlYWNoaW5nLXN6LXBsYW4iLCJyZXF1ZXN0TWV0aG9kIjoiR0VUIiwicmVxdWVzdFBhciI6eyJ1c2VySWQiOiIiLCJrbm93bGVkZ2UiOiIifX0="},
    {name:"基于文档总结知识点",pageLink:"http://aigc.sunmooc.cn:11000/fileUploadView?queryData=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%2B65LqO5paH5qGj5oC757uT55%2Bl6K%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%2Bl6K%2BG54K55oC757uT5LitIiwicmVxdWVzdFN1Y2Nlc3MiOiLnn6Xor4bngrnmgLvnu5PlrozmiJAiLCJyZXF1ZXN0RXJyb3IiOiLnn6Xor4bngrnmgLvnu5PlvILluLgiLCJyZXF1ZXN0VXJsIjoiL3N1bi1haS9vcGVuL2dlbmVyYXRlL2tub3dsZWRnZSIsInJlcXVlc3RNZXRob2QiOiJQT1NUIn0="},
    {name:"智能续写",pageLink:"http://aigc.sunmooc.cn:11000/searchView?queryData=eyJpbWdTcmMiOiIvYXNzZXRzL%2BaZuuiDvee7reWGmS0wYjQwMDc3ZC5wbmciLCJuYW1lIjoi5pm66IO957ut5YaZIiwidHlwZSI6MiwiaWNvbiI6Ii9hc3NldHMv5pm66IO957ut5YaZLTBiNDAwNzdkLnBuZyIsImJ0blRleHQiOiLnu63lhpkiLCJodG1sQ2xhc3NOYW1lIjoiIiwicmVxdWVzdFByb2Nlc3MiOiLmmbrog73nu63lhpnkuK0iLCJyZXF1ZXN0U3VjY2VzcyI6IuaZuuiDvee7reWGmeWujOaIkCIsInJlcXVlc3RFcnJvciI6IuaZuuiDvee7reWGmeW8guW4uCIsInJlcXVlc3RVcmwiOiIvc3VuLWFpL29wZW4vY29udGludWVkLXdyaXRpbmciLCJyZXF1ZXN0TWV0aG9kIjoiR0VUIiwicmVxdWVzdFBhciI6eyJ1c2VySWQiOiIiLCJjb250ZW50IjoiIn19"},
]);
function getlinkfun(name){
    getlinksApi({linkType:'XRK'}).then(res=>{
       console.log(res);
       res.data.forEach(item => {
        if(item.linkDescription==name){
            linkurl.value = item.linkUrl;
        }
       })
       
    })
}
onMounted(()=>{
    const name = route.query.name;
    getlinkfun(name)
    // if (name) {
    //     const matchedItem = linkArr.value.find(item => item.name === name);
    //     if (matchedItem) {
    //         linkurl.value = matchedItem.pageLink;
    //     }
    // }
})
</script>
<style scoped>
.iframebox{
    height: 100vh;
    width: 100vw;
}
</style>