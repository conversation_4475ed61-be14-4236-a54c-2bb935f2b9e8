import { get, post } from '@/utils/request'
//收藏
export function  setcollectionApi<T = any>(data: any) {
  return post<T>({
    url: '/eaide/collection',
    data,
    headers: {
      'Content-Type': 'application/json',
  },
  })
}
//取消收藏
export function  delcollectionApi<T = any>(data: any) {
  return post<T>({
    url: `/eaide/collection/${data.collectionId}/delete`,
    data,
    headers: {
      'Content-Type': 'application/json',
  },
  })
}

//热度
export function  setheatApi<T = any>(data: any) {
  return post<T>({
    url: `/eaide/heat_scale`,
    data,
    headers: {
      'Content-Type': 'application/json',
  },
  })
}

//应用汇列表
  export function getlistApi<T = any>(data: any) {
    return get<T>({
      url: '/eaide/application_hub/all',
      data,
      headers: {
        'Content-Type': 'application/json',
    },
    })
  }

 