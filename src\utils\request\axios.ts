import axios, { type AxiosResponse } from 'axios'
import { joinTimestamp } from './helper'
import signMd5Utils from '@/utils/encryption/signMd5Utils'
import { isString } from '@/utils/is'
import { ConfigEnum, RequestEnum } from '@/enums/httpEnum'
import { useCrypto } from '@/hooks/useSystem'
// import { useAuthStore } from '@/store'
import { infoStore } from '@/store/modules/info'
import { getToken } from '@/utils/microFrontEnd'
import { router } from '@/router'

const { sm4Decrypt, sm4Encrypt } = useCrypto()
const service = axios.create({
  baseURL: import.meta.env.VITE_GLOB_API_URL,
})

const encryptRequestResponse = import.meta.env.VITE_GLOB_ENCRYPT_REQUEST_RESPONSE

service.interceptors.request.use(
  (config) => {
    const token = getToken()
    if (!token) {
      infoStore().setloading(false)
      router.push('/unauthorized')
      return config
    }
    const device = window.$Eucp?.auth?.getBrowserFingerprint()
    infoStore().setloading(false)
    // if (token)
    config.headers.Authorization = `Bearer ${token}`

    // --update-begin--author:liusq---date:20210831---for:将签名和时间戳，添加在请求接口 Header
    // update-begin--author:taoyan---date:20220421--for: VUEN-410【签名改造】 X-TIMESTAMP牵扯
    config.headers[ConfigEnum.TIMESTAMP] = signMd5Utils.getTimestamp()

    // 携带浏览器指纹
    config.headers[ConfigEnum.DEVICEID] = device
    // update-end--author:taoyan---date:20220421--for: VUEN-410【签名改造】 X-TIMESTAMP牵扯\
    if (config.params)
      config.params[ConfigEnum.TIMESTAMP] = signMd5Utils.getTimestamp()

    config.headers[ConfigEnum.Sign] = signMd5Utils.getSign(config.url, config.params || { [ConfigEnum.TIMESTAMP]: signMd5Utils.getTimestamp() })

    // --update-begin--author:liusq---date:20220325---for: 增加vue3标记
    config.headers[ConfigEnum.VERSION] = 'v3'

    // 请求之前处理config
    const params = config.params || {}
    const data = config.data || false
    if (config.method?.toUpperCase() === RequestEnum.GET) {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(params || {}, joinTimestamp(true, false))
      }
      else {
        // 兼容restful风格
        config.url = `${config.url + params}${joinTimestamp(true, true)}`
        config.params = undefined
      }
    }
    else {
      if (!isString(params)) {
        // formatDate && formatRequestDate(params);
        if (Reflect.has(config, 'data') && config.data && Object.keys(config.data).length > 0) {
          config.data = data
          config.params = params
        }
        else {
          // 非GET请求如果没有提供data，则将params视为data
          config.data = params
          config.params = undefined
        }
        // if (joinParamsToUrl) {
        // 	config.url = setObjToUrlParams(config.url as string, Object.assign({}, config.params, config.data));
        // }
      }
      else {
        // 兼容restful风格
        config.url = config.url + params
        config.params = undefined
      }
    }

    // 检查 Content-Type 是否包含 urlencoded，如果不包含则进行加密
    if (
      encryptRequestResponse === 'true'
      && !config?.headers?.['Content-Type']?.includes('urlencoded')
      && !config?.headers?.['Content-Type']?.includes('multipart/form-data')
      && config.method?.toUpperCase() !== RequestEnum.GET
    ) {
      // 加密请求参数
      // @ts-expect-error
      config.data = sm4Encrypt(JSON.stringify(config.data))
    }

    return config
  },
  (error) => {
    return Promise.reject(error.response)
  },
)

service.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    const contentType = response.headers['content-type']
    if (contentType === 'application/octet-stream') {
      console.log(response)
      return response
    }

    infoStore().setloading(false)
    // 解密白名单
    const decryptWhiteList: any[] = ['/emind/knowledge_base/download', '/emind/knowledge_base/download_test', '/emind/textbook/']
    // // 是否需要解密
    const isDecrypt = decryptWhiteList.some(item => response.config.url?.includes(item))
    if (encryptRequestResponse == 'true' && response.headers['x-eucp-body-type'] != 0 && !isDecrypt) {
      // 解密响应数据
      if (response.data.data) {
        // @ts-expect-error
        response.data.data = JSON.parse(sm4Decrypt(response.data.data))
      }
      else {
        if (typeof response.data === 'string') {
          // @ts-expect-error
          response.data = JSON.parse(sm4Decrypt(response.data))
        }
      }
    }
    if (response.status === 200)
      return response

    throw new Error(response.status.toString())
  },
  (error) => {
    if (error.status == 401)
      window.$EucpAuth?.logout()

    return Promise.reject(error)
  },
)

export default service
