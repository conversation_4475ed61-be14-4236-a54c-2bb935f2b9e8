// 配置面板共享样式
.config-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.config-panel {
  position: fixed;
  top: 0;
  right: -500px;
  width: 540px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  width: 540px;
  padding: 0 16px;
}

.config-panel.panel-open {
  right: 0;
}

.panel-header {
  height: 40px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.node-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.node-name {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  
  img {
    margin-right: 11px;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.run-btn,
.more-btn,
.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.run-btn:hover,
.more-btn:hover,
.close-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.run-btn {
  color: #125eff;
}

.run-btn:hover {
  background: #e6f0ff;
  color: #0d47a1;
}

.run-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.run-btn.running {
  color: #faad14;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.panel-content {
  flex: 1;
  overflow: hidden;
}

.panel-footer {
  padding: 20px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafbfc;
  flex-shrink: 0;
  border-radius: 0 0 0 12px;
  margin: 0 -16px;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  :deep(.n-button) {
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    min-height: 36px;

    &:not([type]) {
      min-width: 88px;
      border: 1px solid #e0e0e0;
      background: #ffffff;
      color: #666666;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #125eff;
        color: #125eff;
        box-shadow: 0 2px 6px rgba(18, 94, 255, 0.15);
      }

      &:active {
        transform: translateY(1px);
      }
    }

    &[type="info"] {
      min-width: 108px;
      box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

.divider {
  width: 100%;
  height: 0.92px;
  background: #0000000f;
  margin-bottom: 20px;
}

// 通用表单样式
:deep(.n-form-item) {
  .n-form-item-label {
    height: 22px;
    min-height: 22px;
    padding-bottom: 0px;
  }
  
  .n-form-item-blank {
    height: 38px;
    
    .n-input {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      .n-input__border {
        display: none;
      }
    }
    
    .n-input__input-el {
      height: 100%;
      background: #f5f5f6;
    }
    
    .n-input-number {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;
    }
  }
}

:deep(.n-select) {
  height: 100%;
  background: #f5f5f6;
  border-radius: 8px;

  .n-base-selection__border {
    display: none;
  }
  
  .n-base-selection {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;

    .n-base-selection-label {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px !important;
    }
  }
}

:deep(.n-form-item-feedback-wrapper) {
  display: none;
}
