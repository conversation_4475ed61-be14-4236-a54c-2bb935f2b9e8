<template>
  <div class="end-node-config">
    <!-- 回复内容配置 -->
    <n-form-item label-placement="left" class="setHeight mb-[9px]">
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 回复内容
        </div>
      </template>
    </n-form-item>

    <!-- 可编辑回复内容输入框 -->
    <n-form-item path="config.huifuneirong" class="seteditable">
      <div class="relative w-full">
        <div
          ref="editableDiv"
          contenteditable="true"
          class="editable-div"
          @keydown="handleKeydown"
          @input="handleInput"
        ></div>
        <button
          class="add-variable-btn"
          @click="showVariableSelector = true"
          type="button"
        >
          + 添加变量
        </button>
      </div>
    </n-form-item>

    <!-- 问题建议配置 -->
    <n-form-item
      path="config.wentijianyi"
      label-placement="left"
      class="setHeight mt-[24px] mb-[17px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 问题建议
        </div>
      </template>
      <div class="flex justify-end w-full">
        <n-switch
          v-model:value="formData.config.wentijianyi"
          @update:value="handleWentijianyiChange"
        />
      </div>
    </n-form-item>

    <!-- 问题建议详细配置 -->
    <div v-if="formData.config.wentijianyi">
      <!-- 引导词配置 -->
      <n-form-item
        path="config.yindaoci"
        class="setrowbottom mb-[16px]"
      >
        <template #label>
          <div class="rowstit">引导词</div>
        </template>
        <n-input
          v-model:value="formData.config.yindaoci"
          type="text"
          placeholder="请输入引导词"
          maxlength="20"
          show-count
          default-value="试试这样问"
        />
      </n-form-item>

      <!-- 问题建议类型选择 -->
      <n-form-item path="config.jianyitype" class="setrowbottom">
        <template #label>
          <div class="rowstit">问题建议</div>
        </template>
        <n-radio-group
          v-model:value="formData.config.jianyitype"
          name="radiogroup"
          default-checked="0"
        >
          <div class="flex items-center">
            <div
              @click="changeJianyiType('0')"
              class="w-[236px] h-[38px] bg-[#F5F5F6] flex items-center pl-[12px] rounded-lg mr-[16px]"
            >
              <n-radio key="0" value="0"> 默认 </n-radio>
            </div>
            <div
              @click="changeJianyiType('1')"
              class="w-[236px] h-[38px] bg-[#F5F5F6] flex items-center pl-[12px] rounded-lg"
            >
              <n-radio key="1" value="1"> 自定义 </n-radio>
            </div>
          </div>
        </n-radio-group>
      </n-form-item>

      <!-- 自定义问题建议列表 -->
      <div v-if="formData.config.jianyitype == '1'">
        <div class="mb-[8px]">
          <n-form-item
            v-for="(item, index) in formData.config.jianyiwenti"
            :key="index"
            label-placement="left"
            class="mt-[12px]"
          >
            <div class="flex items-center h-[38px]">
              <!-- 类型选择 -->
              <div class="w-[114px] mr-[14px] h-full">
                <n-select
                  v-model:value="item.type"
                  :options="variableOptions"
                  filterable
                />
              </div>
              
              <!-- 值输入 -->
              <div class="w-[336px] mr-[6px] h-full">
                <AggregationSelector
                  v-if="item.type == '0'"
                  v-model="item.value"
                  :options="aggregationOptions"
                  placeholder="请选择变量"
                  @change="handleAggregationChange"
                />
                <n-input
                  type="text"
                  placeholder="请输入"
                  maxlength="20"
                  show-count
                  v-if="item.type == '1'"
                  v-model:value="item.value"
                />
              </div>
              
              <!-- 删除按钮 -->
              <img
                @click="deleteProblem(index)"
                class="w-[16px] h-[16px] cursor-pointer"
                src="@/assets/agentOrchestration/delIcon2.png"
              />
            </div>
          </n-form-item>
        </div>

        <!-- 添加问题建议按钮 -->
        <div class="w-[488px] btnparent mt-[12px]">
          <n-button @click="addProblem" dashed>
            <img
              class="w-[9px] mr-[6px]"
              src="@/assets/agentOrchestration/addIcon.png"
            />
            添加问题建议
          </n-button>
        </div>
      </div>
    </div>

    <!-- 变量选择器弹窗 -->
    <n-modal v-model:show="showVariableSelector">
      <n-card
        style="width: 600px"
        title="选择变量"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <VariableSelector
          v-model="selectedVariableForInsert"
          :options="aggregationOptions"
          @select="handleVariableSelect"
          @confirm="insertVariableToPrompt"
          @cancel="showVariableSelector = false"
        />
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { watch, nextTick } from 'vue';
import { 
  NFormItem, 
  NSwitch, 
  NInput, 
  NRadioGroup, 
  NRadio, 
  NSelect, 
  NButton,
  NModal,
  NCard 
} from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import VariableSelector from '../VariableSelector.vue';
import { useNodeConfig } from './composables/useNodeConfig';
import { useVariableSelector } from './composables/useVariableSelector';
import type { NodeConfigProps, NodeConfigEvents, QuestionSuggestion } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const { 
  variableOptions, 
  aggregationOptions, 
  updateAggregationOptions, 
  handleAggregationChange 
} = useNodeConfig(props);

const {
  showVariableSelector,
  selectedVariableForInsert,
  selectedVariableObj,
  editableDiv,
  handleVariableSelect,
  insertVariableToPrompt,
  handleKeydown,
  handleInput,
  renderEditableDiv,
} = useVariableSelector();

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'end') {
      const config = newNode.data.config;
      
      // 初始化默认配置
      if (!config.huifuneirong) {
        config.huifuneirong = "";
      }
      if (config.wentijianyi === undefined) {
        config.wentijianyi = false;
      }
      if (!config.yindaoci) {
        config.yindaoci = "试试这样问";
      }
      if (!config.jianyitype) {
        config.jianyitype = "0";
      }
      if (!config.jianyiwenti) {
        config.jianyiwenti = [];
      }
      
      // 渲染回复内容
      if (config.huifuneirong) {
        nextTick(() => {
          renderEditableDiv(config.huifuneirong);
        });
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听显示状态，更新聚合选项
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.node) {
      updateAggregationOptions();
    }
  },
  { immediate: true }
);

// 处理问题建议开关变更
const handleWentijianyiChange = (value: boolean) => {
  if (value) {
    props.formData.config.jianyitype = "0";
  }
};

// 改变问题建议类型
const changeJianyiType = (type: string) => {
  props.formData.config.jianyitype = type;
};

// 添加问题建议
const addProblem = () => {
  if (!props.formData.config.jianyiwenti) {
    props.formData.config.jianyiwenti = [];
  }
  props.formData.config.jianyiwenti.push({
    type: "0",
    value: "",
  });
};

// 删除问题建议
const deleteProblem = (index: number) => {
  if (props.formData.config.jianyiwenti) {
    props.formData.config.jianyiwenti.splice(index, 1);
  }
};
</script>

<style scoped lang="less">
.end-node-config {
  .rowstit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
    width: 100%;

    .rowicon {
      width: 5px;
      height: 16px;
      background: #abc6ff;
      background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
      border-radius: 3px;
      margin-right: 9px;
    }
  }

  .setHeight {
    :deep(.n-form-item-label) {
      height: 22px !important;
      min-height: 22px;
    }

    :deep(.n-form-item-blank) {
      height: 22px;
      min-height: 22px;
    }
  }

  .setrowbottom {
    :deep(.n-form-item-label) {
      margin-bottom: 9px;
    }
  }

  .seteditable {
    grid-template-rows: 1fr 130px;

    :deep(.n-form-item-blank) {
      height: 130px;
    }
  }

  .editable-div {
    min-height: 130px;
    width: 100%;
    background: #f5f5f6;
    border-radius: 8px;
    padding: 12px;
    outline: none;
    position: relative;
  }

  .add-variable-btn {
    position: absolute;
    right: 16px;
    bottom: 16px;
    z-index: 2;
    background: #125eff;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 4px 12px;
    cursor: pointer;
    font-size: 14px;
  }

  .btnparent {
    :deep(.n-button) {
      width: 100%;
    }
  }

  :deep(.n-radio-group) {
    width: 100%;
  }
}
</style>
