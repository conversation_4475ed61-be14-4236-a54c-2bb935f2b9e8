<template>
  <div class="nested-list">
    <div
        v-for="(item, index) in items"
        :key="index"
        class="item-container"

    >
      <div
          class="bg-[#F1F4FE] w-full mb-[16px] px-[18px] py-[20px] cursor-pointer rounded-[8px] relative center-item"
          @click.stop="toggleExpand(item)"
      >
        <!-- 展开/折叠按钮 - 有子级目录或知识点时都显示 -->
        <button
            v-if="hasContentToExpand(item)"
            aria-label="展开/折叠"
            class="expand-btn absolute left-[12px] top-[50%] mt-[-9.5px]"
        >
          <svg
              :class="item.isExpanded ? 'rotate-90' : ''"
              fill="none"
              height="16"
              viewBox="0 0 24 24"
              width="16"
              xmlns="http://www.w3.org/2000/svg"
          >
            <path
                d="M9 5L16 12L9 19"
                stroke="#666666"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
            />
          </svg>
        </button>

        <p
            :style="{ paddingLeft: hasContentToExpand(item) ? '16px' : '0' }"
            class="text-[14px] font-semibold"
            @click="positionTap(item)"
        >
          {{ item.name }}
        </p>

        <!--				<n-popover-->
        <!--					placement="left"-->
        <!--					trigger="click"-->
        <!--					@update:show="(flag:boolean)=>popoverShow(flag,item)"-->
        <!--				>-->
        <!--					<template #trigger>-->
        <!--						<img-->
        <!--							alt="更多操作"-->
        <!--							class="w-4 h-4 absolute right-[16px] top-[50%] mt-[-8px] invisible center-r"-->
        <!--							src="@/assets/toolboxPage/center-r.png"-->
        <!--						/>-->
        <!--					</template>-->
        <!--					<div-->
        <!--						v-if="item.childrenList?.length"-->
        <!--						class="flex flex-col"-->
        <!--					>-->
        <!--						<n-button-->
        <!--							v-for="(i, dex) in item.childrenList"-->
        <!--							:key="dex"-->
        <!--							class="children"-->
        <!--							text-->
        <!--							@click="goTap(i)"-->
        <!--						>-->
        <!--							<template #icon>-->
        <!--								<img-->
        <!--									:src="-->
        <!--                    i.textbookType == 0-->
        <!--                      ? jc-->
        <!--                      : i.textbookType == 1-->
        <!--                      ? kj-->
        <!--                      : sp-->
        <!--                  "-->
        <!--									alt=""-->
        <!--									class="children-img"-->
        <!--								/>-->
        <!--								<img-->
        <!--									:src="-->
        <!--                    i.textbookType == 0-->
        <!--                      ? jca-->
        <!--                      : i.textbookType == 1-->
        <!--                      ? kja-->
        <!--                      : spa-->
        <!--                  "-->
        <!--									alt=""-->
        <!--									class="children-imga"-->
        <!--								/>-->
        <!--							</template>-->
        <!--							{{ i.title }}-->
        <!--						</n-button>-->
        <!--					</div>-->
        <!--					<div v-else class="">-->
        <!--						<n-spin v-if="!item.childrenFlag" size="medium"/>-->
        <!--						<n-empty v-else description="暂无数据"></n-empty>-->
        <!--					</div>-->
        <!--				</n-popover>-->
      </div>

      <!-- 展开内容区域 - 包含目录和知识点，同一层级展示 -->
      <div
          v-if="item.isExpanded && hasContentToExpand(item)"
          class="expanded-content pl-[16px]"
      >
				<!-- 再展示知识点项，使用不同样式区分 -->
				<div v-for="(point, pointIndex) in item.knowledgePoints || []" :key="'point_' + pointIndex" class="mt-2">
					<div
						class="bg-[#E8F4FD] w-full mb-[16px] px-[18px] py-[20px] cursor-pointer rounded-[8px] relative center-item knowledge-point"
					>
						<div class="flex w-full items-center">
							<img :src="zd" alt="" class="w-[18px] h-[18px] mr-[6px]"/>

							<p class="text-[14px] font-semibold" @click="positionTap(point)">
								{{ point.content || point.title }} <!-- 假设知识点有content或title字段 -->
							</p>
						</div>


						<!-- 知识点可以有自己的操作按钮 -->
						<n-popover
							placement="left"
							trigger="click"
							@update:show="(flag:boolean)=>popoverShow(flag,point)"
						>
							<template #trigger>
								<img
									alt="更多操作"
									class="w-4 h-4 absolute right-[16px] top-[50%] mt-[-8px] invisible center-r"
									src="@/assets/toolboxPage/center-r.png"
								/>
							</template>
							<div
								v-if="point.childrenList?.length"
								class="flex flex-col"
							>
								<n-button
									v-for="(i, dex) in point.childrenList"
									:key="dex"
									class="children"
									text
									@click="goTap(i)"
								>
									<template #icon>
										<img
											:src="
                    i.textbookType == 0
                      ? jc
                      : i.textbookType == 1
                      ? kj
                      : sp
                  "
											alt=""
											class="children-img"
										/>
										<img
											:src="
                    i.textbookType == 0
                      ? jca
                      : i.textbookType == 1
                      ? kja
                      : spa
                  "
											alt=""
											class="children-imga"
										/>
									</template>
									{{ i.title }}
								</n-button>
							</div>
							<div v-else class="">
								<n-spin v-if="!point.childrenFlag" size="medium"/>
								<n-empty v-else description="暂无数据"></n-empty>
							</div>
						</n-popover>
					</div>

					<!-- 如果知识点本身还有子级，递归展示 -->
					<div v-if="point.isExpanded && hasContentToExpand(point)" class="pl-[16px]">
						<NestedList
							:items="[point]"
							:jc="jc"
							:jca="jca"
							:kj="kj"
							:kja="kja"
							:sp="sp"
							:spa="spa"
							@goTap="goTap"
							@popoverShow="popoverShow"
							@positionTap="positionTap"
						/>
					</div>
				</div>

        <!-- 先展示目录项 -->
        <div v-for="(child, childIndex) in item.children || []" :key="'child_' + childIndex" class="mt-2">
          <NestedList
              :items="[child]"
              :jc="jc"
              :jca="jca"
              :kj="kj"
              :kja="kja"
              :sp="sp"
              :spa="spa"
              @goTap="goTap"
              @popoverShow="popoverShow"
              @positionTap="positionTap"
          />
        </div>


      </div>
    </div>
  </div>
</template>

<script setup>
import {defineProps, defineEmits, ref} from 'vue';
import {
  NSpin,
  NStep,
  NSteps,
  NEllipsis,
  NDropdown,
  NButton,
  NBadge,
  NTabs,
  NTabPane,
  NPopover,
  NBreadcrumb,
  NBreadcrumbItem,
  NEmpty,
  useMessage,
} from "naive-ui";
import zd from "@/assets/chat/zd.png";

// 定义组件名称，用于递归调用
defineOptions({
  name: 'NestedList'
});

// 定义接收的props
const props = defineProps({
  items: {
    type: Array,
    required: true,
    default: () => []
  },
  jc: {
    type: String,
    required: true
  },
  kj: {
    type: String,
    required: true
  },
  sp: {
    type: String,
    required: true
  },
  jca: {
    type: String,
    required: true
  },
  kja: {
    type: String,
    required: true
  },
  spa: {
    type: String,
    required: true
  }
});

// 定义发出的事件
const emit = defineEmits([
  'positionTap',
  'goTap',
  'popoverShow',
  'knowledgePointTap',
  'handleKnowledgePointAction'
]);

// 判断是否有可展开的内容（目录或知识点）
const hasContentToExpand = (item) => {
  return (item.children && item.children.length > 0) ||
      (item.knowledgePoints && item.knowledgePoints.length > 0);
};

// 展开/折叠切换方法
const toggleExpand = (item) => {
  if (item.isExpanded === undefined) {
    item.isExpanded = true;
  } else {
    item.isExpanded = !item.isExpanded;
  }
};

// 透传事件
const positionTap = (item) => {
  emit('positionTap', item);
};

const goTap = (i) => {
  emit('goTap', i);
};

const popoverShow = (flag, item) => {
  emit('popoverShow', flag, item);
};

// 知识点点击事件
const knowledgePointTap = (point) => {
  emit('knowledgePointTap', point);
};

// 知识点操作事件
const handleKnowledgePointAction = (point) => {
  emit('handleKnowledgePointAction', point);
};
</script>

<script>
export default {
  name: 'NestedList'
}
</script>
<style lang="less" scoped>
.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.children-container {
  /* 子级缩进，区分层级 */
  border-left: 1px dashed #ccc;
  margin-left: 8px;
}

/* 确保图标垂直居中 */
.fa {
  font-size: 12px;
  transition: transform 0.2s ease;
}


.contentList {
  height: calc(100vh - 424px);
}

.full-height {
  height: calc(100vh - 100px);
}

.full-jiaocai {
  height: calc(100vh - 180px);
}

:deep(.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link) {
  color: #125eff;
}

.children {
  justify-content: left;
}

.children:hover .children-img {
  display: none;
}

.children-imga {
  display: none;
}

.children:hover .children-imga {
  display: block;
}

.center-item {
  transition: 0.25s;

  p {
    transition: 0.25s;
  }

  &:hover {
    background: #fff;
  }
}

.center-item:hover p {
  color: #125eff;
}

.center-item:hover .center-r {
  visibility: visible;
  opacity: 1;
}

.center-r {
  transition: 0.25s;
  opacity: 0;
}

:deep(.n-tabs .n-tabs-rail) {
  background: #f1f4fe;
}

:deep(.n-tabs-tab--active) {
  .n-tabs-tab__label {
    color: #125eff;
    font-weight: bold;
  }
}

.chatbg {
  padding-top: 30px;
  // height: 100vh;
}

.leftbox {
  width: 50%;
}

.rightbox {
  width: 50%;
}

.headers {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  // margin-bottom: 90px;

  .left {
    color: #323233;
    font-size: 20px;
    font-weight: 500;
    line-height: 0;
    letter-spacing: 0;
    line-height: 40px;
    display: flex;
    align-items: center;
    margin-left: 24px;

    .gohome {
      width: 40px;
      height: 40px;
      background: #fafbff;
      border: 1px solid #e9ecf3;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 11px;

      img {
        width: 20px;
        height: 20px;
      }
    }

    span {
      color: #909399;
      margin-left: 8px;
    }
  }
}

.documentbg {
  // width: 796px;
  width: 63.5%;
  height: calc(100vh - 100px);
  background: #ffffff;
  box-shadow: 0 0 10px 0 #00000021;
  border-radius: 4px;
  //margin-left: 24px;
  //margin-right: 24px;
  //margin-top: 24px;
  //margin-bottom: 24px;
  overflow-y: auto;
}

.autoheight {
  width: 50%;
  height: 85vh;
  padding-left: 24px;
  padding-right: 32px;
  /* 允许内容溢出时滚动 */
  overflow-y: auto;
  /* 隐藏 WebKit 浏览器的滚动条 */
  scrollbar-width: none;
  /* 隐藏 Firefox 的滚动条 */
  -ms-overflow-style: none;
}

.autoheight::-webkit-scrollbar {
  display: none;
}

/* 优化后的平滑动画效果 */
.slide-fade-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}
</style>
