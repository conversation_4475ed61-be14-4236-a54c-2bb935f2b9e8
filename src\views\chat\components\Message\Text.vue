<script lang="ts" setup>
import { computed, onMounted, onUnmounted, onUpdated, ref, useTemplateRef, watch } from 'vue'
import html2pdf from 'html2pdf.js'
import MarkdownIt from 'markdown-it'
import MdKatex from '@vscode/markdown-it-katex'
import MdLinkAttributes from 'markdown-it-link-attributes'
import MdMermaid from 'mermaid-it-markdown'
import hljs from 'highlight.js'
import xss from 'xss'
import { NTooltip, useMessage } from 'naive-ui'
import KnowledgeBase from './KnowledgeBase.vue'
import { useChatStore, useToolsStore, usestretchoutStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'
import { copyToClip } from '@/utils/copy'
import { exprot } from '@/api/tankChat'

interface Props {
  inversion?: boolean
  error?: boolean
  text?: string
  loading?: boolean
  asRawText?: boolean
  isPlay?: boolean
  topic?: string
  answerList: any
  endstatus: any
  conversationContentId: string
}

const props = defineProps<Props>()
/**********/
const emit = defineEmits(['loadTap', 'handleCopy', 'handleSound', 'handleStopSound', 'handleRegenerate'])
const useTretchOut = usestretchoutStore()
const ToolsStore = useToolsStore()
const ChatStore = useChatStore()
const message = useMessage()

const copy = () => {
  emit('handleCopy')
}
const simulationtestfun = () => {
  useTretchOut.setconversationContentId(props.conversationContentId)
}
const lessonTap = () => {
  console.log(props.text)
  useTretchOut.setContentId(props.conversationContentId, true, ChatStore.markdownToPlainText(props.text))
}
const sound = () => {
  emit('handleSound')
}
const regenerate = () => {
  emit('handleRegenerate')
}
const stopSound = () => {
  emit('handleStopSound')
}
/**********/
const { isMobile } = useBasicLayout()

const textRef = ref<HTMLElement>()
// 逐字打印相关变量
const displayedText = ref<string>('') // 当前显示的文本
const originalText = ref<string>('') // 完整的原始文本
const remainText = ref<string>('') // 剩余待显示文本
const isAnimating = ref<boolean>(false) // 是否正在动画中
const isFinished = ref<boolean>(false) // 是否已完成动画
const isStreamReceiving = ref<boolean>(false) // 是否正在接收流式数据
let lastProcessedLength = 0 // 上次处理的文本长度，用于判断是否有新内容
let animationFrameId: number | null = null // 动画帧ID

// 思考内容的逐字显示相关变量
const displayedThinkText = ref<string>('') // 当前显示的思考文本
const originalThinkText = ref<string>('') // 原始完整的思考文本
const hasThinkContent = ref<boolean>(false) // 是否包含思考内容
const mdi = new MarkdownIt({
  html: true,
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  },
})

mdi.use(MdLinkAttributes, { attrs: { target: '_blank', rel: 'noopener' } }).use(MdKatex).use(MdMermaid)

const wrapClass = computed(() => {
  return [
    'text-wrap',
    'min-w-[20px]',
    // 'rounded-md',
    'rounded-[12px]',
    isMobile.value ? 'p-2' : 'px-3 py-2',
    props.inversion ? 'px-4 py-3' : 'px-8 py-7',
    props.inversion ? 'bg-[#E0ECFD]' : 'bg-[#FFFFFF]',
    props.inversion ? 'dark:bg-[#a1dc95]' : 'dark:bg-[#1e1e20]',
    props.inversion ? 'message-request' : 'message-reply',
    { 'text-red-500': props.error },
  ]
})

// 深度思考内容
const thinkContent = computed(() => {
  if (!hasThinkContent.value)
    return ''

  // 使用动画中的思考文本或完整文本
  const textToRender = (isAnimating.value && !isFinished.value) ? displayedThinkText.value : originalThinkText.value

  return mdi.render(textToRender)
})

// 文本处理和渲染，修改为使用displayedText
const processedText = computed(() => {
  let value = ''
  if (!props.loading)
    value = props.text || ''
  else
    value = props.inversion ? (props.text ?? '') : displayedText.value

  value = value.replace(/四川大学/g, '四川大学')

  const match = value.match(/<think>([\s\S]*?)<\/think>/)
  if (match) {
    value = value.replace(/<think>([\s\S]*?)<\/think>/, '').trim()
  }
  else {
    const partialMatch = value.match(/<think>([\s\S]*)/)
    if (partialMatch)
      value = ''
  }

  if (!props.asRawText) {
    // 对数学公式进行处理，自动添加 $$ 符号
    const escapedText = escapeBrackets(escapeDollarNumber(value))
    return mdi.render(escapedText)
  }
  return mdi.render(value)
})

// 监听text属性变化，处理新文本
watch(() => props.text, (newText, oldText) => {
/*   console.log('[Text] text属性变化:', newText, oldText)
  if (props.inversion || props.loading) {
    // 用户消息或加载中状态，直接显示完整文本
    displayedText.value = newText || ''
    return
  }
*/
  if (!props.loading)
    return

  if (!newText) {
    // 文本为空，重置状态
    resetTextAnimation()
    return
  }

  // 检查是否为新的对话（文本完全变化）
  const isNewConversation = !oldText || oldText.length === 0 || !newText.startsWith(oldText)
  if (isNewConversation) {
    console.log('[Text-isNewConversation] 新的对话，重置并开始新动画')
    // 新的对话，重置并开始新动画
    resetTextAnimation()
    // 提取并设置思考内容
    extractThinkContent(newText)
    startTextAnimation(newText)
  }
  else if (newText !== oldText) {
    // 流式数据更新，追加新内容
    extractThinkContent(newText)
    updateStreamContent(newText, oldText || '')
  }
}, { immediate: true })

// 监听loading状态变化
watch(() => props.loading, (isLoading) => {
  console.log('[Text] Loading状态变化:', isLoading)
  /*
  if (isLoading) {
    // 开始加载，重置状态
    resetTextAnimation()
  }
  else if (!props.inversion && props.text) {
    // 加载完成，可能是非流式返回的情况，确保动画正确开始
    if (!isAnimating.value) {
      console.log('[Text] 开始非流式文本动画')
      startTextAnimation(props.text)
    }
  } */
})

// 重置动画状态
function resetTextAnimation() {
  console.log('[Text] 重置动画状态')
  stopTextAnimation()
  displayedText.value = ''
  originalText.value = ''
  remainText.value = ''
  displayedThinkText.value = '' // 重置思考内容显示
  originalThinkText.value = '' // 重置原始思考内容
  hasThinkContent.value = false // 重置思考内容标志
  isAnimating.value = false
  isFinished.value = false
  isStreamReceiving.value = false
  lastProcessedLength = 0
}

// 提取思考内容
function extractThinkContent(text: string) {
  const value = text ? text.replace(/四川大学/g, '四川大学') : ''

  // 清除之前的思考内容状态，除非已经有完整的思考内容
  if (!originalThinkText.value || !originalThinkText.value.includes('</think>')) {
    hasThinkContent.value = false
    originalThinkText.value = ''
  }
  else {
    // 已经有完整的思考内容，不再更新
    return
  }

  // 完整的<think>标签
  const match = value.match(/<think>([\s\S]*?)<\/think>/)
  if (match) {
    const thinkText = match[1]
    originalThinkText.value = `${escapeBrackets(escapeDollarNumber(thinkText))}</think>` // 标记为完整思考内容
    hasThinkContent.value = true
    return
  }

  // 不完整的<think>标签（可能是流式传输中）
  const partialMatch = value.match(/<think>([\s\S]*)/)
  if (partialMatch) {
    const thinkText = partialMatch[1]
    originalThinkText.value = escapeBrackets(escapeDollarNumber(thinkText))
    hasThinkContent.value = true
  }
}

// 开始文字动画
function startTextAnimation(fullText: string) {
  console.log('[Text] 开始文字动画, 文本长度:', fullText?.length)
  // 停止之前的动画
  stopTextAnimation()

  // 初始化动画状态
  const plainText = fullText || ''
  originalText.value = plainText
  displayedText.value = ''
  remainText.value = plainText
  displayedThinkText.value = '' // 初始化思考内容显示为空
  isAnimating.value = true
  isFinished.value = false

  // 启动动画
  animateResponseText()
}

// 更新流式内容
function updateStreamContent(newText: string, oldText: string) {
  if (!newText || newText === oldText)
    return

  // 获取新增的内容
  const addedContent = newText.slice(oldText.length)
  if (!addedContent)
    return
  // 更新原始文本
  originalText.value = newText

  // 如果动画正在进行，将新内容添加到待显示文本
  if (isAnimating.value && !isFinished.value) {
    remainText.value += addedContent
    isStreamReceiving.value = true
  }
  else {
    // 如果动画已经结束或未开始，则开始新的动画
    displayedText.value = oldText // 保留已显示的内容
    remainText.value = addedContent
    isAnimating.value = true
    isFinished.value = false
    isStreamReceiving.value = true

    if (!animationFrameId)
      animateResponseText()
  }
}

// 动画函数
function animateResponseText() {
  // 如果请求被中止或动画已完成且无新内容，则结束动画
  if (isFinished.value && !isStreamReceiving.value) {
    console.log('[Text] 动画完成')
    displayedText.value = originalText.value
    displayedThinkText.value = originalThinkText.value.replace('</think>', '') // 设置完整思考内容并移除结束标记
    animationFrameId = null
    return
  }

  if (remainText.value.length > 0) {
    // 动态计算每帧处理的字符数，根据剩余文本长度和是否在接收流调整速度
    let fetchCount = Math.max(1, Math.round(remainText.value.length / 60))

    // 如果正在接收流数据，可以适当减慢速度，提供更平滑的体验
    if (isStreamReceiving.value)
      fetchCount = Math.max(1, Math.min(fetchCount, 3)) // 限制每帧最多处理3个字符

    const fetchText = remainText.value.slice(0, fetchCount)
    displayedText.value += fetchText
    remainText.value = remainText.value.slice(fetchCount)

    // 同步更新思考内容的显示进度，但仅当思考内容未完成时
    if (hasThinkContent.value && originalThinkText.value) {
      // 检查思考内容是否已经完整（包含结束标签）
      const isThinkComplete = originalThinkText.value.includes('</think>')

      if (isThinkComplete && displayedThinkText.value.length <= originalThinkText.value.replace('</think>', '').length) {
        // 思考内容已完整且已显示完全，不再更新
        displayedThinkText.value = originalThinkText.value.replace('</think>', '')
      }
      else if (!isThinkComplete) {
        // 思考内容仍在接收中，正常更新显示进度
        const thinkProgress = Math.min(1, displayedText.value.length / originalText.value.length)
        const thinkLength = Math.floor(originalThinkText.value.length * thinkProgress)
        displayedThinkText.value = originalThinkText.value.slice(0, thinkLength)
      }
    }

    // 如果处理完当前所有文本，但可能还在接收新内容
    if (remainText.value.length === 0) {
      // 检查一段时间内是否有新内容到达
      if (lastProcessedLength === originalText.value.length) {
        console.log('[Text] 流接收结束')
        isStreamReceiving.value = false // 无新内容，认为流接收结束
      }

      lastProcessedLength = originalText.value.length

      if (!isStreamReceiving.value) {
        isFinished.value = true // 确认完成
        // 确保思考内容完整显示
        displayedThinkText.value = originalThinkText.value.replace('</think>', '')
      }
    }
  }
  else if (isStreamReceiving.value) {
    // 当前无内容但仍在接收流，等待新内容
  }
  else {
    // 文本已全部显示且不再接收新内容
    console.log('[Text] 动画结束')
    isAnimating.value = false
    isFinished.value = true
    // 确保思考内容完整显示
    displayedThinkText.value = originalThinkText.value.replace('</think>', '')
    animationFrameId = null
    return
  }

  // 继续下一帧
  animationFrameId = requestAnimationFrame(animateResponseText)
}

// 停止动画
function stopTextAnimation() {
  console.log('[Text] 停止动画')
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }

  // 如果动画被中断，直接显示完整文本
  if (isAnimating.value && !isFinished.value) {
    displayedText.value = originalText.value
    displayedThinkText.value = originalThinkText.value.replace('</think>', '') // 显示完整思考内容并移除结束标记
    isAnimating.value = false
    isFinished.value = true
    isStreamReceiving.value = false
  }
}
function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">${t('chat.copyCode')}</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

function addCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy')
    copyBtn.forEach((btn) => {
      btn.addEventListener('click', () => {
        const code = btn.parentElement?.nextElementSibling?.textContent
        if (code) {
          copyToClip(code).then(() => {
            btn.textContent = t('chat.copied')
            setTimeout(() => {
              btn.textContent = t('chat.copyCode')
            }, 1000)
          })
        }
      })
    })
  }
}

function removeCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy')
    copyBtn.forEach((btn) => {
      btn.removeEventListener('click', () => {
      })
    })
  }
}

function escapeDollarNumber(text: string) {
  let escapedText = ''

  for (let i = 0; i < text.length; i += 1) {
    let char = text[i]
    const nextChar = text[i + 1] || ' '

    if (char === '$' && nextChar >= '0' && nextChar <= '9')
      char = '\\$'

    escapedText += char
  }

  return escapedText
}

function escapeBrackets(text: string) {
  const pattern = /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g
  return text.replace(pattern, (match, codeBlock, squareBracket, roundBracket) => {
    if (codeBlock)
      return codeBlock
    else if (squareBracket)
      return `$$${squareBracket}$$`
    else if (roundBracket)
      return `$${roundBracket}$`
    return match
  })
}

const pdfRef = useTemplateRef('pdfRef')
async function exportEl() {
  emit('loadTap', true)
  try {
    const contentMain = pdfRef.value
    if (!contentMain) {
      message.error('未找到要导出的内容')
      return
    }

    // 配置PDF选项
    const options = {
      margin: [10, 10, 10, 10], // 上右下左边距(毫米)
      filename: `教学设计方案_${new Date().toLocaleDateString('zh-CN')}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2, // 提高清晰度
        useCORS: true,
        allowTaint: true,
      },
      jsPDF: {
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait',
      },
    }

    // message.info('正在生成PDF，请稍候...')

    // 生成并下载PDF
    await html2pdf().set(options).from(contentMain).save()

    message.success('PDF下载成功')
    emit('loadTap', false)
  }
  catch (error) {
    // console.error('PDF生成失败:', error)
    message.error('PDF生成失败，请重试')
    emit('loadTap', false)
  }
}

const exportQuestions = async (msg: any, uri: string) => {
  const response = await exprot(uri, {
    input: msg,
  })
  // console.log(response, response.data)
  const blob = response.data
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url

  // 设置下载文件名，建议从响应头获取更精准的文件名
  link.download = uri === 'questions_export_word' ? `试卷内容_${new Date().toLocaleDateString('zh-CN')}.docx` : `考题内容_${new Date().toLocaleDateString('zh-CN')}.xlsx`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url) // 释放 URL
}

onMounted(() => {
  addCopyEvents()
})

onUpdated(() => {
  addCopyEvents()
})

onUnmounted(() => {
  stopTextAnimation()
  removeCopyEvents()
})
</script>

<template>
  <div ref="pdfRef" class="text-black" :class="wrapClass">
    <div class="leading-relaxed break-words">
      <div v-if="!inversion">
        <div
          v-if="thinkContent"
          class=" text-[14px] text-[#909399]  border-[#3232334d;] mb-[24px] leading-[28px]"
          :class="{ 'markdown-body-typing': isAnimating && !isFinished }"
          v-html="xss(thinkContent)"
        />
        <div
          v-if="!asRawText" ref="textRef" class="markdown-body" :class="{
            'markdown-body-generate': loading,
            'markdown-body-typing': isAnimating && !isFinished,
          }" v-html="processedText"
        />
        <div v-else class="whitespace-pre-wrap" v-text="text" />
        <div class="flex items-center gap-2 mt-5">
          <!--          <NTooltip trigger="hover"> -->
          <!--            <template #trigger> -->
          <!--              <img -->
          <!--                v-show="!isPlay" class="w-[22px] h-[22px] cursor-pointer" src="../../../../assets/chat/sound.png" alt="" -->
          <!--                @click="sound()" -->
          <!--              > -->
          <!--            </template> -->
          <!--            播放 -->
          <!--          </NTooltip> -->

          <!-- <NTooltip trigger="hover">
            <template #trigger>
              <img
                v-show="isPlay" src="../../../../assets/chat/player.png" class="w-[22px] h-[22px] cursor-pointer" alt=""
                @click="stopSound()"
              >
            </template>
            停止
          </NTooltip> -->

          <!-- <NTooltip trigger="hover">
            <template #trigger>
              <img class="w-[22px] h-[22px] cursor-pointer" src="../../../../assets/chat/copy.png" alt="" @click="copy()">
            </template>
            复制
          </NTooltip> -->

          <NTooltip v-if="props.conversationContentId && !props.loading" trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer" @click="regenerate()">
                <img class="w-[20px] h-[20px] " src="../../../../assets/regenerate.png">
              </div>
            </template>
            重新生成
          </NTooltip>
          <NTooltip v-if="ToolsStore.ToolInfo.category == 3 && props.conversationContentId && !props.loading" trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer" @click="simulationtestfun()">
                <img class="w-[20px] h-[20px] " src="../../../../assets/ceyan.png">
              </div>
            </template>
            进行模拟测验
          </NTooltip>
          <NTooltip v-if="ToolsStore.ToolInfo.category == 7 && !props.loading && props.topic === '1'" trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer" @click="exportQuestions(props.text, 'questions_export')">
                <img class="w-[20px] h-[20px] " src="../../../../assets/export.png">
              </div>
            </template>
            以此导出考题文档附件
          </NTooltip>
          <NTooltip v-if="ToolsStore.ToolInfo.category == 7 && !props.loading && props.topic === '2'" trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer" @click=" exportQuestions(props.text, 'questions_export_word')">
                <img class="w-[20px] h-[20px] " src="../../../../assets/shijuan.png">
              </div>
            </template>
            以此导出试卷文档附件
          </NTooltip>
          <NTooltip v-if="ToolsStore.ToolInfo.category == 8 && !props.loading" trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer" @click="lessonTap()">
                <img class="w-[20px] h-[20px] " src="../../../../assets/edit.png">
              </div>
            </template>
            深入编辑
          </NTooltip>
          <NTooltip v-if="ToolsStore.ToolInfo.category == 8 && !props.loading" trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer" @click="exportEl()">
                <img class="w-[20px] h-[20px] " src="../../../../assets/export.png">
              </div>
            </template>
            以此导出教案文档附件
          </NTooltip>
          <!-- <NTooltip trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer"  @click="copy()">
              <img class="w-[20px] h-[20px] " src="../../../../assets/exporticon.png">
              </div>
            </template>
            以此导出教案文档附件
          </NTooltip> -->
        </div>
        <div>
          <KnowledgeBase v-if="answerList?.length > 0" :answer-list="answerList" />
        </div>
      </div>
      <div v-else class="whitespace-pre-wrap" v-text="text" />
    </div>
  </div>
</template>

<style lang="less">
@import url(./style.less);
.iconbox{
  width: 30px;
  height: 30px;
  background: #E9F1FF;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}
.iconbox:hover{
  border: 1px solid #125EFF;
}
</style>
