import { ref, nextTick } from 'vue';
import { useMessage } from 'naive-ui';
import { useOrchestrationStore } from '@/store';

export function useVariableSelector() {
  const message = useMessage();
  const orchestrationStore = useOrchestrationStore();

  // 变量选择器相关
  const showVariableSelector = ref(false);
  const selectedVariableForInsert = ref("");
  const selectedVariableObj = ref({});

  // 可编辑div相关
  const editableDiv = ref<HTMLElement | null>(null);
  const savedRange = ref<Range | null>(null);

  // 处理变量选择
  const handleVariableSelect = (variable: any) => {
    console.log("选中变量:", variable);
    selectedVariableObj.value = variable;
  };

  // 保存光标位置
  const saveCursorPosition = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      savedRange.value = selection.getRangeAt(0).cloneRange();
    }
  };

  // 恢复光标位置
  const restoreCursorPosition = () => {
    if (savedRange.value) {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(savedRange.value);
      }
    }
  };

  // 获取焦点的辅助函数
  const focusEditableDiv = () => {
    if (editableDiv.value) {
      editableDiv.value.focus();
      const selection = window.getSelection();
      const range = document.createRange();
      range.selectNodeContents(editableDiv.value);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }
  };

  // 创建变量div的辅助函数
  const createVariableDiv = (variableName?: string, variableId?: string) => {
    const btn = document.createElement("div");
    btn.innerText = variableName || "变量名称";
    btn.setAttribute("contenteditable", "false");
    btn.setAttribute("data-id", variableId || "");

    // 样式设置
    btn.style.display = "inline-block";
    btn.style.padding = "2px 14px";
    btn.style.margin = "0 4px";
    btn.style.background = "#125EFF";
    btn.style.border = "none";
    btn.style.color = "#fff";
    btn.style.borderRadius = "8px";
    btn.style.fontSize = "15px";
    btn.style.fontWeight = "500";
    btn.style.cursor = "pointer";
    btn.style.outline = "none";
    btn.style.lineHeight = "22px";
    btn.style.height = "28px";
    btn.style.boxShadow = "none";
    btn.style.transition = "background 0.2s";

    // 悬浮效果
    btn.onmouseenter = () => {
      btn.style.background = "#0d47a1";
    };
    btn.onmouseleave = () => {
      btn.style.background = "#125EFF";
    };

    return btn;
  };

  // 在保存的位置插入节点
  const insertNodeAtSavedPosition = (node: Node) => {
    if (!savedRange.value) return;

    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(savedRange.value);
    }

    savedRange.value.insertNode(node);
    savedRange.value.setStartAfter(node);
    savedRange.value.collapse(true);

    if (selection) {
      selection.removeAllRanges();
      selection.addRange(savedRange.value);
    }

    savedRange.value = null;

    if (editableDiv.value) editableDiv.value.focus();
    setTimeout(() => {
      editableDiv.value && editableDiv.value.focus();
    }, 0);
  };

  // 在光标位置插入节点
  const insertNodeAtCursor = (node: Node) => {
    const sel = window.getSelection();
    if (!sel || !sel.rangeCount) return;
    
    const range = sel.getRangeAt(0);
    range.collapse(false);
    range.insertNode(node);
    range.setStartAfter(node);
    range.collapse(true);
    sel.removeAllRanges();
    sel.addRange(range);
    
    if (editableDiv.value) editableDiv.value.focus();
    setTimeout(() => {
      editableDiv.value && editableDiv.value.focus();
    }, 0);
  };

  // 插入变量到提示词
  const insertVariable = (variableName?: string, variableId?: string) => {
    const el = editableDiv.value;
    if (!el) return;

    const btn = createVariableDiv(variableName, variableId);

    if (savedRange.value) {
      insertNodeAtSavedPosition(btn);
    } else {
      insertNodeAtCursor(btn);
    }
  };

  // 插入变量到提示词的主函数
  const insertVariableToPrompt = async () => {
    if (!selectedVariableForInsert.value) return;
    const variableSyntax = selectedVariableForInsert.value;

    nextTick(() => {
      focusEditableDiv();
      insertVariable(variableSyntax, selectedVariableObj.value.id);
      
      showVariableSelector.value = false;
      selectedVariableForInsert.value = "";
      selectedVariableObj.value = {};

      message.success("变量已插入");
    });
  };

  // 处理键盘输入
  const handleKeydown = (e: KeyboardEvent) => {
    console.log(e.key);
  };

  // 处理输入事件，检测{}
  const handleInput = (e: Event) => {
    const target = e.target as HTMLElement;
    const text = target.textContent || "";

    if (text.includes("{}")) {
      nextTick(() => {
        const selection = window.getSelection();
        if (!selection || !selection.rangeCount) return;

        const range = selection.getRangeAt(0);
        const startOffset = range.startOffset;
        const textNode = range.startContainer;
        
        if (textNode.nodeType === Node.TEXT_NODE) {
          const textContent = textNode.textContent || "";
          const beforeCursor = textContent.substring(0, startOffset);
          const afterCursor = textContent.substring(startOffset);

          let newBeforeCursor = beforeCursor;
          let newAfterCursor = afterCursor;
          let hasReplacement = false;

          if (beforeCursor.endsWith("{}")) {
            newBeforeCursor = beforeCursor.slice(0, -2);
            hasReplacement = true;
          }

          if (afterCursor.startsWith("{}")) {
            newAfterCursor = afterCursor.slice(2);
            hasReplacement = true;
          }

          if (beforeCursor.endsWith("{") && afterCursor.startsWith("}")) {
            newBeforeCursor = beforeCursor.slice(0, -1);
            newAfterCursor = afterCursor.slice(1);
            hasReplacement = true;
          }

          if (hasReplacement) {
            textNode.textContent = newBeforeCursor + newAfterCursor;

            const newRange = document.createRange();
            newRange.setStart(textNode, newBeforeCursor.length);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            saveCursorPosition();
            showVariableSelector.value = true;
          }
        }
      });
    }
  };

  // 渲染可编辑div内容
  const renderEditableDiv = (content: string) => {
    if (!content || !editableDiv.value) return;
    
    editableDiv.value.innerHTML = '';
    focusEditableDiv();
    
    const fragment = document.createDocumentFragment();
    const regex = /\{\{([^{}]+)\}\}/g;
    let lastIndex = 0;
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        const textNode = document.createTextNode(content.substring(lastIndex, match.index));
        fragment.appendChild(textNode);
      }
      
      const variableId = match[1].trim();
      const variableobj = orchestrationStore.getVariableById(variableId);
      const variableDiv = createVariableDiv(variableobj?.name, variableId);
      fragment.appendChild(variableDiv);
      
      lastIndex = match.index + match[0].length;
    }
    
    if (lastIndex < content.length) {
      const textNode = document.createTextNode(content.substring(lastIndex));
      fragment.appendChild(textNode);
    }
    
    editableDiv.value.appendChild(fragment);
  };

  return {
    showVariableSelector,
    selectedVariableForInsert,
    selectedVariableObj,
    editableDiv,
    savedRange,
    handleVariableSelect,
    saveCursorPosition,
    restoreCursorPosition,
    focusEditableDiv,
    createVariableDiv,
    insertVariable,
    insertVariableToPrompt,
    handleKeydown,
    handleInput,
    renderEditableDiv,
  };
}
