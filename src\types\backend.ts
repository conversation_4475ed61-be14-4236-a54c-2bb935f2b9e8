/**
 * 后端交互数据类型定义
 * 
 * 此文件定义了与后端服务交互时使用的数据结构，
 * 移除了前端UI相关的属性，只保留业务逻辑核心字段
 */

import { NodeType, NodeStatus } from '@/store/modules/orchestration'

/**
 * 参数定义接口
 */
export interface ParameterDefinition {
  /** 参数名称 */
  name: string
  /** 参数类型 */
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  /** 参数描述 */
  description?: string
  /** 是否必需 */
  required?: boolean
  /** 默认值 */
  defaultValue?: any
  /** 参数来源 */
  source?: string
}

/**
 * 流程变量接口 - 扩展支持环境变量和用户变量
 */
export interface FlowVariable {
  /** 变量唯一标识符 */
  id?: string
  /** 变量名称 */
  name: string
  /** 变量分类：environment(环境变量) | user(用户变量) | nodeVariable(节点变量) */
  type: 'envVariable' | 'user' | 'nodeVariable'
  /** 变量标识符 */
  code?: string
  /** 数据类型 */
  valueType: 'string' | 'number' | 'boolean' | 'array' | 'object'
  /** 变量描述 */
  description?: string
  /** 默认值 */
  value?: any
  /** 节点ID */
  nodeId?: string
  /** 节点名称 */
  nodeName?: string
  /** 节点类型 */
  nodeType?: string
  /** 是否只读（环境变量为true，用户变量为false） */
  readonly: boolean
  /** 创建时间 */
  createdAt?: string
  /** 更新时间 */
  updatedAt?: string
  /** 变量作用域标签 */
  tags?: string[]
}

/**
 * 变量创建/更新请求接口
 */
export interface VariableRequest {
  /** 变量名称 */
  name: string
  /** 变量分类 */
  type: 'envVariable' | 'user'
  /** 数据类型 */
  dataType: 'string' | 'number' | 'boolean' | 'array' | 'object'
  /** 变量描述 */
  description?: string
  /** 默认值 */
  defaultValue?: any
  /** 变量作用域标签 */
  tags?: string[]
}

/**
 * 变量查询过滤条件
 */
export interface VariableFilter {
  /** 变量类型过滤 */
  type?: 'envVariable' | 'user'
  /** 数据类型过滤 */
  dataType?: 'string' | 'number' | 'boolean' | 'array' | 'object'
  /** 搜索关键词 */
  search?: string
  /** 标签过滤 */
  tags?: string[]
}

/**
 * 变量导入导出格式
 */
export interface VariableExportData {
  /** 导出版本 */
  version: string
  /** 导出时间 */
  exportedAt: string
  /** 变量列表 */
  variables: FlowVariable[]
}

/**
 * LLM节点配置接口
 */
export interface LLMNodeConfig {
  /** 模型名称 */
  model: string
  /** 系统提示词 */
  systemPrompt?: string
  /** 用户提示词 */
  prompt: string
  /** 温度参数 */
  temperature?: number
  /** top_p参数 */
  topP?: number
  /** 最大token数 */
  maxTokens?: number
  /** 频率惩罚 */
  frequencyPenalty?: number
  /** 存在惩罚 */
  presencePenalty?: number
  /** 提供商 */
  provider?: string
  /** 变量列表 */
  variables?: ParameterDefinition[]
}

/**
 * API节点配置接口
 */
export interface APINodeConfig {
  /** 请求方法 */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  /** 接口地址 */
  url: string
  /** 请求头 */
  headers?: Record<string, string>
  /** 查询参数 */
  params?: Record<string, any>
  /** 请求体 */
  body?: any
  /** 超时时间（秒） */
  timeout?: number
  /** 重试次数 */
  retryCount?: number
}

/**
 * 条件规则接口
 */
export interface ConditionRule {
  /** 条件名称 */
  name: string
  /** 字段名 */
  field: string
  /** 操作符 */
  operator: 'equals' | 'contains' | 'greater' | 'less' | 'regex' | 'not_equals'
  /** 比较值 */
  value: any
  /** 值类型 */
  valueType?: 'fixed' | 'variable' | 'input'
}

/**
 * 条件分支节点配置接口
 */
export interface ConditionNodeConfig {
  /** 条件列表 */
  conditions: ConditionRule[]
  /** 逻辑操作符 */
  conditionLogic: 'AND' | 'OR'
  /** 否则如果分支 */
  elseIfBranches?: ConditionRule[]
  /** 否则如果逻辑操作符 */
  elseIfLogic?: 'AND' | 'OR'
  /** 默认分支 */
  elseBranch?: string
}

/**
 * 分类配置接口
 */
export interface CategoryConfig {
  /** 分类ID */
  id: string
  /** 分类名称 */
  name: string
  /** 关键词列表 */
  keywords: string[]
  /** 分类描述 */
  description?: string
  /** 匹配规则 */
  matchRule: 'contains' | 'exact' | 'regex'
}

/**
 * 问题分类器节点配置接口
 */
export interface QuestionClassifierConfig {
  /** 分类列表 */
  categories: CategoryConfig[]
  /** 默认分类 */
  defaultCategory?: string
}

/**
 * 知识检索节点配置接口
 */
export interface KnowledgeNodeConfig {
  /** 知识库ID列表 */
  knowledgeBaseIds: string[]
  /** 检索方式 */
  retrievalMode: 'semantic' | 'keyword' | 'hybrid'
  /** 返回结果数量 */
  topK?: number
  /** 相似度阈值 */
  similarityThreshold?: number
}

/**
 * 意图识别节点配置接口
 */
export interface IntentionRecognitionConfig {
  /** 意图分类列表 */
  categories: Array<{
    name: string
    description?: string
    isEditing?: boolean
    disable?: boolean
  }>
}

/**
 * 变量赋值节点配置接口
 */
export interface VariableNodeConfig {
  /** 变量名 */
  variableName: string
  /** 变量值 */
  variableValue: any
  /** 值类型 */
  valueType: 'fixed' | 'expression' | 'input'
}

/**
 * 工具节点配置接口
 */
export interface ToolNodeConfig {
  /** 工具名称 */
  toolName: string
  /** 工具参数 */
  parameters?: Record<string, any>
}

/**
 * 循环节点配置接口
 */
export interface CycleNodeConfig {
  /** 循环类型 */
  loopType: 'for' | 'while' | 'foreach'
  /** 循环条件 */
  condition?: string
  /** 最大循环次数 */
  maxIterations?: number
}

/**
 * 聚合节点配置接口
 */
export interface AggregationConfig {
  /** 聚合方式 */
  aggregationType: 'concat' | 'merge' | 'sum' | 'average'
  /** 输入字段 */
  inputFields: string[]
  /** 输出字段 */
  outputField: string
}

/**
 * 文本生成节点配置接口
 */
export interface LLMNodeConfig {
  /** 生成模板 */
  template: string
  /** 变量映射 */
  variableMapping?: Record<string, string>
}

/**
 * 直接回复节点配置接口
 */
export interface DirectReplyConfig {
  /** 回复内容 */
  content: string
  /** 内容类型 */
  contentType: 'text' | 'markdown' | 'html'
}

/**
 * 对话节点配置接口
 */
export interface ChatNodeConfig {
  /** 对话模式 */
  chatMode: 'single' | 'continuous'
  /** 上下文长度 */
  contextLength?: number
}

/**
 * 节点配置联合类型
 */
export type NodeConfig = 
  | LLMNodeConfig 
  | APINodeConfig 
  | ConditionNodeConfig 
  | QuestionClassifierConfig
  | KnowledgeNodeConfig
  | IntentionRecognitionConfig
  | VariableNodeConfig
  | ToolNodeConfig
  | CycleNodeConfig
  | AggregationConfig
  | TextGenerationConfig
  | DirectReplyConfig
  | ChatNodeConfig
  | Record<string, any> // 兜底类型

/**
 * 后端节点数据接口
 */
export interface BackendNodeData {
  /** 节点ID */
  id: string
  /** 节点类型 */
  type: NodeType
  /** 节点标签 */
  label: string
  /** 节点描述 */
  description?: string
  /** 节点位置信息 */
  position: {
    x: number
    y: number
  }
  /** 节点配置 */
  config: NodeConfig
  /** 输入参数定义 */
  inputParams?: ParameterDefinition[]
  /** 输出参数定义 */
  outputParams?: ParameterDefinition[]
  /** 节点连接点信息 */
  handleBounds?: Record<string, any>
}

/**
 * 后端边数据接口
 */
export interface BackendEdgeData {
  /** 边ID */
  id: string
  /** 源节点ID */
  source: string
  /** 目标节点ID */
  target: string
  /** 源连接点ID */
  sourceHandle?: string
  /** 目标连接点ID */
  targetHandle?: string
  /** 条件表达式（用于条件边） */
  condition?: string
}

/**
 * 后端流程数据接口
 */
export interface BackendFlowData {
  /** 流程ID */
  id: string
  /** 流程名称 */
  name: string
  /** 流程描述 */
  description?: string
  /** 节点列表 */
  nodes: BackendNodeData[]
  /** 边列表 */
  edges: BackendEdgeData[]
  /** 流程变量 */
  variables?: FlowVariable[]
  /** 元数据 */
  metadata: {
    /** 版本号 */
    version: string
    /** 创建时间 */
    createdAt: string
    /** 更新时间 */
    updatedAt: string
    /** 标签 */
    tags?: string[]
    /** 分类 */
    category?: string
    /** 场景分类 */
    sceneCategory?: string
  }
}

/**
 * 流程执行请求接口
 */
export interface FlowExecutionRequest {
  /** 流程ID */
  flowId: string
  /** 输入数据 */
  inputData?: Record<string, any>
  /** 执行配置 */
  config?: {
    /** 是否异步执行 */
    async?: boolean
    /** 超时时间（秒） */
    timeout?: number
  }
}

/**
 * 流程执行响应接口
 */
export interface FlowExecutionResponse {
  /** 执行ID */
  executionId: string
  /** 执行状态 */
  status: 'pending' | 'running' | 'completed' | 'failed'
  /** 输出结果 */
  result?: any
  /** 错误信息 */
  error?: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime?: string
}
