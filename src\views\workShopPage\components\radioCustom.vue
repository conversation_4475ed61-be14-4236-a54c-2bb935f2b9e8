<script setup lang='ts'>
import { onMounted, ref } from 'vue'
import {
  NRadio,
  NRadioGroup,
  NSpace,
} from 'naive-ui'

interface listType {
  value: string
  label: string
  note: string
  disabled?: boolean
}
interface Props {
  value: string
  list: listType[]
}
interface Emit {
  (ev: 'change', value: any): any
}

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const a = ref(props.value)

// 点击选中
const radioTap = (val: any) => {
  if (val.disabled)
    return
  a.value = val.value
  emit('change', val)
}

onMounted(() => {

})
</script>

<template>
  <NRadioGroup v-model:value="a" name="radioGroup">
    <NSpace>
      <div v-for="song in props.list" :key="song.value" class="w-[260px] h-[100px] cursor-pointer border animate-[0.5] rounded-lg p-4" style="transition: border-color 0.5s ease;" :class="song.value === a ? 'border-[#0264FA]' : song.disabled ? '!cursor-no-drop' : ''" @click="radioTap(song)">
        <NRadio :value="song.value" :disabled="song.disabled">
          {{ song.label }}
        </NRadio>
        <p class="ml-[24px] mt-[2px] text-[12px]" :class="song.disabled ? 'text-[#C2C2C2]' : ''">
          {{ song.note }}
        </p>
      </div>
    </NSpace>
  </NRadioGroup>
</template>

<style lang='less' scoped>

</style>
