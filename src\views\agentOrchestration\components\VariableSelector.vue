<template>
  <div class="variable-selector">
    <!-- 变量选择下拉框 -->
    <n-select
      v-model:value="selectedVariable"
      :options="variableOptions"
      :placeholder="placeholder"
      :filterable="true"
      :clearable="true"
      @update:value="handleVariableSelect"
      class="variable-select"
    >
      <template #empty>
        <div class="empty-variables">
          <div class="empty-icon">📝</div>
          <div class="empty-text">暂无可用变量</div>
          <div class="empty-hint">
            <n-button text type="primary" @click="openVariableManagement">
              去添加变量
            </n-button>
          </div>
        </div>
      </template>
      
      <template #option="{ node, option }">
        <div class="variable-option">
          <div class="variable-info">
            <div class="variable-name">{{ option.label }}</div>
            <div class="variable-meta">
              <n-tag size="small" :type="getVariableTypeColor(option.variableType)">
                {{ getVariableTypeLabel(option.variableType) }}
              </n-tag>
              <n-tag size="small" :type="getDataTypeColor(option.dataType)">
                {{ getDataTypeLabel(option.dataType) }}
              </n-tag>
              <span v-if="option.readonly" class="readonly-badge">只读</span>
            </div>
          </div>
          <div v-if="option.description" class="variable-description">
            {{ option.description }}
          </div>
        </div>
      </template>
    </n-select>

    <!-- 变量引用语法提示 -->
    <div v-if="showSyntaxHint && selectedVariable" class="syntax-hint">
      <div class="hint-title">变量引用语法：</div>
      <div class="hint-content">
        <code>{{ getVariableSyntax(selectedVariable) }}</code>
        <n-button 
          text 
          size="small" 
          @click="copyToClipboard(getVariableSyntax(selectedVariable))"
          class="copy-btn"
        >
          复制
        </n-button>
      </div>
    </div>

    <!-- 变量使用统计 -->
    <div v-if="showUsageStats && selectedVariableInfo" class="usage-stats">
      <div class="stats-title">使用情况：</div>
      <div class="stats-content">
        <span class="usage-count">在 {{ getVariableUsageCount(selectedVariableInfo.name) }} 个节点中使用</span>
        <n-button 
          text 
          size="small" 
          @click="showVariableUsage(selectedVariableInfo.name)"
          class="view-usage-btn"
        >
          查看详情
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { NSelect, NButton, NTag, useMessage } from 'naive-ui'
import { useOrchestrationStore } from '@/store'
import type { FlowVariable } from '@/types/backend'

interface Props {
  modelValue?: string
  placeholder?: string
  // 移除：不再需要根据类型从store获取
  // variableType?: 'envVariable' | 'user' | 'all'
  showSyntaxHint?: boolean
  showUsageStats?: boolean
  allowedDataTypes?: string[]
  // 新增：从父组件接收变量选项
  variableOptionArr?: FlowVariable[]
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'variableSelect', variable: FlowVariable): void
  // 移除：不再需要打开变量管理
  // (e: 'openVariableManagement'): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择变量',
  // 移除：不再需要默认类型
  // variableType: 'all',
  showSyntaxHint: true,
  showUsageStats: false,
  allowedDataTypes: () => [],
  // 默认值为空数组
  variableOptionArr: () => []
})

const emit = defineEmits<Emits>()
const message = useMessage()
const orchestrationStore = useOrchestrationStore()

// 选中的变量
const selectedVariable = ref<string>(props.modelValue || '')

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedVariable.value = newValue || ''
})

// 获取变量选项：仅使用父组件传入的variables
const variableOptions = computed(() => {
  let variables: FlowVariable[] = []

  // 仅使用父组件传入的variables
  if (props.variableOptionArr && props.variableOptionArr.length > 0) {
    props.variableOptionArr.forEach(variable => {
        variables = [...variables,...variable.variables]
    })
    // variables = [...props.variableOptionArr]
  } else {
    // 如果没有传入，则为空数组
    variables = []
  }

  // 按数据类型过滤
  if (props.allowedDataTypes.length > 0) {
    variables = variables.filter(v => props.allowedDataTypes.includes(v.dataType))
  }

  return variables.map(variable => ({
    label: variable.name,
    value: variable.name,
    variableType: variable.type,
    dataType: variable.dataType,
    description: variable.description,
    readonly: variable.readonly,
    variable: variable
  }))
})

// 当前选中的变量信息
const selectedVariableInfo = computed(() => {
  if (!selectedVariable.value) return null
  const option = variableOptions.value.find(opt => opt.value === selectedVariable.value)
  return option?.variable || null
})

// 处理变量选择
const handleVariableSelect = (value: string) => {
  selectedVariable.value = value
  emit('update:modelValue', value)
  const selectedOption = variableOptions.value.find(opt => opt.value === value)
  if (selectedOption) {
    emit('variableSelect', selectedOption.variable)
  }
}

// 移除：不再需要打开变量管理功能
// // 打开变量管理
// const openVariableManagement = () => {
//   emit('openVariableManagement')
// }

// 获取变量类型颜色
const getVariableTypeColor = (type: string) => {
  return type === 'envVariable' ? 'warning' : 'info'
}

// 获取变量类型标签
const getVariableTypeLabel = (type: string) => {
  return type === 'envVariable' ? '环境变量' : '用户变量'
}

// 获取数据类型颜色
const getDataTypeColor = (dataType: string) => {
  const colorMap: Record<string, string> = {
    string: 'info',
    number: 'success',
    boolean: 'warning',
    array: 'error',
    object: 'default'
  }
  return colorMap[dataType] || 'default'
}

// 获取数据类型标签
const getDataTypeLabel = (dataType: string) => {
  const labelMap: Record<string, string> = {
    string: '文本',
    number: '数字',
    boolean: '布尔',
    array: '数组',
    object: '对象'
  }
  return labelMap[dataType] || dataType
}

// 获取变量引用语法
const getVariableSyntax = (variableName: string) => {
  return `{{${variableName}}}`
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 移除：不再需要从store获取使用次数
// 获取变量使用次数
const getVariableUsageCount = (variableName: string) => {
  return orchestrationStore.getVariableUsageCount(variableName)
}

// // 显示变量使用详情
const showVariableUsage = (variableName: string) => {
  // 这里可以实现显示变量使用详情的逻辑
  message.info(`变量 "${variableName}" 的使用详情功能待实现`)
}
</script>

<script lang="ts">
export default {
  name: 'VariableSelector'
}
</script>

<style scoped lang="less">
.variable-selector {
  .variable-select {
    width: 100%;
    height: 38px;
  }

  .empty-variables {
    text-align: center;
    padding: 20px;
    color: #9ca3af;

    .empty-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .empty-text {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .empty-hint {
      font-size: 12px;
    }
  }

  .variable-option {
    padding: 8px 0;

    .variable-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;

      .variable-name {
        font-weight: 500;
        color: #1f2937;
      }

      .variable-meta {
        display: flex;
        align-items: center;
        gap: 6px;

        .readonly-badge {
          font-size: 10px;
          padding: 2px 4px;
          border-radius: 2px;
          background: #fef3c7;
          color: #92400e;
        }
      }
    }

    .variable-description {
      font-size: 12px;
      color: #6b7280;
      line-height: 1.4;
    }
  }

  .syntax-hint {
    margin-top: 8px;
    padding: 8px;
    background: #f8fafc;
    border-radius: 4px;
    border-left: 3px solid #3b82f6;

    .hint-title {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 4px;
    }

    .hint-content {
      display: flex;
      align-items: center;
      gap: 8px;

      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        background: #e5e7eb;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
        color: #374151;
      }

      .copy-btn {
        font-size: 12px;
      }
    }
  }

  .usage-stats {
    margin-top: 8px;
    padding: 8px;
    background: #f0f9ff;
    border-radius: 4px;
    border-left: 3px solid #0ea5e9;

    .stats-title {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 4px;
    }

    .stats-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .usage-count {
        font-size: 12px;
        color: #374151;
      }

      .view-usage-btn {
        font-size: 12px;
      }
    }
  }
}
</style>
