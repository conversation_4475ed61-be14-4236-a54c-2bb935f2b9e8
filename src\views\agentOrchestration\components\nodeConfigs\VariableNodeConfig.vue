<template>
  <div class="variable-node-config">
    <!-- 变量赋值标题 -->
    <n-form-item label-placement="left" class="setHeight mb-[17px]">
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span>变量赋值
        </div>
      </template>
    </n-form-item>

    <!-- 表头 -->
    <div class="flex items-center mb-[12px] text-[#565756]">
      <div class="w-[114px]">变量名</div>
      <div class="w-[114px] ml-[12px] mr-[12px]">操作</div>
      <div class="w-[212px] mr-[6px]">变量值</div>
    </div>

    <!-- 变量列表 -->
    <div
      class="flex items-center mb-[12px] text-[#565756]"
      v-for="(item, index) in formData.config.variablelist"
      :key="index"
    >
      <!-- 变量名输入 -->
      <div class="w-[114px] h-[38px] outputrow">
        <n-input
          v-model:value="item.key"
          type="text"
          placeholder="变量名"
        />
      </div>

      <!-- 操作选择 -->
      <div class="w-[114px] ml-[12px] mr-[12px] h-[38px]">
        <n-select
          v-model:value="item.operate"
          :options="operateOptions"
          default-value="0"
          filterable
          @update:value="clearVariableValue(item, index)"
        />
      </div>

      <!-- 变量值输入 -->
      <div class="w-[212px] mr-[6px] h-[38px] outputrow">
        <AggregationSelector
          v-if="item.operate == '0'"
          v-model="item.value"
          :options="aggregationOptions"
          placeholder="请选择变量"
          @change="handleAggregationChange"
        />
        <n-input
          v-else
          :disabled="item.operate == '2'"
          v-model:value="item.value"
          type="text"
          placeholder="变量值"
        />
      </div>

      <!-- 删除按钮 -->
      <img
        @click="deleteVariable(index)"
        class="w-[16px] h-[16px] cursor-pointer"
        src="@/assets/agentOrchestration/delIcon2.png"
      />
    </div>

    <!-- 添加变量按钮 -->
    <div class="w-[488px] mt-[12px] btnparent">
      <n-button @click="addVariable" dashed> + 添加变量 </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { NFormItem, NInput, NSelect, NButton } from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import { useNodeConfig } from './composables/useNodeConfig';
import type { NodeConfigProps, NodeConfigEvents, VariableAssignment } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const {
  operateOptions,
  aggregationOptions,
  updateAggregationOptions,
  handleAggregationChange,
} = useNodeConfig(props);

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'variable') {
      const config = newNode.data.config;
      
      // 初始化变量列表
      if (!config.variablelist) {
        config.variablelist = [];
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听显示状态，更新聚合选项
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.node) {
      updateAggregationOptions();
    }
  },
  { immediate: true }
);

// 添加变量
const addVariable = () => {
  if (!props.formData.config.variablelist) {
    props.formData.config.variablelist = [];
  }
  props.formData.config.variablelist.push({
    key: "",
    value: "",
    operate: "0",
  });
};

// 删除变量
const deleteVariable = (index: number) => {
  if (props.formData.config.variablelist) {
    props.formData.config.variablelist.splice(index, 1);
  }
};

// 清空变量值
const clearVariableValue = (item: VariableAssignment, index: number) => {
  if (item.operate == "2") {
    props.formData.config.variablelist[index].value = "";
  }
};
</script>

<style scoped lang="less">
.variable-node-config {
  .rowstit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
    width: 100%;
    
    .rowicon {
      width: 5px;
      height: 16px;
      background: #abc6ff;
      background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
      border-radius: 3px;
      margin-right: 9px;
    }
  }

  .setHeight {
    :deep(.n-form-item-label) {
      height: 22px !important;
      min-height: 22px;
    }
    
    :deep(.n-form-item-blank) {
      height: 22px;
      min-height: 22px;
    }
  }

  .outputrow {
    .n-input {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;
      
      :deep(.n-input__border) {
        display: none;
      }
    }
    
    .n-input__input-el {
      height: 100%;
      border: 0;
    }
    
    .n-input-number {
      height: 100%;
      border: 0;
    }
  }

  .btnparent {
    :deep(.n-button) {
      width: 100%;
    }
  }

  :deep(.n-select) {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;

    .n-base-selection__border {
      display: none;
    }
    
    .n-base-selection {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      .n-base-selection-label {
        height: 100%;
        background: #f5f5f6;
        border-radius: 8px !important;
      }
    }
  }
}
</style>
