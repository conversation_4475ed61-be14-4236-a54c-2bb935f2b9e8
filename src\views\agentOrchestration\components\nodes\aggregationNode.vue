<template>
  <div class="aggregation-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />

    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/aggregationIcon.png" alt="聚合">
        <div class="node-title">{{ data.label || '聚合' }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>

      <!-- 聚合类型 -->
      <div class="aggregation-type">{{ data.config?.type || '数据聚合' }}</div>
    </div>

    <!-- 输出连接点 -->
    <Handle
      type="source"
      :position="Position.Right"
      :id="`${id}-output`"
      class="output-handle"
    />

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'

interface AggregationNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      type?: string
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<AggregationNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})

</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.aggregation-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .aggregation-type {
      .node-subtitle-style();
      background: #f3f4f6;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: 500;
      color: #374151;
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
