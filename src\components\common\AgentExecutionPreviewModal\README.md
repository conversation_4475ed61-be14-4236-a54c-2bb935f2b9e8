# AgentExecutionPreviewModal 智能体执行进度预览弹框

## 概述

`AgentExecutionPreviewModal` 是一个用于展示智能体执行进度的弹框组件，支持动态可变节点数量的流程展示、实时状态更新和文件下载功能。

## 功能特性

- ✅ 支持动态可变节点数量的流程展示
- ✅ 节点执行过程中实时显示执行状态（进行中/完成/失败）
- ✅ 支持外部触发关闭弹框功能
- ✅ 提供教案内容本地导出功能
- ✅ 现代化扁平化设计风格，与项目整体UI组件库保持一致
- ✅ 响应式设计，支持移动端适配
- ✅ 蓝色渐变背景，统一的进度指示器样式

## 安装和导入

```typescript
// 导入组件
import { AgentExecutionPreviewModal } from '@/components/common'

// 导入类型定义
import type { ExecutionNode } from '@/components/common/AgentExecutionPreviewModal/types'
```

## 组件接口

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `show` | `boolean` | `false` | 控制弹框显示/隐藏 |
| `title` | `string` | `'正在生成答案'` | 弹框标题 |
| `nodes` | `ExecutionNode[]` | `[]` | 执行节点列表 |
| `allowClose` | `boolean` | `true` | 是否允许关闭弹框 |
| `showStopButton` | `boolean` | `true` | 是否显示停止按钮 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:show` | `(value: boolean)` | 弹框显示状态变化 |
| `close` | `()` | 关闭弹框 |
| `stop` | `()` | 停止执行 |
| `downloadResult` | `()` | 教案内容本地导出 |

### 类型定义

```typescript
// 节点执行状态
export type NodeStatus = 'pending' | 'running' | 'completed' | 'error'

// 文件信息接口
export interface FileInfo {
  name: string
  url: string
  type: string
  size?: number
  lastModified?: number
}

// 执行节点接口
export interface ExecutionNode {
  id: string
  label: string
  description?: string
  details?: string[]
  status: NodeStatus
  icon?: string
  outputFile?: FileInfo
  startTime?: number
  endTime?: number
  duration?: number
  errorMessage?: string
}
```

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <NButton @click="startExecution">开始执行</NButton>
    
    <AgentExecutionPreviewModal
      v-model:show="showModal"
      :title="executionTitle"
      :nodes="executionNodes"
      @close="handleClose"
      @stop="handleStop"
      @downloadResult="handleDownloadResult"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NButton } from 'naive-ui'
import { AgentExecutionPreviewModal } from '@/components/common'
import type { ExecutionNode } from '@/components/common/AgentExecutionPreviewModal/types'

const showModal = ref(false)
const executionTitle = ref('智能体执行流程')
const executionNodes = ref<ExecutionNode[]>([])

const startExecution = () => {
  executionNodes.value = [
    {
      id: '1',
      label: '问题理解',
      description: '分析用户输入的问题',
      details: ['解析问题意图', '提取关键信息', '确定处理方式'],
      status: 'pending',
    },
    {
      id: '2',
      label: '知识检索',
      description: '从知识库中检索相关信息',
      details: ['搜索相关文档', '筛选有效信息', '整理检索结果'],
      status: 'pending',
    },
    {
      id: '3',
      label: '答案生成',
      description: '基于检索结果生成回答',
      details: ['分析检索内容', '组织答案结构', '生成最终回答'],
      status: 'pending',
    },
  ]
  showModal.value = true
}

const handleClose = () => {
  showModal.value = false
}

const handleStop = () => {
  // 停止执行逻辑
  executionNodes.value.forEach(node => {
    if (node.status === 'running') {
      node.status = 'error'
    }
  })
}

const handleDownloadFile = (node: ExecutionNode) => {
  if (node.outputFile) {
    // 实现文件下载
    const link = document.createElement('a')
    link.href = node.outputFile.url
    link.download = node.outputFile.name
    link.click()
  }
}

const handleDownloadResult = () => {
  // 实现结果下载逻辑
  console.log('下载执行结果')
}
</script>
```

### 高级用法

```vue
<template>
  <AgentExecutionPreviewModal
    v-model:show="showModal"
    :title="executionTitle"
    :nodes="executionNodes"
    :allow-close="false"
    :show-stop-button="true"
    @close="handleClose"
    @stop="handleStop"
    @downloadResult="handleDownloadResult"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { AgentExecutionPreviewModal } from '@/components/common'
import type { ExecutionNode } from '@/components/common/AgentExecutionPreviewModal/types'

const showModal = ref(false)
const executionTitle = ref('文件处理流程')
const executionNodes = ref<ExecutionNode[]>([
  {
    id: '1',
    label: '文件解析',
    description: '解析上传的文件',
    details: ['文件格式识别', '内容提取', '结构分析'],
    status: 'completed',
    icon: '/assets/icons/file.png',
  },
  {
    id: '2',
    label: '数据处理',
    description: '处理提取的数据',
    details: ['数据清洗', '格式转换', '内容标准化'],
    status: 'running',
    outputFile: {
      name: 'processed_data.xlsx',
      url: '/downloads/processed_data.xlsx',
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
  },
  {
    id: '3',
    label: '结果生成',
    description: '生成处理结果',
    status: 'pending',
    outputFile: {
      name: 'analysis_report.pdf',
      url: '/downloads/analysis_report.pdf',
      type: 'application/pdf',
    },
  },
])

const handleClose = () => {
  showModal.value = false
}

const handleStop = () => {
  executionNodes.value.forEach(node => {
    if (node.status === 'running') {
      node.status = 'error'
    }
  })
}

const handleDownloadResult = () => {
  // 实现结果下载逻辑
  window.open('/downloads/final_result.zip', '_blank')
}
</script>
```

## 工具函数

组件提供了一些实用的工具函数：

```typescript
import {
  createExecutionNode,
  calculateProgress,
  isExecutionComplete,
  isExecutionRunning,
  formatDuration
} from '@/components/common/AgentExecutionPreviewModal/types'

// 创建执行节点
const node = createExecutionNode('1', '问题理解', {
  description: '分析用户输入',
  status: 'running'
})

// 计算进度百分比
const progress = calculateProgress(nodes) // 返回 0-100

// 检查执行状态
const isComplete = isExecutionComplete(nodes)
const isRunning = isExecutionRunning(nodes)

// 格式化持续时间
const duration = formatDuration(5000) // "5.0s"
```

## 设计规范

组件严格遵循项目的设计规范：

- **色彩方案**: 使用项目统一的主色调 `#125EFF`
- **背景渐变**: `linear-gradient(180deg, #E4EFFF 0%, #FAFBFF 100%)`
- **进度指示器**: 50px圆形，蓝色渐变背景，白色半透明边框
- **字体**: 遵循现有的字体大小和权重规范
- **间距**: 使用统一的间距系统
- **圆角**: 30px 的弹框圆角，12px 的节点卡片圆角
- **阴影**: 轻量级的阴影效果
- **动画**: 流畅的过渡动画

## 注意事项

1. 组件依赖 Naive UI 组件库
2. 需要正确配置类型定义文件路径
3. 文件下载功能需要根据实际项目需求实现
4. 建议在生产环境中添加错误边界处理
5. 大量节点时建议控制节点数量以保持性能
6. 弹框宽度固定为800px，移动端自适应为95vw

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的执行进度展示
- 实现文件下载功能
- 添加响应式设计支持
- 统一的蓝色渐变背景设计
- 移除footer，简化界面
