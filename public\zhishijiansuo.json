{"id": "1752222297155-8w029tpgu", "name": "知识检索问答", "description": "基于Dify工作流转换的知识检索+聊天机器人流程，支持从知识库检索信息并智能回答", "nodes": [{"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABWNJREFUWEfFWGtsFFUU/s6dWWlCVRBs0R+GikbBB2VnplvjIyCiqU9iQtGYCCaISlQMKon8MEiiRvERDEElPDQYUEsMxkfDw9gEie7uTLe0KYjRWIkRixAEH3G7e++xp8zCsn1s25T2JpPszJ57z3fP+c7jXsIARiwWOy+TyUy1LGuy1nqGUuoyABXMXCrLENHfAH42xvxoWdbXWuv9kUhkbzweP9FfNdRfQc/zapn5fmaOASgHoIrMNQDaiShORFuSyeTH/dHVJ6CamppR7e3tdyilFjHzzIIFjwE4BOAogP/C/0oAjANwEYCx+fJE9JUxZk15efkX9fX16d7A9QpI3JPNZp8EsKRg8WYAO4hInt8EUCaT6QIUiUS6ADHzxcx8KwB5rs1TLpt4o6SkZNWePXv+6glUj4AqKysnWpb1TrigyHQA2EVEa33f/7Q/ps/JuK57DzMvBHALgHMAsGxIa/1oU1NTW+Fa3QAJGNu232Lmu0Jhccnrxph1qVTqj4GAyclOmzbtQqXUAgBPhy6VAPhMPFAI6gxAoZuEfLeFOzlg2/ZD8Xj8u8EAKZwTi8Wqs9nsRgBXSFAC2G7bdm1+FJ4CJAQ+fPjws53RsyIU/p6IHvN9v2EowOS5cDozvw3gynDTz5eVla3MEf0UoGg0ei8RrQsJfNS27TuHyjK9WOrz0H3HmHlBY2PjJ125LA/5rjC0hcDLgyB4eSgtU7iW4zjPiR4huqQE3/eF9CcBSdIzxnwUTvrSGDO/NwI7jjOeiGQhj5k/sCxrcyKREOIPaIREfw/A7TJRKTVXkicJkbXW7zPz7C6ERLP7Cu1oNHojEQnxJwCQBFdPRKsGw7UwJWwL9W6zLGsehQrEOpJdm4MgmNrXVj3Pm2mMEUAX5MlJbtlgWdb6RCIhESnv/RqO4+wNk+chZp5Lrus+wsxrxGpEtNL3/aUDAJQFYIfyUrt+AbC6o6NjY0tLi2TlosN13VeZWaLbENEisdAWIroPwDEimuv7/s4BAJLw3Q9gPoBo3rw2IlqmlNpRjF+u685iZvHQWGb+kDzP840xDoB9SqnaZDLZ2l9AzLx60qRJTx08ePBSrfUSZn6gM6ufG86XWrXbGLMslUpJ/evRjZ7nXRVSYIpSKhCXHWFmqdC7Q0C/DwRQY2PjEzl5x3Ek2b0EYAaAMeH3fwHUaa2X91S7PM+bEAKSYDlKjuNIpR4FYKfWurapqenPwQKSeZWVlWMikcjNxhgBJiUiN9ZnMpnFzc3N/+SvL/KWZUmQzJKoHXFAc+bMsdra2l4xxiwmotSIu0ysJW5j5hs6O9IDZ5PU0kd/U4zUhfQY8bDvBsjzvIXGGMknQ5YYtdYbigVHb4Ez4qWjm4VGsrg6jnMJM99k23ZLIpGQmjb49oOZPSLapJTaUqw89OQex3HOJ6I3mXkegGQQBNWnAMkP13WHtUFzXXcpM78AQI5OiSAI5AB6umMczha2urp6ciaTkV69rPPcd4KIHvd9f9MZgIarya+qqrpOa70WwNUCgJlfTKfTK1pbW6V1Pm0heTnbx6Cwsm8GcM3J5pSSSqmafA4Oy0FRCAxADorPhK2v7H8HgEVBEPyUT/rhOEpLA/YwgLvDrkL0xy3LejCRSPzQLQ/1ljH7umwgou3SruRfNpSWllI6nZY2ZlznLYeQ9XoAchyvytNxnJnflVY5CIIjPekesuuYzhAWTnQBCqNnfIHCvUqp10aPHr21oaEhd33TDdPZvLDScmHFzL5SamtFRcXmuro6+dbn6DegXBTKlZ5SaooxZrplWZcz88TwSo+VUieMMb8S0T4i+tYYI710SxAEx4sByf3/P+DdY8jXxzHzAAAAAElFTkSuQmCC", "description": "知识检索聊天机器人的起始点", "status": "idle", "config": {"variables": [{"name": "query", "type": "string", "description": "用户查询问题", "required": true, "defaultValue": ""}], "fileUpload": {"enabled": false, "allowedTypes": ["image"], "allowedExtensions": [".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG"], "maxFileSize": 15, "maxImageSize": 10, "maxCount": 3}}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, {"id": "knowledge-retrieval-node", "type": "knowledge", "dimensions": {"width": 239, "height": 122}, "computedPosition": {"x": 357.3778444210218, "y": 166.57085836942883, "z": 0}, "handleBounds": {"source": [{"id": "knowledge-retrieval-node-output", "type": "source", "nodeId": "knowledge-retrieval-node", "position": "right", "x": 237.00009934076644, "y": 56.8906357241018, "width": 8, "height": 8}], "target": [{"id": "knowledge-retrieval-node-input", "type": "target", "nodeId": "knowledge-retrieval-node", "position": "left", "x": -6.000018656999022, "y": 56.8906357241018, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 357.3778444210218, "y": 166.57085836942883}, "data": {"label": "知识检索", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABORJREFUWEfVmF1sVEUUx/8zt7ZdE7APgiRIolAlUlJN771LlgSsT35gxWi6mhANRDA+8CIEH9CYNUSND6iJIcSoiT6I0Zr4WdGEYImptffeWZfU+hGLkqAISmJCS+l2e+e4ZzNrlmX3bm8hCvMyD/fMmd+crzlzhW3bhOjxvZQy7fv+aAO5yM+u63Zord8DsCJKUFzKQHkAvwHg+d9BRGMAtmezWZ7nPLq6utoB7BZC8Fw5WgBcC4BnVFroiBBimxDiSKV0GIb506dPHxsbGzsHNC5Ze3t7y/z585dYllXauDyIaBkRvQhgWTXQRYmVuKDVsVVpoVhAqVRqcT6fXyKlbLUsazyRSPw0MDAw8Z8BdXR0NLe2tq4E8DCAuwDcULX5lBDiO631a1rrL3O53M+zgZuThbq7u5vOnDmzS2vdW/Z1xGYcaz9IKXf4vn8QgI4CqwVUqi/1simZTN4YhuFuAHdXKT4L4C8AkwBaASwyc1mMv++Zmpp6cnR0dLoeVHX2CSZk4VrZxJaZmJj4kIjuAGAZpZNCiM+klPvCMDzOQBxHAJZqrVnuoQrZKQCPz5s37/WBgYGZWlDV2SfqkbNgW1vbLiLaYWTY9Icty9rked7heutc112ttX4WQLeR+VMIsSUIgo9nE1N1gZLJpBOGIZf6642ib4loSzabVQ0Uc+YuBfA5gFIRlFJ+VYyntRcE5DjOXiJ6rOwmy7JWR1mmejPXdW/VWr8NYLGB6vZ9/1AjqHoW4lP+AuC6UvUU4v0gCDjDZj1s274SwF5TJjhpXspms9saKagJxJEvhCjXkbOWZW3wPO+DRsqqvyeTydvDMGTXMdCBQqGQHhkZ+TtKT02gVatW3TYzM8M1hMcxIkpns9lv4gK5rrtIa/2HsXLAbYzneb/GBrJtex2AT83CHwGklVIjcYG4bIyPjxfMulxTU1N6eHg4soLXtJDrumu11uUAPGoaND8ukG3bV5viyS7ztdbpXC53NLaF1qxZs2BycpKLXhOAKSnlRt/3340L5DjOPUT0kXHZJ0T0oFKKK3vdUbcO2bbNrlpuVr6llNoYB4gv40Qi8QoRPWrW7VJKPd1IRxTQ9mIX9zyAK/iCFEKsDYLga06YRkr5u+M464no1WLqX8NdqJRyhe/7XEoiR10gx3FWEhFX6ptM2h5qaWnZPDQ01LCVNbFzAMDNZvc+pdQDszlMXSBW1NXVdZ8Q4h0AzUbxGN9LROTVigXjpjuJ6JkKmONCiHQQBIONrFOKtSghc9vvIaJNxnUs/jsRHbQsa5/Wmi/ZU1rrNsuyUgDWEdG9ABZW6OUgflMptfWCLcRKU6lUIp/PbxVC7ATQNptTGpkTAK4CkDBZxgfLFMFOzSnLKhcZV9xPRE8AuKUBFDdj/UWrvSyl5N5os5GflaUiG7TqjTOZjOzv71+nte4pxtZyImKLceM2XewMTkgpR6SUb3iex9WYs1Hats2ZtqFsqaLszp6enhcymUyptT2vQbNtO7KFrWUNBtu/f/+CMAw5dqxCoTCdSCRODg4O8qvjnLLQ2dm5sLm5+TkiegTASQDrlVJc9UtA57WwFU/pWM+gGLHEotJ13ae01l8opYYr187p1RFz85rivb29Vl9fH1vvnFfI/wZU71CXFdAl97PhkvsdU8/NFyX7Lts/aP8A62inM+7IjDoAAAAASUVORK5CYII=", "description": "从知识库中检索与用户问题相关的文本内容", "status": "idle", "config": {"database": "default", "searchType": "semantic", "maxResults": 5, "retrievalMode": "single", "singleOutput": true, "queryVariable": "{{query}}", "datasetIds": [], "model": {"provider": "openai", "name": "gpt-3.5-turbo", "mode": "chat", "completionParams": {"temperature": 0, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}}, "variables": [{"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "llm-answer-node", "type": "llm", "dimensions": {"width": 217, "height": 114}, "computedPosition": {"x": 693.3141716738858, "y": 169.07804399172167, "z": 0}, "handleBounds": {"source": [{"id": "llm-answer-node-output", "type": "source", "nodeId": "llm-answer-node", "position": "right", "x": 215.00008194062562, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-answer-node-input", "type": "target", "nodeId": "llm-answer-node", "position": "left", "x": -5.999967648143428, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 693.3141716738858, "y": 169.07804399172167}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "基于检索到的上下文信息回答用户问题", "status": "idle", "config": {"model": "gpt-3.5-turbo", "provider": "openai", "temperature": 0.7, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0, "prompt": "You are a helpful assistant. \nUse the following context as your learned knowledge, inside <context></context> XML tags.\n<context>\n{{#context#}}\n</context>\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.", "systemPrompt": "You are a helpful assistant that answers questions based on provided context.", "memory": {"enabled": false, "windowSize": 50, "rolePrefix": {"user": "", "assistant": ""}}, "context": {"enabled": true, "variable": "{{#knowledge-retrieval-node.result#}}"}, "vision": {"enabled": false}, "variables": [{"name": "context", "type": "string", "source": "knowledge-retrieval-node.result"}, {"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "answer-node", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 1000, "y": 200, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "answer-node-input-0", "type": "target", "nodeId": "answer-node", "position": "left", "x": -1.9999552101440796, "y": 25.499990026748552, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 1000, "y": 200}, "data": {"label": "结束节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABVlJREFUWEfFWFtsVFUUXfvcGaeTiAqkD/2hjQ8EibRz7+1YxASDiIhE1LTolxoJIomaaDQxJqgkhChRIyZoCMbHD2ITAwEhPIxNtMWZOXc6kPAMhvIjtlKL4odt557t7HJbh+lMKaR27k+Tzj5nr7Mfa69zCFfxxePxGwYHB+daljXL9/37lVK3Aahj5utlGyL6G8AZY8xpy7J+8H3/eDgcPpxIJP4arxsar6Hrui3M/BQzxwFUA1BXWGsAdBNRgoi2pVKpb8bja0xAS5YsiXR3dy9VSq1h5oUFG/YBOAegF8A/wW8VAKYDuBnA1Hx7IvreGLO5urr6u7179/aXAlcSkKQnm82+BOCVgs2PENE+AAeI6FcBNDg4OAQoHA4PAWLmWwAsYubFAO7Ocy6H+KCiouKj9vb2i8VAFQVUX19fa1nWpwAelNIAMADgIBFt0VrvHE/oh20cx3mUmVcBeADAdQAYwH7f91dnMpmuwr1GARIwoVBoEzMvC4wlJe8bY7Z2dnb+fjVghm0bGhoqlVIrAbwapFQaYJdkoBDUZYCCNEnxSajlJCdDodCziUTi52sBUrgmHo/fk81mPwcwM4j8vlAo1JLfhSOApIB7enpey3XPusD4BBG9oLVumwgweSlcwMyfALgzOPTaqqqqjcOFPgIoFos9TkRbgwLuDYVCj0xUZEpEaneQvj5mXplOp78d4rI85AeD1pYCftvzvA0TGZnCvWzbfkP8SKELJWitpegvARLSM8ZsDxbtMcY8U6qAHcdpIKJlvu8vVkoJ1xQSpDHG9FmWtY+Zd2mtO4sdLCj0LwA8LL8rpVYIeZIUsu/7XzLz8iGERMtLtbZt2/MBfAZARsZ4mPo0gOc8z/upGKiAEnYEfndYlvU0xWKx+4hIoiPsesTzvLklFjcw89cA7gh+PyukSEQyIkY+ZhagwtYzgn+eIqInS0XKtu3DAXmeY+YV5DjO88y8WU5MRBu11q8XA+S67lpjzFtix8wHlVJvMnOvZVmXAfJ9X/aZboxZT0RSF0Yp9U4qlZLuHfU5jvMeM0t3GyJaIxHaJicA0EdEK7TWB4otjMVi7UQ0D8BZImrRWifHKnrHcRqZWThtBjN3pNPpe0sAkhEjGZoqGSDXdbUxxgZwTCnVkkqljpZYeCy3cBaAdM62xfO8X8YCZNv2rQAEUCx30ONa69klIn+XMUbsZhORlpSdZ2bJ+Y8BoN9KADrBzDNlkdglk8kzYwFqbGysE0fM7BDRSa21EOGoz3XdmgCQ1HIv2bYtkzoi09v3/ZZMJnNhMgHV19ffZFmWRGgRgP6yA2pubra6urrezXHXy0TUWfaUBcRck+vc+TlFerLsRV1YHmVv+1GAXNddZYwROVAWYiwWobKOjryZOMT4ZR2utm3LlWoPgAuRSKSpo6Ojp2zyo6mpaVp/f/8WInoimPbztdbt5RRocr1aD6CCiJJ1dXXzWltb/bJIWMdx5ohiCG7AfxLR6txoEWnzn4SdLJHf2NjY5Pu+dPWw7towZcqUdW1tbUOXzUm9BjmOM5OZWwHMuSROKRUOhx86dOjQH8PtPykXRdu2bwQgF0URf1WB8/2WZb2YTCZP5XPR/36Vdl13oTFmNYClAKKB885IJNLc0dExSlNN2GNDNBpVAwMDEaXUtGw2W0NEcaXUY6KH8iJwMSdvP2bmDz3PO19M5kzYcwwRidYWXTUNQE3wd8SnUspj5k2VlZXbr+k5phD9tT5Y5e5dSaXUztra2q+EZ8ZSmaO67ErGw096SqnZxpgFlmXdzsy1wZOezCJRm3KdOaqUajfGHItGo5lSb0HF/P0LhaJvxBYEIy0AAAAASUVORK5CYII=", "description": "输出最终的智能回答结果", "status": "idle", "config": {"outputVariables": [{"name": "answer", "type": "string", "source": "llm-answer-node.text", "description": "基于知识库的智能回答"}, {"name": "context", "type": "string", "source": "knowledge-retrieval-node.result", "description": "检索到的相关上下文"}, {"name": "query", "type": "string", "source": "start-node.query", "description": "原始用户查询"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}], "edges": [{"id": "1752222309776-us7g3oxfy", "type": "default", "source": "start-node", "target": "knowledge-retrieval-node", "sourceHandle": "start-node-output", "targetHandle": "knowledge-retrieval-node-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABWNJREFUWEfFWGtsFFUU/s6dWWlCVRBs0R+GikbBB2VnplvjIyCiqU9iQtGYCCaISlQMKon8MEiiRvERDEElPDQYUEsMxkfDw9gEie7uTLe0KYjRWIkRixAEH3G7e++xp8zCsn1s25T2JpPszJ57z3fP+c7jXsIARiwWOy+TyUy1LGuy1nqGUuoyABXMXCrLENHfAH42xvxoWdbXWuv9kUhkbzweP9FfNdRfQc/zapn5fmaOASgHoIrMNQDaiShORFuSyeTH/dHVJ6CamppR7e3tdyilFjHzzIIFjwE4BOAogP/C/0oAjANwEYCx+fJE9JUxZk15efkX9fX16d7A9QpI3JPNZp8EsKRg8WYAO4hInt8EUCaT6QIUiUS6ADHzxcx8KwB5rs1TLpt4o6SkZNWePXv+6glUj4AqKysnWpb1TrigyHQA2EVEa33f/7Q/ps/JuK57DzMvBHALgHMAsGxIa/1oU1NTW+Fa3QAJGNu232Lmu0Jhccnrxph1qVTqj4GAyclOmzbtQqXUAgBPhy6VAPhMPFAI6gxAoZuEfLeFOzlg2/ZD8Xj8u8EAKZwTi8Wqs9nsRgBXSFAC2G7bdm1+FJ4CJAQ+fPjws53RsyIU/p6IHvN9v2EowOS5cDozvw3gynDTz5eVla3MEf0UoGg0ei8RrQsJfNS27TuHyjK9WOrz0H3HmHlBY2PjJ125LA/5rjC0hcDLgyB4eSgtU7iW4zjPiR4huqQE3/eF9CcBSdIzxnwUTvrSGDO/NwI7jjOeiGQhj5k/sCxrcyKREOIPaIREfw/A7TJRKTVXkicJkbXW7zPz7C6ERLP7Cu1oNHojEQnxJwCQBFdPRKsGw7UwJWwL9W6zLGsehQrEOpJdm4MgmNrXVj3Pm2mMEUAX5MlJbtlgWdb6RCIhESnv/RqO4+wNk+chZp5Lrus+wsxrxGpEtNL3/aUDAJQFYIfyUrt+AbC6o6NjY0tLi2TlosN13VeZWaLbENEisdAWIroPwDEimuv7/s4BAJLw3Q9gPoBo3rw2IlqmlNpRjF+u685iZvHQWGb+kDzP840xDoB9SqnaZDLZ2l9AzLx60qRJTx08ePBSrfUSZn6gM6ufG86XWrXbGLMslUpJ/evRjZ7nXRVSYIpSKhCXHWFmqdC7Q0C/DwRQY2PjEzl5x3Ek2b0EYAaAMeH3fwHUaa2X91S7PM+bEAKSYDlKjuNIpR4FYKfWurapqenPwQKSeZWVlWMikcjNxhgBJiUiN9ZnMpnFzc3N/+SvL/KWZUmQzJKoHXFAc+bMsdra2l4xxiwmotSIu0ysJW5j5hs6O9IDZ5PU0kd/U4zUhfQY8bDvBsjzvIXGGMknQ5YYtdYbigVHb4Ez4qWjm4VGsrg6jnMJM99k23ZLIpGQmjb49oOZPSLapJTaUqw89OQex3HOJ6I3mXkegGQQBNWnAMkP13WHtUFzXXcpM78AQI5OiSAI5AB6umMczha2urp6ciaTkV69rPPcd4KIHvd9f9MZgIarya+qqrpOa70WwNUCgJlfTKfTK1pbW6V1Pm0heTnbx6Cwsm8GcM3J5pSSSqmafA4Oy0FRCAxADorPhK2v7H8HgEVBEPyUT/rhOEpLA/YwgLvDrkL0xy3LejCRSPzQLQ/1ljH7umwgou3SruRfNpSWllI6nZY2ZlznLYeQ9XoAchyvytNxnJnflVY5CIIjPekesuuYzhAWTnQBCqNnfIHCvUqp10aPHr21oaEhd33TDdPZvLDScmHFzL5SamtFRcXmuro6+dbn6DegXBTKlZ5SaooxZrplWZcz88TwSo+VUieMMb8S0T4i+tYYI710SxAEx4sByf3/P+DdY8jXxzHzAAAAAElFTkSuQmCC", "description": "知识检索聊天机器人的起始点", "status": "idle", "config": {"variables": [{"name": "query", "type": "string", "description": "用户查询问题", "required": true, "defaultValue": ""}], "fileUpload": {"enabled": false, "allowedTypes": ["image"], "allowedExtensions": [".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG"], "maxFileSize": 15, "maxImageSize": 10, "maxCount": 3}}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "targetNode": {"id": "knowledge-retrieval-node", "type": "knowledge", "dimensions": {"width": 239, "height": 122}, "computedPosition": {"x": 357.3778444210218, "y": 166.57085836942883, "z": 0}, "handleBounds": {"source": [{"id": "knowledge-retrieval-node-output", "type": "source", "nodeId": "knowledge-retrieval-node", "position": "right", "x": 237.00009934076644, "y": 56.8906357241018, "width": 8, "height": 8}], "target": [{"id": "knowledge-retrieval-node-input", "type": "target", "nodeId": "knowledge-retrieval-node", "position": "left", "x": -6.000018656999022, "y": 56.8906357241018, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 357.3778444210218, "y": 166.57085836942883}, "data": {"label": "知识检索", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABORJREFUWEfVmF1sVEUUx/8zt7ZdE7APgiRIolAlUlJN771LlgSsT35gxWi6mhANRDA+8CIEH9CYNUSND6iJIcSoiT6I0Zr4WdGEYImptffeWZfU+hGLkqAISmJCS+l2e+e4ZzNrlmX3bm8hCvMyD/fMmd+crzlzhW3bhOjxvZQy7fv+aAO5yM+u63Zord8DsCJKUFzKQHkAvwHg+d9BRGMAtmezWZ7nPLq6utoB7BZC8Fw5WgBcC4BnVFroiBBimxDiSKV0GIb506dPHxsbGzsHNC5Ze3t7y/z585dYllXauDyIaBkRvQhgWTXQRYmVuKDVsVVpoVhAqVRqcT6fXyKlbLUsazyRSPw0MDAw8Z8BdXR0NLe2tq4E8DCAuwDcULX5lBDiO631a1rrL3O53M+zgZuThbq7u5vOnDmzS2vdW/Z1xGYcaz9IKXf4vn8QgI4CqwVUqi/1simZTN4YhuFuAHdXKT4L4C8AkwBaASwyc1mMv++Zmpp6cnR0dLoeVHX2CSZk4VrZxJaZmJj4kIjuAGAZpZNCiM+klPvCMDzOQBxHAJZqrVnuoQrZKQCPz5s37/WBgYGZWlDV2SfqkbNgW1vbLiLaYWTY9Icty9rked7heutc112ttX4WQLeR+VMIsSUIgo9nE1N1gZLJpBOGIZf6642ib4loSzabVQ0Uc+YuBfA5gFIRlFJ+VYyntRcE5DjOXiJ6rOwmy7JWR1mmejPXdW/VWr8NYLGB6vZ9/1AjqHoW4lP+AuC6UvUU4v0gCDjDZj1s274SwF5TJjhpXspms9saKagJxJEvhCjXkbOWZW3wPO+DRsqqvyeTydvDMGTXMdCBQqGQHhkZ+TtKT02gVatW3TYzM8M1hMcxIkpns9lv4gK5rrtIa/2HsXLAbYzneb/GBrJtex2AT83CHwGklVIjcYG4bIyPjxfMulxTU1N6eHg4soLXtJDrumu11uUAPGoaND8ukG3bV5viyS7ztdbpXC53NLaF1qxZs2BycpKLXhOAKSnlRt/3340L5DjOPUT0kXHZJ0T0oFKKK3vdUbcO2bbNrlpuVr6llNoYB4gv40Qi8QoRPWrW7VJKPd1IRxTQ9mIX9zyAK/iCFEKsDYLga06YRkr5u+M464no1WLqX8NdqJRyhe/7XEoiR10gx3FWEhFX6ptM2h5qaWnZPDQ01LCVNbFzAMDNZvc+pdQDszlMXSBW1NXVdZ8Q4h0AzUbxGN9LROTVigXjpjuJ6JkKmONCiHQQBIONrFOKtSghc9vvIaJNxnUs/jsRHbQsa5/Wmi/ZU1rrNsuyUgDWEdG9ABZW6OUgflMptfWCLcRKU6lUIp/PbxVC7ATQNptTGpkTAK4CkDBZxgfLFMFOzSnLKhcZV9xPRE8AuKUBFDdj/UWrvSyl5N5os5GflaUiG7TqjTOZjOzv71+nte4pxtZyImKLceM2XewMTkgpR6SUb3iex9WYs1Hats2ZtqFsqaLszp6enhcymUyptT2vQbNtO7KFrWUNBtu/f/+CMAw5dqxCoTCdSCRODg4O8qvjnLLQ2dm5sLm5+TkiegTASQDrlVJc9UtA57WwFU/pWM+gGLHEotJ13ae01l8opYYr187p1RFz85rivb29Vl9fH1vvnFfI/wZU71CXFdAl97PhkvsdU8/NFyX7Lts/aP8A62inM+7IjDoAAAAASUVORK5CYII=", "description": "从知识库中检索与用户问题相关的文本内容", "status": "idle", "config": {"database": "default", "searchType": "semantic", "maxResults": 5, "retrievalMode": "single", "singleOutput": true, "queryVariable": "{{query}}", "datasetIds": [], "model": {"provider": "openai", "name": "gpt-3.5-turbo", "mode": "chat", "completionParams": {"temperature": 0, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}}, "variables": [{"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 230.00007330584654, "sourceY": 229.49999002674855, "targetX": 351.37782576402276, "targetY": 227.46149409353063}, {"id": "1752222312014-7o0s02g89", "type": "default", "source": "knowledge-retrieval-node", "target": "llm-answer-node", "sourceHandle": "knowledge-retrieval-node-output", "targetHandle": "llm-answer-node-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "knowledge-retrieval-node", "type": "knowledge", "dimensions": {"width": 239, "height": 122}, "computedPosition": {"x": 357.3778444210218, "y": 166.57085836942883, "z": 0}, "handleBounds": {"source": [{"id": "knowledge-retrieval-node-output", "type": "source", "nodeId": "knowledge-retrieval-node", "position": "right", "x": 237.00009934076644, "y": 56.8906357241018, "width": 8, "height": 8}], "target": [{"id": "knowledge-retrieval-node-input", "type": "target", "nodeId": "knowledge-retrieval-node", "position": "left", "x": -6.000018656999022, "y": 56.8906357241018, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 357.3778444210218, "y": 166.57085836942883}, "data": {"label": "知识检索", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABORJREFUWEfVmF1sVEUUx/8zt7ZdE7APgiRIolAlUlJN771LlgSsT35gxWi6mhANRDA+8CIEH9CYNUSND6iJIcSoiT6I0Zr4WdGEYImptffeWZfU+hGLkqAISmJCS+l2e+e4ZzNrlmX3bm8hCvMyD/fMmd+crzlzhW3bhOjxvZQy7fv+aAO5yM+u63Zord8DsCJKUFzKQHkAvwHg+d9BRGMAtmezWZ7nPLq6utoB7BZC8Fw5WgBcC4BnVFroiBBimxDiSKV0GIb506dPHxsbGzsHNC5Ze3t7y/z585dYllXauDyIaBkRvQhgWTXQRYmVuKDVsVVpoVhAqVRqcT6fXyKlbLUsazyRSPw0MDAw8Z8BdXR0NLe2tq4E8DCAuwDcULX5lBDiO631a1rrL3O53M+zgZuThbq7u5vOnDmzS2vdW/Z1xGYcaz9IKXf4vn8QgI4CqwVUqi/1simZTN4YhuFuAHdXKT4L4C8AkwBaASwyc1mMv++Zmpp6cnR0dLoeVHX2CSZk4VrZxJaZmJj4kIjuAGAZpZNCiM+klPvCMDzOQBxHAJZqrVnuoQrZKQCPz5s37/WBgYGZWlDV2SfqkbNgW1vbLiLaYWTY9Icty9rked7heutc112ttX4WQLeR+VMIsSUIgo9nE1N1gZLJpBOGIZf6642ib4loSzabVQ0Uc+YuBfA5gFIRlFJ+VYyntRcE5DjOXiJ6rOwmy7JWR1mmejPXdW/VWr8NYLGB6vZ9/1AjqHoW4lP+AuC6UvUU4v0gCDjDZj1s274SwF5TJjhpXspms9saKagJxJEvhCjXkbOWZW3wPO+DRsqqvyeTydvDMGTXMdCBQqGQHhkZ+TtKT02gVatW3TYzM8M1hMcxIkpns9lv4gK5rrtIa/2HsXLAbYzneb/GBrJtex2AT83CHwGklVIjcYG4bIyPjxfMulxTU1N6eHg4soLXtJDrumu11uUAPGoaND8ukG3bV5viyS7ztdbpXC53NLaF1qxZs2BycpKLXhOAKSnlRt/3340L5DjOPUT0kXHZJ0T0oFKKK3vdUbcO2bbNrlpuVr6llNoYB4gv40Qi8QoRPWrW7VJKPd1IRxTQ9mIX9zyAK/iCFEKsDYLga06YRkr5u+M464no1WLqX8NdqJRyhe/7XEoiR10gx3FWEhFX6ptM2h5qaWnZPDQ01LCVNbFzAMDNZvc+pdQDszlMXSBW1NXVdZ8Q4h0AzUbxGN9LROTVigXjpjuJ6JkKmONCiHQQBIONrFOKtSghc9vvIaJNxnUs/jsRHbQsa5/Wmi/ZU1rrNsuyUgDWEdG9ABZW6OUgflMptfWCLcRKU6lUIp/PbxVC7ATQNptTGpkTAK4CkDBZxgfLFMFOzSnLKhcZV9xPRE8AuKUBFDdj/UWrvSyl5N5os5GflaUiG7TqjTOZjOzv71+nte4pxtZyImKLceM2XewMTkgpR6SUb3iex9WYs1Hats2ZtqFsqaLszp6enhcymUyptT2vQbNtO7KFrWUNBtu/f/+CMAw5dqxCoTCdSCRODg4O8qvjnLLQ2dm5sLm5+TkiegTASQDrlVJc9UtA57WwFU/pWM+gGLHEotJ13ae01l8opYYr187p1RFz85rivb29Vl9fH1vvnFfI/wZU71CXFdAl97PhkvsdU8/NFyX7Lts/aP8A62inM+7IjDoAAAAASUVORK5CYII=", "description": "从知识库中检索与用户问题相关的文本内容", "status": "idle", "config": {"database": "default", "searchType": "semantic", "maxResults": 5, "retrievalMode": "single", "singleOutput": true, "queryVariable": "{{query}}", "datasetIds": [], "model": {"provider": "openai", "name": "gpt-3.5-turbo", "mode": "chat", "completionParams": {"temperature": 0, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}}, "variables": [{"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "llm-answer-node", "type": "llm", "dimensions": {"width": 217, "height": 114}, "computedPosition": {"x": 693.3141716738858, "y": 169.07804399172167, "z": 0}, "handleBounds": {"source": [{"id": "llm-answer-node-output", "type": "source", "nodeId": "llm-answer-node", "position": "right", "x": 215.00008194062562, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-answer-node-input", "type": "target", "nodeId": "llm-answer-node", "position": "left", "x": -5.999967648143428, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 693.3141716738858, "y": 169.07804399172167}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "基于检索到的上下文信息回答用户问题", "status": "idle", "config": {"model": "gpt-3.5-turbo", "provider": "openai", "temperature": 0.7, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0, "prompt": "You are a helpful assistant. \nUse the following context as your learned knowledge, inside <context></context> XML tags.\n<context>\n{{#context#}}\n</context>\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.", "systemPrompt": "You are a helpful assistant that answers questions based on provided context.", "memory": {"enabled": false, "windowSize": 50, "rolePrefix": {"user": "", "assistant": ""}}, "context": {"enabled": true, "variable": "{{#knowledge-retrieval-node.result#}}"}, "vision": {"enabled": false}, "variables": [{"name": "context", "type": "string", "source": "knowledge-retrieval-node.result"}, {"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 602.3779437617882, "sourceY": 227.46149409353063, "targetX": 687.3142040257424, "targetY": 225.96871828667972}, {"id": "1752222314094-8wffced3y", "type": "default", "source": "llm-answer-node", "target": "answer-node", "sourceHandle": "llm-answer-node-output", "targetHandle": "answer-node-input-0", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "llm-answer-node", "type": "llm", "dimensions": {"width": 217, "height": 114}, "computedPosition": {"x": 693.3141716738858, "y": 169.07804399172167, "z": 0}, "handleBounds": {"source": [{"id": "llm-answer-node-output", "type": "source", "nodeId": "llm-answer-node", "position": "right", "x": 215.00008194062562, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-answer-node-input", "type": "target", "nodeId": "llm-answer-node", "position": "left", "x": -5.999967648143428, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 693.3141716738858, "y": 169.07804399172167}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "基于检索到的上下文信息回答用户问题", "status": "idle", "config": {"model": "gpt-3.5-turbo", "provider": "openai", "temperature": 0.7, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0, "prompt": "You are a helpful assistant. \nUse the following context as your learned knowledge, inside <context></context> XML tags.\n<context>\n{{#context#}}\n</context>\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.", "systemPrompt": "You are a helpful assistant that answers questions based on provided context.", "memory": {"enabled": false, "windowSize": 50, "rolePrefix": {"user": "", "assistant": ""}}, "context": {"enabled": true, "variable": "{{#knowledge-retrieval-node.result#}}"}, "vision": {"enabled": false}, "variables": [{"name": "context", "type": "string", "source": "knowledge-retrieval-node.result"}, {"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "answer-node", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 1000, "y": 200, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "answer-node-input-0", "type": "target", "nodeId": "answer-node", "position": "left", "x": -1.9999552101440796, "y": 25.499990026748552, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 1000, "y": 200}, "data": {"label": "结束节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABVlJREFUWEfFWFtsVFUUXfvcGaeTiAqkD/2hjQ8EibRz7+1YxASDiIhE1LTolxoJIomaaDQxJqgkhChRIyZoCMbHD2ITAwEhPIxNtMWZOXc6kPAMhvIjtlKL4odt557t7HJbh+lMKaR27k+Tzj5nr7Mfa69zCFfxxePxGwYHB+daljXL9/37lVK3Aahj5utlGyL6G8AZY8xpy7J+8H3/eDgcPpxIJP4arxsar6Hrui3M/BQzxwFUA1BXWGsAdBNRgoi2pVKpb8bja0xAS5YsiXR3dy9VSq1h5oUFG/YBOAegF8A/wW8VAKYDuBnA1Hx7IvreGLO5urr6u7179/aXAlcSkKQnm82+BOCVgs2PENE+AAeI6FcBNDg4OAQoHA4PAWLmWwAsYubFAO7Ocy6H+KCiouKj9vb2i8VAFQVUX19fa1nWpwAelNIAMADgIBFt0VrvHE/oh20cx3mUmVcBeADAdQAYwH7f91dnMpmuwr1GARIwoVBoEzMvC4wlJe8bY7Z2dnb+fjVghm0bGhoqlVIrAbwapFQaYJdkoBDUZYCCNEnxSajlJCdDodCziUTi52sBUrgmHo/fk81mPwcwM4j8vlAo1JLfhSOApIB7enpey3XPusD4BBG9oLVumwgweSlcwMyfALgzOPTaqqqqjcOFPgIoFos9TkRbgwLuDYVCj0xUZEpEaneQvj5mXplOp78d4rI85AeD1pYCftvzvA0TGZnCvWzbfkP8SKELJWitpegvARLSM8ZsDxbtMcY8U6qAHcdpIKJlvu8vVkoJ1xQSpDHG9FmWtY+Zd2mtO4sdLCj0LwA8LL8rpVYIeZIUsu/7XzLz8iGERMtLtbZt2/MBfAZARsZ4mPo0gOc8z/upGKiAEnYEfndYlvU0xWKx+4hIoiPsesTzvLklFjcw89cA7gh+PyukSEQyIkY+ZhagwtYzgn+eIqInS0XKtu3DAXmeY+YV5DjO88y8WU5MRBu11q8XA+S67lpjzFtix8wHlVJvMnOvZVmXAfJ9X/aZboxZT0RSF0Yp9U4qlZLuHfU5jvMeM0t3GyJaIxHaJicA0EdEK7TWB4otjMVi7UQ0D8BZImrRWifHKnrHcRqZWThtBjN3pNPpe0sAkhEjGZoqGSDXdbUxxgZwTCnVkkqljpZYeCy3cBaAdM62xfO8X8YCZNv2rQAEUCx30ONa69klIn+XMUbsZhORlpSdZ2bJ+Y8BoN9KADrBzDNlkdglk8kzYwFqbGysE0fM7BDRSa21EOGoz3XdmgCQ1HIv2bYtkzoi09v3/ZZMJnNhMgHV19ffZFmWRGgRgP6yA2pubra6urrezXHXy0TUWfaUBcRck+vc+TlFerLsRV1YHmVv+1GAXNddZYwROVAWYiwWobKOjryZOMT4ZR2utm3LlWoPgAuRSKSpo6Ojp2zyo6mpaVp/f/8WInoimPbztdbt5RRocr1aD6CCiJJ1dXXzWltb/bJIWMdx5ohiCG7AfxLR6txoEWnzn4SdLJHf2NjY5Pu+dPWw7towZcqUdW1tbUOXzUm9BjmOM5OZWwHMuSROKRUOhx86dOjQH8PtPykXRdu2bwQgF0URf1WB8/2WZb2YTCZP5XPR/36Vdl13oTFmNYClAKKB885IJNLc0dExSlNN2GNDNBpVAwMDEaXUtGw2W0NEcaXUY6KH8iJwMSdvP2bmDz3PO19M5kzYcwwRidYWXTUNQE3wd8SnUspj5k2VlZXbr+k5phD9tT5Y5e5dSaXUztra2q+EZ8ZSmaO67ErGw096SqnZxpgFlmXdzsy1wZOezCJRm3KdOaqUajfGHItGo5lSb0HF/P0LhaJvxBYEIy0AAAAASUVORK5CYII=", "description": "输出最终的智能回答结果", "status": "idle", "config": {"outputVariables": [{"name": "answer", "type": "string", "source": "llm-answer-node.text", "description": "基于知识库的智能回答"}, {"name": "context", "type": "string", "source": "knowledge-retrieval-node.result", "description": "检索到的相关上下文"}, {"name": "query", "type": "string", "source": "start-node.query", "description": "原始用户查询"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "sourceX": 916.3142536145114, "sourceY": 225.96871828667972, "targetX": 998.0000447898559, "targetY": 229.49999002674855}], "createdAt": "2025-07-11T08:24:57.155Z", "updatedAt": "2025-07-11T08:25:14.759Z", "metadata": {"originalSource": "dify-workflow", "version": "1.0.0", "convertedAt": "2024-01-15T10:30:00.000Z", "description": "从Dify工作流YAML转换而来的知识检索聊天机器人流程", "tags": ["知识检索", "聊天机器人", "RAG", "问答系统"], "features": {"fileUpload": false, "speechToText": false, "textToSpeech": false, "suggestedQuestions": false}, "originalConfig": {"mode": "advanced-chat", "icon": "📑", "iconBackground": "#EFF1F5"}}}