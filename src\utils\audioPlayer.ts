import PCMPlayer from 'pcm-player'

/**
 * base64转int16数组
 * @param byteArray base64
 */
const base64ToInt16Array = (byteArray: string) => {
  const result = new Int16Array(byteArray.length / 2)
  for (let i = 0; i < byteArray.length; i = i + 2) {
    const value
      = (byteArray[i + 1].charCodeAt(0) << 8) | byteArray[i].charCodeAt(0)
    result[i / 2] = value
  }
  return result
}

interface PcmOptions {
  channels: number
  flushTime: number
}
/**
 * pcm格式播放方法
 * @param pcmBase64 解密后的base64
 * @param sampleRate 采样率
 */
export const pcmPlay = (
  sampleRate: number,
  endFun: Function | null,
  pcmOptions?: PcmOptions,
) => {
  const channels = pcmOptions?.channels || 1
  const flushTime = pcmOptions?.flushTime || 2000
  return new PCMPlayer({
    inputCodec: 'Int16',
    sampleRate,
    channels,
    flushTime,
    onstatechange: () => {
      return {}
    },
    onended: () => {
      endFun && endFun()
      return {}
    },
  })
}

/**
 * pcm音频播放
 * @param pcmBase64
 * @param sampleRate 采样率
 * @param endFun 停止后的回调
 * @param pcmOptions pcm其他配置（声道、缓冲时间）
 * @returns
 */
const pcmPlayer = (
  pcmBase64: string,
  sampleRate: number,
  endFun: Function | null,
  pcmOptions?: PcmOptions,
) => {
  const pcmAudio = pcmPlay(sampleRate, endFun, pcmOptions)
  pcmAudio.feed(base64ToInt16Array(pcmBase64))
  return pcmAudio
}

interface AudioPlayerParam {
  playingCallback: any
  pauseCallback: any
  endCallback: any
  errCallback?: any
}

/**
 * mp3、wav音频播放
 * @param playingCallback 播放中的回调
 * @param pauseCallback 暂停的回调
 * @param endCallback 结束的回调
 * @param errCallback 错误的回调
 */
const audioPlayer = (param: AudioPlayerParam) => {
  const audio = new Audio()
  audio.addEventListener('playing', param.playingCallback)
  audio.addEventListener('pause', param.pauseCallback)
  audio.addEventListener('ended', param.endCallback)
  audio.addEventListener('error', param.errCallback)
  return audio
}

export { audioPlayer, pcmPlayer, base64ToInt16Array }
