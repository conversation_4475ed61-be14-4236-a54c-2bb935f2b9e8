<script setup lang='ts'>
import { onMounted, ref } from "vue";
import { NButton, NInput, NSpin } from "naive-ui";
import Message from "@/views/tankChat/components/Message/index.vue";
import testTit from "@/assets/workShopPage/test-tit.png";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { t } from "@/locales";
import { useScroll } from "@/views/tankChat/hooks/useScroll";
import { fetchChatAPIProcess } from "@/api/tankChat";
import { useToolsStore } from "@/store";
import icon1 from "@/assets/applicationPage/icon1.png";

defineOptions({
  name: "Chat",
});

const props = defineProps<Props>();

const emit = defineEmits(["textConverse"]);

const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll();

interface Props {
  submitFlag: boolean;
  useChat: any;
  show: boolean;
}

const ToolsStore = useToolsStore();
const { isMobile } = useBasicLayout();
let controller: any = new AbortController();
const loading = ref(false);

const prompt = ref();

const uuid: any = ref(null);

const dataSources: any = ref([]);
const addChat = (uuid: any, chat: any) => {
  // console.log(uuid, chat)
  // const index = dataSources.value.findIndex(item => item.uuid === uuid)
  // if (index !== -1) {
  //   dataSources.value[index] = chat
  // }
  // else {
  dataSources.value.push({
    uuid,
    ...chat,
  });
  // }
};
const updateChat = (uuid: any, index: any, chat: any) => {
  if (!uuid || uuid === 0) {
    if (dataSources.value.length) dataSources.value[0].data[index] = chat;
    return;
  }

  // const chatIndex = dataSources.value.findIndex(item => item.uuid === uuid)
  // // console.log(uuid, index, chatIndex)
  // if (chatIndex !== -1)
  dataSources.value[index] = chat;
};

const updateChatSome = (uuid: any, index: any, chat: any) => {
  dataSources.value[index] = {
    ...dataSources.value[index],
    ...chat,
  };
};

const onConversation = async () => {
  // 做对话前处理  后父组件调用 textConversation
  emit("textConverse", { message: prompt.value });
};

const textConversation = async (obj: any, Id: string) => {
  console.log(obj);
  uuid.value = obj?.conversationId;
  loading.value = true;

  const message = obj?.question;
  addChat(uuid, {
    dateTime: new Date().toLocaleString(),
    text: message,
    answerList: [],
    endstatus: 1,
    inversion: true,
    error: false,
    conversationOptions: null,
    requestOptions: { prompt: message, options: null },
  });
  prompt.value = "";
  controller = new AbortController();
  addChat(uuid.value, {
    dateTime: new Date().toLocaleString(),
    text: t("chat.thinking"),
    loading: true,
    answerList: [],
    endstatus: 1,
    inversion: false,
    error: false,
    conversationOptions: null,
  });
  await scrollToBottom();

  try {
    let lastText = "";
    const fetchChatAPIOnce = async () => {
      await fetchChatAPIProcess<Chat.ConversationResponse>({
        signal: controller.signal,
        ...obj,
        onDownloadProgress: ({ event }) => {
          const xhr = event.target;
          const { responseText } = xhr;
          // if (chatStore.netFlag) {
          // 深度思考
          // 按行分割响应文本
          const lines = responseText
            .split("\n")
            .filter((line) => line.trim() !== "");

          // 重置文本,避免重复累加
          lastText = "";
          // 处理每一行数据
          for (const line of lines) {
            const trimmedLine = line.replace(/^data: /, "").trim();
            // info.setloading(false)
            try {
              // const data = JSON.parse(trimmedLine)
              const data = JSON.parse(trimmedLine?.substring(5));
              console.log(data);
              // 停止回答用
              // currectemitterId.value = data.emitterId

              // 直接使用当前响应文本,不进行累加
              const deltaContent = data.choices[0].message.content || "";

              lastText += deltaContent;

              updateChat(uuid.value, dataSources.value.length - 1, {
                dateTime: new Date().toLocaleString(),
                text: lastText, // 使用完整的lastText,不再和之前的文本拼接
                // answerList: data.answerList,
                inversion: false,
                error: false,
                loading: true,
                conversationOptions: {
                  conversationId: data.conversationContentId || "",
                  parentMessageId: data.id || "",
                },
              });

              // if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
              //   options.parentMessageId = data.id
              //   message = ''
              //   return fetchChatAPIOnce()
              // }

              scrollToBottomIfAtBottom();

              if (
                data.choices[0].finish_reason === "stop" ||
                data.choices[0].finish_reason === "STOP"
              ) {
                console.log(dataSources.value);
                // updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
                updateChatSome(uuid, dataSources.value.length - 1, {
                  loading: false,
                  answerList: data.answerList,
                });
                loading.value = false;
              }
            } catch (error) {
              console.log(error);
            }
          }
        },
      });
    };

    await fetchChatAPIOnce();
  } catch (error: any) {
    console.log(error);
  } finally {
    await scrollToBottom();
    // loading.value = false
  }
};

function handleEnter(event: KeyboardEvent) {
  if (!isMobile.value) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (props.submitFlag && prompt) onConversation();
    }
  } else {
    if (event.key === "Enter" && event.ctrlKey) {
      event.preventDefault();
      if (props.submitFlag && prompt) onConversation();
    }
  }
}

onMounted(() => {
  ToolsStore.ToolInfo.icon = icon1;
});

defineExpose({
  textConversation,
});
</script>

<template>
  <NSpin :show="show">
    <div class="p-6">
      <div class="flex">
        <img class="w-5 mr-2" :src="testTit" alt="" />
        <p class="text-[16px]">测试预览</p>
      </div>
      <div id="scrollRef" ref="scrollRef" class="overflow-y-scroll h-[62vh]">
        <Message
          v-for="(item, index) of dataSources"
          :key="index"
          :answer-list="item.answerList"
          :category="item.category"
          :conversation-content-id="item.conversationOptions?.conversationId"
          :date-time="item.dateTime"
          :endstatus="item.endstatus"
          :error="item.error"
          :inversion="item.inversion"
          :loading="item.loading"
          :text="item.text"
          @delete="handleDelete(index)"
          @regenerate="onRegenerate(index)"
        />
      </div>
      <div class="">
        <NInput
          v-model:value="prompt"
          class="!bg-[#F6F6F7]"
          placeholder="有什么问题可以尽管问我哦…"
          size="large"
          @keypress="handleEnter"
        >
          <template #suffix>
            <NButton
              color="#125EFF"
              class="!p-[8px] !rounded-[8px]"
              :disabled="!(submitFlag && prompt && !loading)"
              @click="onConversation"
            >
              <img
                class="w-[20px] h-[20px]"
                src="@/assets/workShopPage/test-btn.png"
                alt=""
              />
            </NButton>
          </template>
        </NInput>
      </div>
      <div class="">
        <p class="text-center text-[#3232334d] text-[12px] mt-[12px]">
          内容由AI生成，请以最新政策文件为准。
        </p>
      </div>
    </div>
  </NSpin>
</template>

<style lang='less' scoped>
</style>
