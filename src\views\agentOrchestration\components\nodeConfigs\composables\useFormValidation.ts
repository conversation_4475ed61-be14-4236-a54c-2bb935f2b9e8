import { ref } from 'vue';
import type { FormRules } from '../types';

export function useFormValidation() {
  const formRef = ref();

  // 通用表单验证规则
  const commonFormRules: FormRules = {
    label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  };

  // LLM节点表单验证规则
  const llmFormRules: FormRules = {
    ...commonFormRules,
    "config.modelConfig.model": [
      { required: true, message: "请选择模型", trigger: "blur" },
    ],
    "config.tishici": [
      { required: true, message: "请输入提示词", trigger: "blur" },
    ],
    "config.wenbenshuchu": [
      { required: true, message: "请输入输出名称", trigger: "blur" },
    ],
  };

  // 意图识别节点表单验证规则
  const questionClassifierFormRules: FormRules = {
    ...commonFormRules,
    "config.modelConfig.model": [
      { required: true, message: "请选择模型", trigger: "blur" },
    ],
    "config.inputValue": [
      { required: true, message: "请输入变量名称", trigger: "blur" },
    ],
  };

  // API节点表单验证规则
  const apiFormRules: FormRules = {
    ...commonFormRules,
    "config.method": [
      { required: true, message: "请选择请求方法", trigger: "blur" },
    ],
    "config.url": [
      { required: true, message: "请输入API接口地址", trigger: "blur" },
    ],
    "config.timeout": [
      { required: true, message: "请输入超时时间", trigger: "blur" },
    ],
  };

  // 知识检索节点表单验证规则
  const knowledgeFormRules: FormRules = {
    ...commonFormRules,
    "config.jiansuotypevalue": [
      { required: true, message: "请输入查询变量", trigger: "blur" },
    ],
    "config.wenbenshuchu": [
      { required: true, message: "请输入输出名称", trigger: "blur" },
    ],
  };

  // 变量赋值节点表单验证规则
  const variableFormRules: FormRules = {
    ...commonFormRules,
  };

  // 条件节点表单验证规则
  const conditionFormRules: FormRules = {
    ...commonFormRules,
  };

  // 聚合节点表单验证规则
  const aggregationFormRules: FormRules = {
    ...commonFormRules,
    "config.aggregationVariable": [
      { required: true, message: "请选择聚合变量", trigger: "blur" },
    ],
  };

  // 结束节点表单验证规则
  const endFormRules: FormRules = {
    ...commonFormRules,
    "config.huifuneirong": [
      { required: true, message: "请输入回复内容", trigger: "blur" },
    ],
  };

  // 开始节点表单验证规则
  const startFormRules: FormRules = {
    ...commonFormRules,
  };

  // 根据节点类型获取表单验证规则
  const getFormRules = (nodeType: string): FormRules => {
    switch (nodeType) {
      case 'start':
        return startFormRules;
      case 'llm':
        return llmFormRules;
      case 'end':
        return endFormRules;
      case 'question-classifier':
        return questionClassifierFormRules;
      case 'knowledge':
        return knowledgeFormRules;
      case 'variable':
        return variableFormRules;
      case 'condition':
        return conditionFormRules;
      case 'aggregation':
        return aggregationFormRules;
      case 'api':
        return apiFormRules;
      case 'questionClassifier':
        return questionClassifierFormRules;
      default:
        return commonFormRules;
    }
  };

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error("表单验证失败:", error);
      return false;
    }
  };

  // 清除表单验证
  const clearValidation = () => {
    formRef.value?.restoreValidation();
  };

  return {
    formRef,
    commonFormRules,
    llmFormRules,
    questionClassifierFormRules,
    apiFormRules,
    knowledgeFormRules,
    variableFormRules,
    conditionFormRules,
    aggregationFormRules,
    endFormRules,
    startFormRules,
    getFormRules,
    validateForm,
    clearValidation,
  };
}
