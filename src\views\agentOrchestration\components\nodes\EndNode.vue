<template>
  <div class="end-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 多个输入连接点 -->
    <Handle
      v-for="(input, index) in inputs"
      :key="`input-${index}`"
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
      :style="getInputHandleStyle(index)"
    />

    <!-- 节点主体 - 横向布局 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 内容区域 - 横向排列 -->
      <div class="node-content">
        <!-- 停止图标 -->
        <img class="stop-icon" src="@/assets/agentOrchestration/endIcon.png" alt="结束">

        <!-- 节点标题和描述 -->
        <div class="node-text-content">
          <div class="node-title">
            {{ data.label || '结束' }}
          </div>
          <!-- 节点描述信息 -->
          <div v-if="data.description" class="node-description">
            {{ data.description }}
          </div>
        </div>
      </div>

      <!-- 输入配置显示 -->
      <div class="inputs-config" v-if="inputs.length > 1">
        <div class="config-item">
          <span class="config-label">输入数量:</span>
          <span class="config-value">{{ inputs.length }}</span>
        </div>
      </div>
    </div>

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'

interface EndNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      inputs?: Array<{
        id: string
        name?: string
      }>
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<EndNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

// 输入配置
const inputs = computed(() => {
  return props.data.config?.inputs || [{ id: 'default', name: '默认输入' }]
})

// 计算输入连接点位置
const getInputHandleStyle = (index: number) => {
  const totalInputs = inputs.value.length
  if (totalInputs === 1) {
    return {
      top: '50%',
    }
  }

  // 多个输入时，垂直分布
  const spacing = 80 / (totalInputs + 1) // 在节点高度的80%范围内分布
  const startOffset = 10 // 从节点顶部10%开始
  const top = startOffset + spacing * (index + 1)

  return {
    top: `${Math.min(top, 90)}%`, // 最大不超过90%
    transform: 'translateY(-50%)'
  }
}

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.end-node {
  .circular-node-style();
  .unified-handle-style();

  .node-body {
    // 横向布局容器
    // 当有描述信息时增加内边距和高度
    &:has(.node-description:not(:empty)) {
      padding: 10px 16px;
      min-height: 55px;
    }

    .node-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
    }

    .stop-icon {
      width: 18px;
      height: 18px;
      color: #ef4444;
      flex-shrink: 0;
    }

    .node-text-content {
      display: flex;
      flex-direction: column;
      gap: 2px;
      flex: 1;
      min-width: 0; // 允许文本截断

      .node-title {
        .node-title-style();
        font-size: 12px;
        text-align: left;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .node-description {
        .node-description-style();
        font-size: 10px; // 更小的字体适应横向布局
        -webkit-line-clamp: 2; // 允许显示2行
        max-width: 90px; // 增加最大宽度
        text-align: left;
        line-height: 1.3;
        margin-top: 2px;
      }
    }

    .inputs-config {
      margin-top: 8px;

      .config-item {
        .node-list-item-style();
        justify-content: center;

        .config-label {
          font-size: 10px;
          color: #6b7280;
          font-weight: 500;
        }

        .config-value {
          font-size: 10px;
          color: #9ca3af;
          margin-left: 4px;
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
