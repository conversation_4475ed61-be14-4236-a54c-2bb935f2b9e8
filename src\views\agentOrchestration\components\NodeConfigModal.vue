<template>
  <n-modal v-model:show="visible" :mask-closable="false">
    <n-card
      style="width: 700px; max-height: 80vh;"
      :title="modalTitle"
      :bordered="false"
      size="huge"
      closable
      @close="handleClose"
    >
      <template #header-extra>
        <div class="flex items-center space-x-2">
          <div
            class="node-type-badge px-3 py-1 rounded-full text-xs font-medium"
            :style="{ backgroundColor: nodeColor + '20', color: nodeColor }"
          >
            {{ nodeTypeLabel }}
          </div>
          <div
            class="node-status-indicator w-3 h-3 rounded-full"
            :class="statusClass"
            :title="statusText"
          ></div>
        </div>
      </template>

      <div v-if="node" class="config-modal-content">
        <n-scrollbar style="max-height: 60vh;">
          <n-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-placement="top"
            :show-feedback="true"
            :show-label="true"
          >
            <!-- 基础配置 -->
            <div class="config-section mb-6">
              <div class="section-header">
                <h4 class="section-title">
                  <span class="mr-2">⚙️</span>
                  基础配置
                </h4>
                <n-divider />
              </div>

              <n-grid :cols="2" :x-gap="16">
                <n-grid-item>
                  <n-form-item label="节点名称" path="label">
                    <n-input
                      v-model:value="formData.label"
                      placeholder="请输入节点名称"
                      :maxlength="50"
                      show-count
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="节点ID" path="id">
                    <n-input
                      :value="node.id"
                      readonly
                      placeholder="自动生成"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>

              <n-form-item label="节点描述" path="description">
                <n-input
                  v-model:value="formData.description"
                  type="textarea"
                  placeholder="请输入节点描述（可选）"
                  :rows="3"
                  :maxlength="200"
                  show-count
                />
              </n-form-item>
            </div>

            <!-- 节点特定配置 -->
            <div class="config-section mb-6" v-if="showSpecificConfig">
              <div class="section-header">
                <h4 class="section-title">
                  <span class="mr-2">🔧</span>
                  {{ specificConfigTitle }}
                </h4>
                <n-divider />
              </div>

              <!-- API节点配置 -->
              <template v-if="node.type === 'api'">
                <n-grid :cols="2" :x-gap="16">
                  <n-grid-item>
                    <n-form-item label="请求方法" path="config.method">
                      <n-select
                        v-model:value="formData.config.method"
                        :options="methodOptions"
                        placeholder="选择请求方法"
                      />
                    </n-form-item>
                  </n-grid-item>
                  <n-grid-item>
                    <n-form-item label="超时时间(秒)" path="config.timeout">
                      <n-input-number
                        v-model:value="formData.config.timeout"
                        placeholder="请求超时时间"
                        :min="1"
                        :max="300"
                        :step="1"
                      />
                    </n-form-item>
                  </n-grid-item>
                </n-grid>

                <n-form-item label="接口地址" path="config.url">
                  <n-input
                    v-model:value="formData.config.url"
                    placeholder="请输入API接口地址 (如: https://api.example.com/endpoint)"
                  />
                </n-form-item>

                <n-form-item label="请求头" path="config.headers">
                  <n-input
                    v-model:value="formData.config.headers"
                    type="textarea"
                    placeholder='JSON格式的请求头，例如：&#10;{&#10;  "Content-Type": "application/json",&#10;  "Authorization": "Bearer your-token"&#10;}'
                    :rows="4"
                  />
                </n-form-item>

                <n-form-item label="请求体" path="config.body" v-if="['POST', 'PUT', 'PATCH'].includes(formData.config.method)">
                  <n-input
                    v-model:value="formData.config.body"
                    type="textarea"
                    placeholder='JSON格式的请求体，例如：&#10;{&#10;  "key": "value",&#10;  "data": "{{input}}"&#10;}'
                    :rows="4"
                  />
                </n-form-item>

                <n-form-item label="查询参数" path="config.params" v-if="formData.config.method === 'GET'">
                  <n-input
                    v-model:value="formData.config.params"
                    type="textarea"
                    placeholder='JSON格式的查询参数，例如：&#10;{&#10;  "page": 1,&#10;  "limit": 10&#10;}'
                    :rows="3"
                  />
                </n-form-item>

                <n-grid :cols="2" :x-gap="16">
                  <n-grid-item>
                    <n-form-item label="重试次数" path="config.retries">
                      <n-input-number
                        v-model:value="formData.config.retries"
                        placeholder="失败重试次数"
                        :min="0"
                        :max="5"
                        :step="1"
                      />
                    </n-form-item>
                  </n-grid-item>
                  <n-grid-item>
                    <n-form-item label="响应格式" path="config.responseFormat">
                      <n-select
                        v-model:value="formData.config.responseFormat"
                        :options="responseFormatOptions"
                        placeholder="选择响应格式"
                      />
                    </n-form-item>
                  </n-grid-item>
                </n-grid>
              </template>

              <!-- 条件节点配置 -->
              <template v-if="node.type === 'condition'">
                <n-form-item label="条件类型" path="config.conditionType">
                  <n-select
                    v-model:value="formData.config.conditionType"
                    :options="conditionTypeOptions"
                    placeholder="选择条件类型"
                  />
                </n-form-item>

                <n-form-item label="条件表达式" path="config.condition">
                  <n-input
                    v-model:value="formData.config.condition"
                    placeholder="请输入条件表达式，如: input.value > 100"
                  />
                </n-form-item>

                <n-form-item label="条件说明" path="config.conditionDesc">
                  <n-input
                    v-model:value="formData.config.conditionDesc"
                    type="textarea"
                    placeholder="请输入条件说明，描述判断逻辑"
                    :rows="2"
                  />
                </n-form-item>

                <n-grid :cols="2" :x-gap="16">
                  <n-grid-item>
                    <n-form-item label="True分支标签" path="config.trueBranchLabel">
                      <n-input
                        v-model:value="formData.config.trueBranchLabel"
                        placeholder="条件为真时的分支标签"
                      />
                    </n-form-item>
                  </n-grid-item>
                  <n-grid-item>
                    <n-form-item label="False分支标签" path="config.falseBranchLabel">
                      <n-input
                        v-model:value="formData.config.falseBranchLabel"
                        placeholder="条件为假时的分支标签"
                      />
                    </n-form-item>
                  </n-grid-item>
                </n-grid>
              </template>
            </div>

            <!-- 输入输出配置 -->
            <div class="config-section mb-6" v-if="showIOConfig">
              <div class="section-header">
                <h4 class="section-title">
                  <span class="mr-2">🔄</span>
                  输入输出配置
                </h4>
                <n-divider />
              </div>

              <n-grid :cols="2" :x-gap="16">
                <n-grid-item>
                  <n-form-item label="输入参数">
                    <n-dynamic-input
                      v-model:value="formData.inputParams"
                      :on-create="createInputParam"
                      #="{ index, value }"
                    >
                      <div class="flex space-x-2">
                        <n-input
                          v-model:value="value.name"
                          placeholder="参数名"
                          style="width: 40%"
                        />
                        <n-select
                          v-model:value="value.type"
                          :options="paramTypeOptions"
                          placeholder="类型"
                          style="width: 30%"
                        />
                        <n-input
                          v-model:value="value.description"
                          placeholder="描述"
                          style="width: 30%"
                        />
                      </div>
                    </n-dynamic-input>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="输出参数">
                    <n-dynamic-input
                      v-model:value="formData.outputParams"
                      :on-create="createOutputParam"
                      #="{ index, value }"
                    >
                      <div class="flex space-x-2">
                        <n-input
                          v-model:value="value.name"
                          placeholder="参数名"
                          style="width: 40%"
                        />
                        <n-select
                          v-model:value="value.type"
                          :options="paramTypeOptions"
                          placeholder="类型"
                          style="width: 30%"
                        />
                        <n-input
                          v-model:value="value.description"
                          placeholder="描述"
                          style="width: 30%"
                        />
                      </div>
                    </n-dynamic-input>
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </div>

            <!-- 测试运行 -->
            <div class="config-section" v-if="showTestSection">
              <div class="section-header">
                <h4 class="section-title">
                  <span class="mr-2">▶️</span>
                  测试运行
                </h4>
                <n-divider />
              </div>

              <div class="test-controls mb-4">
                <n-space>
                  <n-button
                    type="primary"
                    @click="handleTest"
                    :loading="isTestRunning"
                    :disabled="!canTest"
                  >
                    ▶️ 运行测试
                  </n-button>

                  <n-button
                    @click="clearTestResult"
                    :disabled="!testResult"
                  >
                    🔄 清除结果
                  </n-button>

                  <span class="text-sm text-gray-500">
                    测试当前节点配置是否正确
                  </span>
                </n-space>
              </div>

              <div v-if="testResult" class="test-result">
                <n-card size="small" :bordered="true">
                  <template #header>
                    <div class="flex items-center justify-between">
                      <span class="font-medium">测试结果</span>
                      <n-tag
                        :type="testResultType"
                        size="small"
                      >
                        {{ testResultStatus }}
                      </n-tag>
                    </div>
                  </template>
                  <n-code
                    :code="testResult"
                    language="json"
                    show-line-numbers
                    word-wrap
                  />
                </n-card>
              </div>
            </div>
          </n-form>
        </n-scrollbar>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <n-button @click="handleClose">取消</n-button>
          <n-button type="primary" @click="handleSave">保存配置</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  NModal,
  NCard,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSlider,
  NButton,
  NIcon,
  NGrid,
  NGridItem,
  NInputNumber,
  NDivider,
  NSpace,
  NTag,
  NCode,
  NScrollbar,
  NDynamicInput,
  useMessage
} from 'naive-ui'
// 使用简单的文本图标替代，避免依赖问题
import type { FlowNode } from '@/store/modules/orchestration'
import { useOrchestrationStore } from '@/store'

const props = defineProps<{
  show: boolean
  node: FlowNode | null
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  save: [config: any]
}>()

const message = useMessage()
const orchestrationStore = useOrchestrationStore()
const formRef = ref()
const isTestRunning = ref(false)
const testResult = ref('')

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = ref({
  label: '',
  description: '',
  config: {} as any,
  inputParams: [] as Array<{name: string, type: string, description: string}>,
  outputParams: [] as Array<{name: string, type: string, description: string}>
})

// 表单验证规则
const formRules = {
  label: [
    { required: true, message: '请输入节点名称', trigger: 'blur' },
    { min: 2, max: 50, message: '节点名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  'config.url': [
    { required: true, message: '请输入API接口地址', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  'config.condition': [
    { required: true, message: '请输入条件表达式', trigger: 'blur' }
  ]
}

// 选项数据
const modelOptions = [
  { label: 'GPT-3.5-turbo', value: 'gpt-3.5-turbo' },
  { label: 'GPT-4', value: 'gpt-4' },
  { label: 'GPT-4-turbo', value: 'gpt-4-turbo' },
  { label: 'Claude-3-haiku', value: 'claude-3-haiku' },
  { label: 'Claude-3-sonnet', value: 'claude-3-sonnet' },
  { label: 'Claude-3-opus', value: 'claude-3-opus' }
]

const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' }
]

const responseFormatOptions = [
  { label: 'JSON', value: 'json' },
  { label: 'Text', value: 'text' },
  { label: 'XML', value: 'xml' },
  { label: 'HTML', value: 'html' }
]

const conditionTypeOptions = [
  { label: '数值比较', value: 'numeric' },
  { label: '字符串匹配', value: 'string' },
  { label: '布尔判断', value: 'boolean' },
  { label: '自定义表达式', value: 'custom' }
]

const paramTypeOptions = [
  { label: 'String', value: 'string' },
  { label: 'Number', value: 'number' },
  { label: 'Boolean', value: 'boolean' },
  { label: 'Object', value: 'object' },
  { label: 'Array', value: 'array' }
]

// 计算属性
const modalTitle = computed(() => {
  if (!props.node) return '节点配置'
  const typeLabels = {
    start: '开始节点配置',
    end: '结束节点配置',
    api: 'API节点配置',
    condition: '条件节点配置'
  }
  return typeLabels[props.node.type as keyof typeof typeLabels] || '节点配置'
})

const nodeTypeLabel = computed(() => {
  if (!props.node) return ''
  const labels = {
    start: '开始节点',
    end: '结束节点',
    api: 'API节点',
    condition: '条件节点'
  }
  return labels[props.node.type as keyof typeof labels] || '未知节点'
})

const nodeColor = computed(() => {
  if (!props.node) return '#d9d9d9'
  const colors = {
    start: '#52c41a',
    end: '#ff4d4f',
    api: '#722ed1',
    condition: '#fa8c16'
  }
  return colors[props.node.type as keyof typeof colors] || '#d9d9d9'
})

const statusClass = computed(() => {
  if (!props.node?.data?.status) return 'status-idle'
  return `status-${props.node.data.status}`
})

const statusText = computed(() => {
  if (!props.node?.data?.status) return '空闲'
  const statusTexts = {
    idle: '空闲',
    running: '运行中',
    success: '成功',
    error: '错误'
  }
  return statusTexts[props.node.data.status as keyof typeof statusTexts] || '未知'
})

const showSpecificConfig = computed(() => {
  return props.node && !['start', 'end'].includes(props.node.type)
})

const showIOConfig = computed(() => {
  return props.node && !['start', 'end'].includes(props.node.type)
})

const specificConfigTitle = computed(() => {
  if (!props.node) return ''
  const titles = {
    api: 'API配置',
    condition: '条件配置'
  }
  return titles[props.node.type as keyof typeof titles] || '特定配置'
})

const showTestSection = computed(() => {
  return props.node && !['start', 'end'].includes(props.node.type)
})

const canTest = computed(() => {
  if (!props.node) return false
  return formData.value.label.trim() !== ''
})

const testResultType = computed(() => {
  if (!testResult.value) return 'default'
  try {
    const result = JSON.parse(testResult.value)
    return result.status === 'success' ? 'success' : 'error'
  } catch {
    return 'error'
  }
})

const testResultStatus = computed(() => {
  if (!testResult.value) return '未测试'
  try {
    const result = JSON.parse(testResult.value)
    return result.status === 'success' ? '测试通过' : '测试失败'
  } catch {
    return '解析错误'
  }
})

// 工具函数
const createInputParam = () => ({
  name: '',
  type: 'string',
  description: ''
})

const createOutputParam = () => ({
  name: '',
  type: 'string',
  description: ''
})

// 监听节点变化，初始化表单数据
watch(() => props.node, (newNode) => {
  if (newNode) {
    formData.value = {
      label: newNode.data.label || '',
      description: newNode.data.description || '',
      config: { ...newNode.data.config } || {},
      inputParams: newNode.data.inputParams || [],
      outputParams: newNode.data.outputParams || []
    }

    // 初始化特定配置的默认值
    if (newNode.type === 'api') {
      if (!formData.value.config.method) formData.value.config.method = 'POST'
      if (!formData.value.config.timeout) formData.value.config.timeout = 30
      if (!formData.value.config.retries) formData.value.config.retries = 3
      if (!formData.value.config.responseFormat) formData.value.config.responseFormat = 'json'
    }
    if (newNode.type === 'condition') {
      if (!formData.value.config.conditionType) formData.value.config.conditionType = 'custom'
      if (!formData.value.config.trueBranchLabel) formData.value.config.trueBranchLabel = '是'
      if (!formData.value.config.falseBranchLabel) formData.value.config.falseBranchLabel = '否'
    }
  }
}, { immediate: true })

// 方法
const handleClose = () => {
  visible.value = false
  testResult.value = ''
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    emit('save', {
      label: formData.value.label,
      description: formData.value.description,
      config: formData.value.config,
      inputParams: formData.value.inputParams,
      outputParams: formData.value.outputParams
    })
    handleClose()
    message.success('节点配置保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    message.error('请检查表单输入')
  }
}

const clearTestResult = () => {
  testResult.value = ''
}

const handleTest = async () => {
  if (!props.node) return

  isTestRunning.value = true
  testResult.value = ''

  try {
    // 先保存当前配置到节点
    orchestrationStore.updateNodeData(props.node.id, {
      ...formData.value.config,
      label: formData.value.label,
      description: formData.value.description
    })

    // 使用store的runNode方法执行节点
    await orchestrationStore.runNode(props.node.id)

    // 获取执行结果
    const updatedNode = orchestrationStore.currentNodes.find(n => n.id === props.node!.id)

    const testData = {
      status: 'success',
      message: '节点测试运行完成',
      timestamp: new Date().toISOString(),
      nodeType: props.node.type,
      nodeId: props.node.id,
      nodeStatus: updatedNode?.data?.status,
      config: formData.value.config,
      executionResult: '节点执行成功'
    }

    testResult.value = JSON.stringify(testData, null, 2)
    message.success('节点测试运行完成')
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    const testData = {
      status: 'error',
      message: '节点测试运行失败',
      error: errorMessage,
      timestamp: new Date().toISOString(),
      nodeType: props.node.type,
      nodeId: props.node.id
    }

    testResult.value = JSON.stringify(testData, null, 2)
    message.error('节点测试运行失败')
  } finally {
    isTestRunning.value = false
  }
}
</script>

<style scoped lang="less">
.config-modal-content {
  .config-section {
    .section-header {
      margin-bottom: 20px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #2f3033;
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }
    }
  }

  .node-type-badge {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .node-status-indicator {
    &.status-idle {
      background-color: #d9d9d9;
    }

    &.status-running {
      background-color: #faad14;
      animation: pulse 2s infinite;
    }

    &.status-success {
      background-color: #52c41a;
    }

    &.status-error {
      background-color: #ff4d4f;
    }
  }

  .test-controls {
    .n-button {
      border-radius: 8px;
    }
  }

  .test-result {
    margin-top: 16px;

    :deep(.n-card) {
      border-radius: 8px;
    }

    :deep(.n-code) {
      border-radius: 6px;
      background: #fafafa;
    }
  }
}

// 表单样式优化
:deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;

  .n-card-header__main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

:deep(.n-form-item-label) {
  font-weight: 500;
  color: #2f3033;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  .n-base-selection {
    border-radius: 6px;
  }
}

:deep(.n-slider) {
  margin: 8px 0;
}

:deep(.n-divider) {
  margin: 8px 0;
}

:deep(.n-dynamic-input) {
  .n-dynamic-input-item {
    margin-bottom: 8px;
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-modal-content {
    :deep(.n-card) {
      width: 90vw !important;
      max-width: none !important;
    }

    .config-section {
      .section-header {
        .section-title {
          font-size: 14px;
        }
      }
    }

    :deep(.n-grid) {
      .n-grid-item {
        grid-column: span 2;
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .config-modal-content {
    .config-section {
      .section-header {
        .section-title {
          color: #ffffff;
        }
      }
    }

    .node-type-badge {
      background-color: rgba(255, 255, 255, 0.1) !important;
      color: #ffffff !important;
    }

    :deep(.n-card) {
      background-color: #1a1a1a;
      border-color: #404040;
    }

    :deep(.n-form-item-label) {
      color: #ffffff;
    }

    :deep(.n-code) {
      background: #2a2a2a !important;
      color: #ffffff;
    }
  }
}
</style>
