import type { FlowNode } from "@/store/modules/orchestration";

// 节点配置组件的通用Props接口
export interface NodeConfigProps {
  node: FlowNode;
  formData: any;
  visible: boolean;
}

// 节点配置组件的通用Events接口
export interface NodeConfigEvents {
  'update:formData': [value: any];
  'save': [config: any];
  'run-node': [];
}

// 模型配置接口
export interface ModelConfig {
  model: string | null;
  modelName: string;
  completionParams: {
    temperature: string;
    maxTokens: string;
    topP: string;
    style: string;
  };
}

// 变量选项接口
export interface VariableOption {
  label: string;
  value: string;
  variables?: Array<{
    name: string;
    type: string;
    id: string;
  }>;
}

// 条件配置接口
export interface ConditionConfig {
  variable: string;
  operator: string;
  field: string;
  value: string;
}

// 条件分支接口
export interface ConditionBranch {
  name: string;
  disabled: boolean;
  conditionArr: ConditionConfig[];
  conditionLogic: string;
}

// 问题分类接口
export interface QuestionCategory {
  id: string;
  name: string;
  des: string;
  isEditing?: boolean;
  disable?: boolean;
}

// 变量赋值接口
export interface VariableAssignment {
  key: string;
  value: string;
  operate: string;
}

// 知识库接口
export interface KnowledgeBase {
  name: string;
  size: string;
  value: string;
}

// 问题建议接口
export interface QuestionSuggestion {
  type: string;
  value: string;
}

// 表单验证规则类型
export type FormRules = Record<string, Array<{
  required?: boolean;
  message: string;
  trigger: string;
}>>;

// 选项数据类型
export interface SelectOption {
  label: string;
  value: string | number;
}

// 聚合选项接口
export interface AggregationOption {
  label: string;
  value: string;
  variables: Array<{
    name: string;
    type: string;
    id: string;
  }>;
}

// 执行日志接口
export interface ExecutionLog {
  id: string;
  status: 'idle' | 'running' | 'success' | 'error';
  timestamp: string;
  duration: number;
  output?: any;
  error?: string;
}

// 节点类型枚举
export enum NodeType {
  START = 'start',
  LLM = 'llm',
  END = 'end',
  QUESTION_CLASSIFIER = 'question-classifier',
  KNOWLEDGE = 'knowledge',
  VARIABLE = 'variable',
  CONDITION = 'condition',
  AGGREGATION = 'aggregation',
  API = 'api',
  QUESTION_CLASSIFIER_NEW = 'questionClassifier'
}

// 节点配置组件映射类型
export type NodeConfigComponentMap = {
  [NodeType.START]: 'StartNodeConfig';
  [NodeType.LLM]: 'LLMNodeConfig';
  [NodeType.END]: 'EndNodeConfig';
  [NodeType.QUESTION_CLASSIFIER]: 'QuestionClassifierNodeConfig';
  [NodeType.KNOWLEDGE]: 'KnowledgeNodeConfig';
  [NodeType.VARIABLE]: 'VariableNodeConfig';
  [NodeType.CONDITION]: 'ConditionNodeConfig';
  [NodeType.AGGREGATION]: 'AggregationNodeConfig';
  [NodeType.API]: 'APINodeConfig';
  [NodeType.QUESTION_CLASSIFIER_NEW]: 'QuestionClassifierNodeConfig';
};
