<template>
	<div
		v-if="visible"
		class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
		@click.self="close"
	>
		<div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-200 scale-100">
			<!-- 头部 -->
			<div class="flex items-center justify-between p-4 border-b border-gray-200">
				<div class="flex items-center gap-2">
					<div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
						<svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
							<path
								d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
						</svg>
					</div>
					<h3 class="text-lg font-semibold text-gray-800">疑难点标记</h3>
				</div>
				<button
					class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded"
					title="关闭"
					@click="close"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
					</svg>
				</button>
			</div>

			<!-- 内容 -->
			<div class="p-4">
				<!-- 选中文本预览 -->
				<div class="mb-4">
					<label class="block text-sm font-medium text-gray-700 mb-2">选中的文本</label>
					<div class="p-3 bg-gray-50 rounded-lg border-l-4 border-blue-400">
						<p class="text-sm text-gray-800 leading-relaxed">
							"{{ selectedText }}"
						</p>
					</div>
				</div>

				<!-- 注释输入 -->
				<div class="mb-4">
					<label class="block text-sm font-medium text-gray-700 mb-2">
						疑难问题 <span class="text-red-500">*</span>
					</label>
					<textarea
						ref="noteTextarea"
						v-model="noteContent"
						class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200"
						placeholder="请描述你的疑难问题..."
						rows="4"
						@keydown.ctrl.enter="save"
						@keydown.esc="close"
					></textarea>
					<div class="flex justify-between items-center mt-2">
						<p class="text-xs text-gray-500">
							支持 Ctrl+Enter 快速保存，ESC 取消
						</p>
						<p class="text-xs text-gray-400">
							{{ noteContent.length }}/500
						</p>
					</div>
				</div>

				<!-- 颜色选择 -->
				<!--				<div class="mb-6">-->
				<!--					<label class="block text-sm font-medium text-gray-700 mb-2">高亮颜色</label>-->
				<!--					<div class="flex gap-2">-->
				<!--						<button-->
				<!--							v-for="color in colorOptions"-->
				<!--							:key="color.value"-->
				<!--							:class="[-->
				<!--                'w-8 h-8 rounded-full border-2 transition-all duration-200 hover:scale-110',-->
				<!--                selectedColor === color.value-->
				<!--                  ? 'border-gray-800 ring-2 ring-blue-200'-->
				<!--                  : 'border-gray-300 hover:border-gray-400'-->
				<!--              ]"-->
				<!--							:style="{ backgroundColor: color.value }"-->
				<!--							:title="color.name"-->
				<!--							@click="selectedColor = color.value"-->
				<!--						>-->
				<!--							<svg-->
				<!--								v-if="selectedColor === color.value"-->
				<!--								class="w-4 h-4 text-white mx-auto"-->
				<!--								fill="currentColor"-->
				<!--								viewBox="0 0 20 20"-->
				<!--							>-->
				<!--								<path clip-rule="evenodd"-->
				<!--											d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"-->
				<!--											fill-rule="evenodd"/>-->
				<!--							</svg>-->
				<!--						</button>-->
				<!--					</div>-->
				<!--				</div>-->
			</div>

			<!-- 底部操作 -->
			<div class="flex gap-3 p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
				<button
					class="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
					@click="close"
				>
					取消
				</button>
				<button
					:class="[
            'flex-1 px-4 py-2 text-white rounded-lg transition-all duration-200',
            noteContent.trim()
              ? 'bg-blue-600 hover:bg-blue-700 hover:shadow-md'
              : 'bg-gray-400 cursor-not-allowed'
          ]"
					:disabled="!noteContent.trim()"
					@click="save"
				>
          <span class="flex items-center justify-center gap-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path clip-rule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										fill-rule="evenodd"/>
            </svg>
            保存疑难点
          </span>
				</button>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import {ref, nextTick, onMounted, onUnmounted, watch} from 'vue'

interface Props {
	visible: boolean
	selectedText: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
	(e: 'close'): void
	(e: 'save', data: { note: string; color: string }): void
}>()

const noteContent = ref('')
const selectedColor = ref('#FDE047')
const noteTextarea = ref<HTMLTextAreaElement>()

// 颜色选项
const colorOptions = [
	{name: '黄色', value: '#FDE047'},
	{name: '绿色', value: '#4ADE80'},
	{name: '蓝色', value: '#60A5FA'},
	{name: '红色', value: '#F87171'},
	{name: '紫色', value: '#A78BFA'},
	{name: '橙色', value: '#FB923C'}
]

// 保存注释
const save = () => {
	if (noteContent.value.trim()) {
		emit('save', {
			note: noteContent.value.trim(),
			color: selectedColor.value
		})
		close()
	}
}

// 关闭模态框
const close = () => {
	noteContent.value = ''
	selectedColor.value = '#FDE047'
	emit('close')
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
	if (!props.visible) return

	if (event.key === 'Escape') {
		close()
	} else if (event.ctrlKey && event.key === 'Enter') {
		save()
	}
}

// 监听visible变化，自动聚焦
watch(() => props.visible, (newVisible) => {
	if (newVisible) {
		nextTick(() => {
			noteTextarea.value?.focus()
		})
	}
})

onMounted(() => {
	document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
	document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 模态框动画 */
.fixed {
	animation: modalFadeIn 0.2s ease-out;
}

@keyframes modalFadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

.transform {
	animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
	from {
		transform: translateY(-16px) scale(0.95);
		opacity: 0;
	}
	to {
		transform: translateY(0) scale(1);
		opacity: 1;
	}
}

/* 自定义滚动条 */
textarea::-webkit-scrollbar {
	width: 6px;
}

textarea::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}

/* 聚焦时的特殊效果 */
textarea:focus {
	box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮悬停效果 */
button:hover {
	transform: translateY(-1px);
}

button:active {
	transform: translateY(0);
}
</style>
