<script setup>
import {onMounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import {NSpin, NStep, NSteps} from 'naive-ui'
import examination from '@/views/chat/components/examination/index.vue'
import chart from '@/views/chat/chat.vue'
import lesson from '@/views/chat/components/lesson/index.vue'
import {useToolsStore, usestretchoutStore} from '@/store'
import {infoStore} from '@/store/modules/info'
import {SvgIcon} from '@/components/common'

const router = useRouter()
const info = infoStore()
const ToolsStore = useToolsStore()
const useTretchOut = usestretchoutStore()
const charts = ref(null)

function jumpPage(url) {
	if (url)
		router.push(url)
	else
		router.go(-1)
}

function fetchWeaknessfun() {
	charts.value.fetchWeaknessAnalysisProcessfun()
}

const show = ref(false)
const loadTap = (flag) => {
	show.value = flag
}

const StepsList = ref([
	{
		name: '意图识别',
	}, {
		name: '查询知识库',
	}, {
		name: '意图分析',
	}, {
		name: '知识点聚合分析',
	}, {
		name: '考题内容生成',
	},
])

onMounted(() => {

})
</script>

<template>
	<NSpin :show="show">
		<template #description>
			正在生成PDF，请稍候...
		</template>
		<div class="chatbg h-full w-full  bg-[#F7F9FF]">
			<header class="headers relative pb-[10vh]">
				<div class="left">
					<div class="gohome" @click="jumpPage('toolboxPage')">
						<img src="@/assets/workShopPage/leftarrow.png">
					</div>
					{{ ToolsStore.ToolInfo.name }}助手
				</div>
				<div v-if="ToolsStore.ToolInfo.category === 7 && info.ToolLoding?.loding && false"
						 class="w-[61%] absolute left-[50%] top-[10%] translate-x-[-50%]">
					<NSteps :current="info.ToolLoding.current" status="wait">
						<NStep
							v-for="(item, index) in StepsList"
							:key="index"
							:title="item.name"
						>
							<template #icon>
								<SvgIcon v-if="info.ToolLoding.current === index + 1" class="text-[#0264FA]"
												 icon="line-md:loading-twotone-loop"/>
							</template>
						</NStep>
					</NSteps>
				</div>
			</header>
			<div class="flex justify-center">
				<transition name="slide-fade">
					<div v-if="useTretchOut.stretchoutStorageInfo.documentisshow" class="h-full w-3/6 bg-[#F7F8FC] documentbg">
						<examination @fetch-weaknessfun="fetchWeaknessfun()"/>
					</div>
				</transition>
				<transition name="slide-fade">
					<div v-if="useTretchOut.stretchoutStorageInfo.lessonShow" class="h-full w-3/6 bg-[#F7F8FC] documentbg">
						<lesson @load-tap="loadTap"/>
					</div>
				</transition>
				<div :class="{ 'w-full': !useTretchOut.stretchoutStorageInfo.documentisshow, 'w-3/6': useTretchOut.stretchoutStorageInfo.documentisshow }"
						 class="autoheight transition-all duration-500">
					<chart ref="charts" @load-tap="loadTap"/>
				</div>
			</div>
		</div>
	</NSpin>
</template>

<style lang="less" scoped>
.chatbg {
	padding-top: 30px;
	// height: 100vh;
}

.leftbox {
	width: 50%;
}

.rightbox {
	width: 50%;
}

.headers {
	height: 40px;
	//display: flex;
	//justify-content: space-between;
	// margin-bottom: 90px;
	.left {
		color: #323233;
		font-size: 20px;
		font-weight: 500;
		line-height: 0;
		letter-spacing: 0;
		line-height: 40px;
		display: flex;
		align-items: center;
		margin-left: 24px;

		.gohome {
			width: 40px;
			height: 40px;
			background: #FAFBFF;
			border: 1px solid #E9ECF3;
			border-radius: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 11px;

			img {
				width: 20px;
				height: 20px;
			}
		}

		span {
			color: #909399;
			margin-left: 8px;

		}
	}

}

.documentbg {
	// width: 796px;
	width: 50%;
	height: 87vh;
	background: #FFFFFF;
	box-shadow: 0 0 10px 0 #00000021;
	border-radius: 4px;
	margin-left: 24px;
	margin-right: 24px;
	margin-top: 24px;
	margin-bottom: 24px;
	overflow-y: auto;
}

.autoheight {
	width: 50%;
	height: 85vh;
	padding-left: 24px;
	padding-right: 32px;
	/* 允许内容溢出时滚动 */
	overflow-y: auto;
	/* 隐藏 WebKit 浏览器的滚动条 */
	scrollbar-width: none;
	/* 隐藏 Firefox 的滚动条 */
	-ms-overflow-style: none;
}

.autoheight::-webkit-scrollbar {
	display: none;
}

/* 优化后的平滑动画效果 */
.slide-fade-enter-active {
	transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-leave-active {
	transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
	transform: translateX(-20px);
	opacity: 0;
}
</style>
