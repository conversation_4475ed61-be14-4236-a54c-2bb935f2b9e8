<template>
  <NModal v-model:show="visible" :mask-closable="false" :close-on-esc="false" class="agent-execution-modal" :style="{
      width: `${modalWidth}px`,
      maxWidth: '90vw'
    }">
    <NCard class="execution-preview-card" :bordered="false" size="huge">
      <!-- 顶部标题栏 -->
      <template #header>
        <div class="modal-header">
          <div class="header-left">
            <div class="execution-title">
              {{ title || '正在生成答案' }}...
            </div>
          </div>
          <div class="header-right">
            <NButton v-if="allowClose" quaternary circle size="small" @click="handleClose">
              <template #icon>
                <NIcon size="25">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18" />
                    <line x1="6" y1="6" x2="18" y2="18" />
                  </svg>
                </NIcon>
              </template>
            </NButton>
          </div>
        </div>
      </template>

      <!-- 中间节点流程展示区 -->
      <div class="execution-content">
        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <div class="progress-steps">
            <div v-for="(node, index) in nodes" :key="node.id" class="progress-step" :class="getStepClass(node.status)">
              <div class="step-number">
                <!-- 已完成状态显示对号 -->
                <svg v-if="node.status === 'completed'" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                  stroke-width="3">
                  <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
                <!-- 其他状态显示数字 -->
                <span v-else>{{ index + 1 }}</span>
              </div>
              <!-- 也是圆形，白色半透明，位置与step-number重叠，比step-number直径大3px，显示在step-number下方 -->
              <div class="step-layer">
                <div class="step-layer-circle"></div>
              </div>
              <div v-if="index < nodes.length - 1" class="step-connector"
                :class="{ 'connector-active': isConnectorActive(index) }" />
            </div>
          </div>
        </div>

        <!-- 节点卡片展示区域 -->
        <div class="nodes-container">
          <div class="nodes-grid">
            <div v-for="(node, index) in nodes" :key="node.id" class="node-card" :class="getNodeCardClass(node.status)">
              <!-- 节点图标 -->
              <div class="node-icon">
                <img v-if="node.icon" :src="node.icon" :alt="node.label">
                <NIcon v-else size="20" color="#125EFF">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                </NIcon>
              </div>

              <!-- 节点内容 -->
              <div class="node-content">
                <div class="node-title">
                  {{ node.label }}
                </div>
                <div v-if="node.details && node.details.length > 0" class="node-details">
                  <div v-for="detail in node.details" :key="detail" class="detail-item">
                    {{ detail }}
                  </div>
                </div>
              </div>

              <!-- 移除文件下载按钮 -->
            </div>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="bottom-actions">
          <NButton v-if="!props.hideDownloadButton" type="primary" size="medium" :disabled="isExecuting"
            @click="handleExportContent">
            教案内容本地导出
          </NButton>
        </div>
      </div>

      <!-- 底部操作按钮已移到内容区域 -->
    </NCard>
  </NModal>
</template>

<script setup lang="ts">
  import { computed, watch } from 'vue'
  import {
    NButton,
    NCard,
    NIcon,
    NModal,
  } from 'naive-ui'
  import type {
    ExecutionNode,
    NodeStatus,
  } from './types'
  import { isExecutionComplete } from './types'

  // 组件属性
  interface Props {
    show: boolean
    title?: string
    nodes: ExecutionNode[]
    allowClose?: boolean
    showStopButton?: boolean
    hideDownloadButton?: boolean
    autoCloseOnComplete?: boolean
    autoCloseDelay?: number
  }

  // 组件事件
  interface Emits {
    (e: 'update:show', value: boolean): void
    (e: 'close'): void
    (e: 'stop'): void
    (e: 'downloadFile', node: ExecutionNode): void
    (e: 'downloadResult'): void
  }

  const props = withDefaults(defineProps < Props > (), {
    title: '正在生成答案',
    allowClose: true,
    showStopButton: true,
    hideDownloadButton: false,
    autoCloseOnComplete: false,
    autoCloseDelay: 3000,
  })

  const emit = defineEmits < Emits > ()

  // 响应式数据
  const visible = computed({
    get: () => props.show,
    set: value => emit('update:show', value),
  })

  // 计算属性
  const isExecuting = computed(() => {
    return props.nodes.some(node => node.status === 'running')
  })

  const executionComplete = computed(() => {
    return isExecutionComplete(props.nodes)
  })

  // 监听执行完成状态，实现自动关闭
  watch(executionComplete, (isComplete) => {
    if (isComplete && props.autoCloseOnComplete) {
      setTimeout(() => {
        emit('update:show', false)
        emit('close')
      }, props.autoCloseDelay)
    }
  })

  // 方法
  const handleClose = () => {
    emit('close')
  }

  const handleStop = () => {
    emit('stop')
  }

  const handleDownloadFile = (node: ExecutionNode) => {
    emit('downloadFile', node)
  }

  const handleExportContent = () => {
    emit('downloadResult')
  }

  const getStepClass = (status: NodeStatus) => {
    return {
      'step-pending': status === 'pending',
      'step-running': status === 'running',
      'step-completed': status === 'completed',
      'step-error': status === 'error',
    }
  }

  const getNodeCardClass = (status: NodeStatus) => {
    return {
      'node-pending': status === 'pending',
      'node-running': status === 'running',
      'node-completed': status === 'completed',
      'node-error': status === 'error',
    }
  }

  const isConnectorActive = (index: number) => {
    const currentNode = props.nodes[index]
    const nextNode = props.nodes[index + 1]
    return currentNode.status === 'completed' && (nextNode.status === 'running' || nextNode.status === 'completed')
  }

  // 计算模态框宽度
  const modalWidth = computed(() => {
    const nodeCount = props.nodes.length
    const isMobile = window.innerWidth <= 768

    if (isMobile) {
      // 移动设备：基于屏幕宽度的95%
      return window.innerWidth * 0.95
    }

    // 桌面设备：根据节点数量计算
    const nodeWidth = 180 // 每个节点卡片最大宽度
    const gap = 16 // 节点间距
    const padding = 80 // 左右内边距
    const calculated = nodeCount * nodeWidth + (nodeCount - 1) * gap + padding

    // 设置最小宽度和最大宽度
    const minWidth = 600
    const maxWidth = Math.min(1400, window.innerWidth * 0.9)

    return Math.max(minWidth, Math.min(calculated, maxWidth))
  })
</script>

<style scoped lang="less">
  /* 主要变量 */
  @primary-color: #125EFF;
  @success-color: #10b981;
  @error-color: #ef4444;
  @warning-color: #f59e0b;
  @text-color: #1f2937;
  @text-secondary: #6b7280;
  @text-muted: #9ca3af;
  @bg-color: #ffffff;
  @border-color: #e5e7eb;
  @shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
  @shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.12);

  .execution-preview-card {
    border-radius: 30px;
    overflow: hidden;
    /* 用渐变色复刻背景图片效果 */
    background: linear-gradient(135deg,
        #E8F4FF 0%,
        #D1E9FF 25%,
        #F0F8FF 50%,
        #E3F1FF 75%,
        #F5FAFF 100%) !important;

    :deep(.n-card-header) {
      padding: 20px 24px 16px;
      border-bottom: none;
      background: transparent;
    }

    :deep(.n-card__content) {
      padding: 0;
    }
  }

  /* 头部样式 */
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;

    .header-left {
      flex: 1;

      .execution-title {
        font-size: 20px;
        font-weight: 600;
        color: @text-color;
      }
    }

    .header-right {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  /* 执行内容区域 */
  .execution-content {
    padding: 24px 20px;
    background: transparent;

    /* 进度指示器 */
    .progress-indicator {
      margin-bottom: 24px;

      .progress-steps {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0;

        .progress-step {
          display: flex;
          align-items: center;
          position: relative;

          .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            z-index: 3;
            position: relative;
            background-image: linear-gradient(180deg, #30A6FF 0%, #1677FF 100%);
            /* border: 4px solid #ffffff; */
            color: white;
          }

          .step-layer {
            position: absolute;
            top: -8px;
            left: -8px;
            width: 66px;
            height: 66px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            z-index: 1;

            .step-layer-circle {
              width: 56px;
              height: 56px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.4);
            }
          }

          .step-connector {
            width: 146px;
            height: 6px;
            /* background: #2a56ad; */
            background: #ffffffb3;
            transition: all 0.3s ease;

            &.connector-active {
              background-image: linear-gradient(90deg, #1677FF 0%, #30A6FF 100%);
            }
          }

          /* 不同状态的样式 */
          &.step-pending .step-number {
            background-image: linear-gradient(180deg, #EEEEEE 0%, #E2E2E2 100%);
            font-family: Arial-Black;
            font-weight: 900;
            font-size: 24px;
            color: #FFFFFF;
            letter-spacing: 0;
          }

          /* ====== 🌟 转圈效果核心实现区域 🌟 ====== */
          &.step-running {
            /* 1️⃣ 设置相对定位，为伪元素提供定位基准 */
            position: relative;

            /* 2️⃣ 中心数字圆圈的样式 */
            .step-number {
              background-image: linear-gradient(180deg, #E4EFFF 0%, #FAFBFF 100%);
              /* 浅蓝色背景 */
              font-family: Arial-Black;
              font-weight: 900;
              font-size: 24px;
              color: #125EFF;
              letter-spacing: 0;
              /* 蓝色字体 */
              /* border-color: rgba(255, 255, 255, 0.3); */
              /* 蓝色边框 */
            }

            /* 3️⃣ 【核心】流星转圈效果 - 在边框上方运行 */
            &::before {
              content: '';
              /* 必须设置content才能显示伪元素 */
              position: absolute;
              /* 绝对定位，相对于父元素 */
              top: -4px;
              /* 与数字圆圈对齐 */
              left: -4px;
              /* 与数字圆圈对齐 */
              width: 58px;
              /* 流星圈直径，稍小于数字圈以避免完全覆盖 */
              height: 58px;
              /* 保持正圆 */
              border-radius: 50%;
              /* 变成圆形 */
              /* 🎯 核心渐变：conic-gradient 创建圆锥渐变，形成流星拖尾效果 */
              background: conic-gradient(from 0deg,
                  /* 从0度开始 */
                  transparent,
                  /* 起始透明 */
                  transparent,
                  /* 大部分透明 */
                  #125EFF,
                  /* 流星尾巴颜色 */
                  transparent
                  /* 结束透明 */
                );
              /* 🔄 应用旋转动画：1.5秒线性无限循环 */
              animation: meteor-spin 1.5s linear infinite;
              z-index: 2;
              /* 层级2，在边框上方但在数字文字下方 */
            }

            /* 4️⃣ 中间遮罩层 - 遮挡流星内圈，形成环形效果 */
            &::after {
              content: '';
              /* 必须设置content */
              position: absolute;
              /* 绝对定位 */
              top: 10px;
              /* 更小的内圈遮罩，让流星形成环形 */
              left: 10px;
              /* 更小的内圈遮罩 */
              width: 30px;
              /* 内圈遮罩直径（让流星只在边缘显示环形效果） */
              height: 30px;
              /* 保持正圆 */
              border-radius: 50%;
              /* 圆形遮罩 */
              background: rgba(255, 255, 255, 0.1);
              /* 半透明白色背景，遮挡内圈流星 */
              z-index: 5;
              /* 层级5，在流星上方，确保遮罩生效 */
            }

            /* 5️⃣ 确保数字文字在最上层显示 */
            .step-number {
              position: relative;
              /* 相对定位 */
              z-index: 3;
              /* 层级3，在流星和所有伪元素上方 */

              /* 确保数字文字本身单独分层 */
              span,
              svg {
                position: relative;
                z-index: 4;
                /* 数字/对号文字在最顶层 */
              }
            }
          }

          /* ====== 🌟 转圈效果核心实现区域结束 🌟 ====== */

          &.step-completed .step-number {
            background-image: linear-gradient(180deg, #30A6FF 0%, #1677FF 100%);
            color: white;

            svg {
              width: 20px;
              height: 20px;
            }
          }

          &.step-error .step-number {
            background-image: linear-gradient(180deg, #EF4444 0%, #DC2626 100%);
            color: white;
          }
        }
      }
    }

    /* 节点容器 */
    .nodes-container {
      .nodes-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        justify-content: center;

        .node-card {
          background: #ffffff;
          border: 1px solid #e5e7eb;
          border-radius: 12px;
          padding: 20px;
          transition: all 0.3s ease;
          position: relative;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          flex: 1;
          min-width: 160px;
          max-width: 180px;
          min-height: 200px;

          &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
          }

          /* 节点状态样式 */
          &.node-pending {
            border-color: #ffffff;
            background-image: linear-gradient(180deg, #f3f7ff 0%, #fdfdfe 100%);
          }

          &.node-running {
            border-color: #bbe0fe;
            background: transparent;
            box-shadow: 0 0 0 2px rgba(18, 94, 255, 0.1);
          }

          &.node-completed {
            border-color: #99c4ff;
            background-image: linear-gradient(180deg, #eef2fe 0%, #e1f3ff 100%);
          }

          &.node-error {
            border-color: @error-color;
            background: #fef2f2;
          }

          .node-icon {
            width: 58px;
            height: 58px;
            border-radius: 10px;
            background: #f0f8ff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;

            img {
              width: 48px;
              height: 48px;
            }
          }

          .node-content {
            text-align: center;

            .node-title {
              font-size: 15px;
              font-weight: 600;
              color: @text-color;
              margin-bottom: 10px;
              line-height: 1.3;
            }

            .node-details {
              .detail-item {
                font-size: 12px;
                color: @text-secondary;
                line-height: 1.4;
                margin-bottom: 3px;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }

    /* 底部操作按钮 */
    .bottom-actions {
      display: flex;
      justify-content: center;
      margin-top: 32px;

      .n-button {
        padding: 12px 32px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px;
      }
    }
  }

  /* ====== 🎬 流星转圈动画定义 🎬 ====== */
  @keyframes meteor-spin {

    /* 动画开始：0度（12点钟方向） */
    0% {
      transform: rotate(0deg);
      /* 无旋转 */
    }

    /* 动画结束：360度（完成一圈，回到12点钟方向） */
    100% {
      transform: rotate(360deg);
      /* 顺时针旋转一整圈 */
    }

    /* 
    💡 使用说明：
    - 动画时长：1.5s（在 ::before 中设置）
    - 动画类型：linear（匀速旋转，无加速减速）
    - 循环次数：infinite（无限循环）
    - 效果：流星会以1.5秒的速度匀速绕圆圈旋转
    
    🔧 可调整参数：
    - 修改动画时长：在 animation 属性中改变 1.5s
    - 修改旋转方向：100% 改为 rotate(-360deg) 可逆时针
    - 修改动画曲线：linear 改为 ease-in-out 等
    */
  }

  /* ====== 🎬 流星转圈动画定义结束 🎬 ====== */

  /* 响应式设计 */
  @media (max-width: 768px) {
    .execution-preview-card {
      :deep(.n-card-header) {
        padding: 16px 16px 12px;
      }
    }

    .execution-content {
      padding: 16px 12px;

      .progress-indicator {
        margin-bottom: 20px;

        .progress-steps {
          .progress-step {
            .step-number {
              width: 32px;
              height: 32px;
              font-size: 14px;
            }

            .step-connector {
              width: 146px;
              height: 6px;
            }
          }
        }
      }

      .nodes-container {
        .nodes-grid {
          gap: 8px;

          .node-card {
            padding: 10px;
            min-width: 100px;
            max-width: 120px;

            .node-icon {
              width: 32px;
              height: 32px;
              margin-bottom: 8px;

              img {
                width: 16px;
                height: 16px;
              }
            }

            .node-content {
              .node-title {
                font-size: 12px;
                margin-bottom: 6px;
              }

              .node-details {
                .detail-item {
                  font-size: 9px;
                }
              }
            }
          }
        }
      }

      .bottom-actions {
        margin-top: 20px;
      }
    }

    .modal-header {
      .header-left {
        .execution-title {
          font-size: 16px;
        }
      }
    }
  }
</style>