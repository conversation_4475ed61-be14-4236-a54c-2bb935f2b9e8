<template>
    <div v-if="visible" class="progress-modal-overlay" @click="handleOverlayClick">
        <div class="progress-modal" @click.stop>
            <div class="progress-header">
                <h2>{{ isCompleted ? '正在生成教案' : '正在生成教案' }}</h2>
            </div>

            <div class="progress-content">
                <div class="steps-container">
                    <div v-for="(step, index) in steps" :key="index" class="step-item" :class="getStepClass(index)">
                        <div class="step-circle">
                            <span v-if="getStepStatus(index) === 'completed'">✓</span>
                            <span v-else-if="getStepStatus(index) === 'loading'">
                                <div class="loading-spinner"></div>
                            </span>
                            <span v-else>{{ index + 1 }}</span>
                        </div>
                        <div class="step-content">
                            <h3>{{ step.title }}</h3>
                            <div class="step-details">
                                <div v-for="(detail, idx) in step.details" :key="idx" class="detail-item">
                                    {{ detail }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="currentStep < steps.length - 1" class="connecting-lines">
                    <div v-for="i in 3" :key="i" class="line" :class="{ 'completed': currentStep >= i }"></div>
                </div>

                <div v-if="isCompleted" class="download-section">
                    <button class="download-btn" @click="handleDownload">
                        教案内容本地导出
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, computed, watch, onMounted, readonly } from 'vue'

    const props = defineProps({
        visible: {
            type: Boolean,
            default: false
        },
        autoProgress: {
            type: Boolean,
            default: true
        },
        onDownload: {
            type: Function,
            default: null
        },
        onClose: {
            type: Function,
            default: null
        }
    })

    const emit = defineEmits(['update:visible', 'download', 'close'])

    const currentStep = ref(0)
    const isCompleted = ref(false)

    const steps = [
        {
            title: '识别必要条件',
            details: [
                '1.识别到课程信息',
                '2.识别到讲授主题',
                '3.提炼出核心知识点'
            ]
        },
        {
            title: '知识点查询',
            details: [
                '1.识别到课程信息',
                '2.识别到讲授主题'
            ]
        },
        {
            title: '知识库检索',
            details: [
                '1.识别到课程信息',
                '2.识别到讲授主题'
            ]
        },
        {
            title: '教案内容生成',
            details: [
                '1.识别到课程信息',
                '2.识别到讲授主题'
            ]
        }
    ]

    const getStepStatus = (index) => {
        if (index < currentStep.value) return 'completed'
        if (index === currentStep.value && !isCompleted.value) return 'loading'
        if (isCompleted.value) return 'completed'
        return 'pending'
    }

    const getStepClass = (index) => {
        const status = getStepStatus(index)
        return {
            'step-completed': status === 'completed',
            'step-loading': status === 'loading',
            'step-pending': status === 'pending'
        }
    }

    const handleOverlayClick = () => {
        if (isCompleted.value) {
            // emit('update:visible', false)
            // emit('close')
        }
    }

    const handleDownload = () => {
        emit('download')
        if (props.onDownload) {
            props.onDownload()
        }
    }

    /* 自动进度模拟 */
    const simulateProgress = () => {
        if (!props.autoProgress) return

        const interval = setInterval(() => {
            if (currentStep.value < steps.length - 1) {
                currentStep.value++
            } else {
                isCompleted.value = true
                clearInterval(interval)
            }
        }, 2000) /* 每2秒进入下一步 */
    }

    /* 监听visible变化，重置进度 */
    watch(() => props.visible, (newVal) => {
        if (newVal) {
            currentStep.value = 0
            isCompleted.value = false
            setTimeout(() => {
                simulateProgress()
            }, 500)
        }
    })

    /* 暴露方法给父组件 */
    const nextStep = () => {
        if (currentStep.value < steps.length - 1) {
            currentStep.value++
        } else {
            isCompleted.value = true
        }
    }

    const resetProgress = () => {
        currentStep.value = 0
        isCompleted.value = false
    }

    defineExpose({
        nextStep,
        resetProgress,
        currentStep: readonly(currentStep),
        isCompleted: readonly(isCompleted)
    })
</script>

<style scoped lang="less">
    .progress-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .progress-modal {
        background: white;
        border-radius: 16px;
        padding: 32px;
        width: 90%;
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    }

    .progress-header {
        text-align: center;
        margin-bottom: 40px;

        h2 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
    }

    .progress-content {
        position: relative;
    }

    .steps-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: relative;
        margin-bottom: 40px;
    }

    .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        max-width: 180px;

        &:not(:last-child) {
            margin-right: 20px;
        }
    }

    .step-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 16px;
        position: relative;
        transition: all 0.3s ease;

        .step-completed & {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        .step-loading & {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        .step-pending & {
            background: #e5e7eb;
            color: #9ca3af;
        }
    }

    .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .step-content {
        text-align: center;

        h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 12px 0;

            .step-completed &,
            .step-loading & {
                color: #6366f1;
            }

            .step-pending & {
                color: #9ca3af;
            }
        }
    }

    .step-details {
        font-size: 12px;
        line-height: 1.5;

        .step-completed &,
        .step-loading & {
            color: #4b5563;
        }

        .step-pending & {
            color: #9ca3af;
        }
    }

    .detail-item {
        margin-bottom: 4px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .connecting-lines {
        position: absolute;
        top: 30px;
        left: 0;
        right: 0;
        height: 2px;
        display: flex;
        justify-content: space-between;
        z-index: -1;
    }

    .line {
        flex: 1;
        height: 2px;
        background: #e5e7eb;
        margin: 0 30px;
        transition: background-color 0.3s ease;

        &.completed {
            background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
        }
    }

    .download-section {
        text-align: center;
        margin-top: 40px;
    }

    .download-btn {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
        }

        &:active {
            transform: translateY(0);
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .progress-modal {
            width: 95%;
            padding: 24px;
            margin: 20px;
        }

        .steps-container {
            flex-direction: column;
            align-items: center;
        }

        .step-item {
            max-width: none;
            width: 100%;
            margin-bottom: 32px;

            &:not(:last-child) {
                margin-right: 0;
            }
        }

        .connecting-lines {
            display: none;
        }

        .step-content {
            max-width: 300px;
        }
    }
</style>