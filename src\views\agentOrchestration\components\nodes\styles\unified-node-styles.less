// 统一节点样式基础
// 根据截图设计的统一样式规范

// 基础变量
@node-bg-color: #ffffff;
@node-border-color: transparent; // 去掉边框，使用透明色
@node-border-radius: 8px;
@node-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); // 减轻阴影
@node-hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.12); // 减轻hover阴影
@node-selected-border: #125EFF; // 使用统一的蓝色
@node-selected-shadow: 0 0 0 2px rgba(18, 94, 255, 0.2);

// 连接点样式
@handle-size: 8px;
@handle-color: #1890ff;
@handle-bg: #ffffff;
@handle-border: 2px solid @handle-color;

// 字体样式
@node-title-size: 12px; // 减小标题字体
@node-title-weight: 500;
@node-title-color: #1f2937;
@node-subtitle-size: 11px; // 减小副标题字体
@node-subtitle-color: #6b7280;
@node-label-size: 10px; // 减小标签字体
@node-label-color: #9ca3af;

// 图标样式
@icon-size: 14px; // 减小图标尺寸
@icon-color: #6b7280;

// 基础节点样式混合
.base-node-style() {
  position: relative;
  
  .node-body {
    background: @node-bg-color;
    border: 1px solid @node-border-color;
    border-radius: @node-border-radius;
    box-shadow: @node-shadow;
    // 精确控制transition，避免transform相关的抖动
    transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
    padding: 8px; // 减小内边距

    &:hover {
      box-shadow: @node-hover-shadow;
      border-color: darken(@node-border-color, 10%);
    }
  }

  // Vue Flow拖拽状态下禁用transition
  &.vue-flow__node-dragging .node-body {
    transition: none !important;
  }
  
  &.selected .node-body {
    border-color: @node-selected-border;
    box-shadow: @node-selected-shadow, @node-hover-shadow;
  }
  
  &.running .node-body {
    border-color: #faad14;
    box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.3), @node-hover-shadow;
  }
}

// 圆形节点样式混合 - 调整为扁平椭圆形
.circular-node-style() {
  .base-node-style();

  .node-body {
    width: 100px; // 减小宽度
    height: 36px; // 减小高度
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 4px 8px; // 减少内边距
  }
}

// 矩形节点样式混合
.rectangular-node-style() {
  .base-node-style();

  .node-body {
    min-width: 140px; // 减小最小宽度
    min-height: 70px; // 减小最小高度
    display: flex;
    flex-direction: column;
    gap: 6px; // 减小间距
  }
}

// 节点标题样式
.node-title-style() {
  font-size: @node-title-size;
  font-weight: @node-title-weight;
  color: @node-title-color;
  line-height: 1.2;
  margin: 0;
}

// 节点副标题样式
.node-subtitle-style() {
  font-size: @node-subtitle-size;
  color: @node-subtitle-color;
  line-height: 1.3;
  margin: 0;
}

// 节点标签样式
.node-label-style() {
  font-size: @node-label-size;
  color: @node-label-color;
  line-height: 1.2;
  margin: 0;
}

// 图标样式
.node-icon-style() {
  width: @icon-size;
  height: @icon-size;
  color: @icon-color;
  flex-shrink: 0;
}

// 连接点统一样式
.unified-handle-style() {
  :deep(.vue-flow__handle) {
    width: @handle-size;
    height: @handle-size;
    background: @handle-bg;
    border: @handle-border;
    border-radius: 50%;
    // 分离transform和其他属性的transition
    transition: border-color 0.2s ease, background-color 0.2s ease;

    &:hover {
      transform: scale(1.2);
      transform-origin: center;
      border-color: darken(@handle-color, 10%);
      // 为transform单独设置快速transition
      transition: border-color 0.2s ease, background-color 0.2s ease, transform 0.1s ease;
    }

    &.vue-flow__handle-connecting {
      border-color: #52c41a;
      background: #f6ffed;
    }
  }
  :deep(.input-handle) {
    &:hover {
      transform: translateY(-50%) translateX(-50%) scale(1.2);
      transform-origin: center;
      border-color: darken(@handle-color, 10%);
      // 为transform单独设置快速transition
      transition: border-color 0.2s ease, background-color 0.2s ease, transform 0.1s ease;
    }
  }

  :deep(.output-handle) {
    &:hover {
      transform: translateY(-50%) translateX(50%) scale(1.2);
      transform-origin: center;
      border-color: darken(@handle-color, 10%);
      // 为transform单独设置快速transition
      transition: border-color 0.2s ease, background-color 0.2s ease, transform 0.1s ease;
    }
  }

  // Vue Flow拖拽状态下禁用连接点的hover效果
  &.vue-flow__node-dragging :deep(.vue-flow__handle) {
    transition: none !important;

    &:hover {
      transform: none !important;
      border-color: @handle-color;
    }
  }


}

// 节点头部样式（包含图标和标题）
.node-header-style() {
  display: flex;
  align-items: center;
  gap: 6px; // 减小间距
  margin-bottom: 3px; // 减小底部边距

  .node-icon {
    .node-icon-style();
  }

  .node-title {
    .node-title-style();
    flex: 1;
  }
}

// 节点内容区域样式
.node-content-style() {
  display: flex;
  flex-direction: column;
  gap: 2px; // 减小间距

  .node-subtitle {
    .node-subtitle-style();
  }

  .node-description {
    .node-description-style();
  }
}

// 节点描述信息样式
.node-description-style() {
  font-size: 10px; // 减小字体
  color: #9ca3af;
  line-height: 1.3; // 减小行高
  margin: 2px 0; // 减小边距
  padding: 0;
  word-wrap: break-word;
  word-break: break-word;

  // 文本截断处理 - 最多显示2行
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  // 空描述时隐藏
  &:empty {
    display: none;
    margin: 0;
  }
}

// 节点描述容器样式（用于包装描述信息）
.node-description-container() {
  .node-description {
    .node-description-style();
  }

  // 当有描述信息时，调整间距
  &:has(.node-description:not(:empty)) {
    .node-header {
      margin-bottom: 6px;
    }
  }
}

// 节点列表项样式
.node-list-item-style() {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  background: #f9fafb;
  border-radius: 4px;
  margin-bottom: 2px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .item-label {
    .node-label-style();
    flex: 1;
  }
  
  .item-value {
    .node-label-style();
    color: @node-subtitle-color;
    font-weight: 500;
  }
}

// 状态指示器样式
.status-indicator-style() {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &.status-idle {
    background: #d1d5db;
  }
  
  &.status-running {
    background: #f59e0b;
    animation: pulse-dot 1.5s infinite;
  }
  
  &.status-success {
    background: #10b981;
  }
  
  &.status-error {
    background: #ef4444;
  }
}

// 动画定义
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .base-node-style() {
    .node-body {
      padding: 8px;
    }
  }
  
  .rectangular-node-style() {
    .node-body {
      min-width: 140px;
      min-height: 70px;
    }
  }
  
  .circular-node-style() {
    .node-body {
      width: 50px;
      height: 50px;
    }
  }
}
