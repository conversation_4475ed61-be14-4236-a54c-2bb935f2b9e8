<script lang="ts">
import type { MenuOption } from "naive-ui";
import { NMenu } from "naive-ui";
import { defineComponent, h, ref, watch, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/store";

import icon1 from "@/assets/sider/icon1.png";
import icon2 from "@/assets/sider/icon2.png";
import icon3 from "@/assets/sider/icon3.png";
import icon4 from "@/assets/sider/icon4.png";
import icon5 from "@/assets/sider/icon5.png";
import icon6 from "@/assets/sider/icon6.png";
import checkicon1 from "@/assets/sider/checkicon1.png";
import checkicon2 from "@/assets/sider/checkicon2.png";
import checkicon3 from "@/assets/sider/checkicon3.png";
import checkicon4 from "@/assets/sider/checkicon4.png";
import checkicon5 from "@/assets/sider/checkicon5.png";
import checkicon6 from "@/assets/sider/checkicon6.png";

const menuOptions: MenuOption[] = [
  {
    label: "智能体广场",
    key: "/toolboxPage",
    iconurl: icon1,
    checkicon: checkicon1,
    ischecked: false,
  },
  {
    label: "智教应用汇",
    key: "/applicationPage",
    iconurl: icon2,
    checkicon: checkicon2,
    ischecked: false,
  },
  {
    label: "智教共创汇",
    key: "/coCreationPage",
    iconurl: icon3,
    checkicon: checkicon3,
    ischecked: false,
  },
  {
    label: "智能孵化仓",
    key: "contact",
    iconurl: icon4,
    checkicon: checkicon4,
    ischecked: false,
    children: [
      {
        label: "智能体工坊",
        key: "/workShopPage",
      },
      {
        label: "知识库工厂",
        key: "/knowledgeFactoryPage",
      },
      {
        label: "提示词管理",
        key: "/promptManagement",
      },
    ],
  },
  {
    label: "智教大课堂",
    key: "/classroom",
    iconurl: icon5,
    checkicon: checkicon5,
    ischecked: false,
  },
  // {
  //   label: "智能体编排",
  //   key: "/agentOrchestration",
  //   iconurl: icon6,
  //   checkicon:checkicon6,
  //   ischecked: false,
  // },
];
var activeKey = ref("");
const clearactiveKey = () => {
  activeKey.value = "";
  menuOptions.forEach((item) => {
    item.ischecked = false;
  });
};

export default defineComponent({
  components: {
    NMenu,
  },
  setup() {
    // 新增：初始化路由实例
    const router = useRouter();
    const route = useRoute();
    const appStore = useAppStore();

    // 获取收缩状态
    const collapsed = computed(() => appStore.siderCollapsed);
    watch(
      () => route.fullPath,
      (newVal, oldVal) => {
        console.log("newVal", newVal);
        if (route.name != "tankChat") {
          activeKey.value = newVal;
        } else {
          activeKey.value = "";
        }
        menuOptions.forEach((item) => {
          item.ischecked = item.key === newVal;
        });
      },
      { immediate: true }
    );
    const handleMenuChange = (key: string) => {
      menuOptions.forEach((item) => {
        item.ischecked = item.key === key;
      });
      router.push(key);
    };
    const renderMenuIcon = (option: MenuOption) => {
      if (option.iconurl) {
        if (option.ischecked) {
          return h(
            "img",
            { src: option.checkicon, width: 24, height: 24 },
            { default: () => null }
          );
        } else {
          return h(
            "img",
            { src: option.iconurl, width: 24, height: 24 },
            { default: () => null }
          );
        }
      } else {
        return null;
      }
    };

    return {
      route,
      activeKey,
      menuOptions,
      handleMenuChange,
      renderMenuIcon,
      clearactiveKey,
      collapsed,
    };
  },
});
</script>

<template>
  <n-menu
    :collapsed="collapsed"
    :collapsed-width="64"
    :collapsed-icon-size="20"
    :icon-size="20"
    v-model:value="activeKey"
    :root-indent="36"
    :indent="12"
    :options="menuOptions"
    :render-icon="renderMenuIcon"
    @update:value="handleMenuChange"
  />
</template>
<style lang="less" scoped>
:deep(.n-menu-item) {
  padding-left: 16px !important;
  padding-right: 16px !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}
:deep(.n-menu-item-content) {
  height: 44px !important;
  padding-left: 15px !important;
  padding-right: 0px !important;
  position: static !important;

  .n-menu-item-content__icon {
    padding-right: 12px !important;
  }

  .n-menu-item-content-header {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #2f3033 !important;
    letter-spacing: 0;
  }
}
:deep(.n-menu-item-content--selected::before) {
  background: #fff !important;
  border: 1px solid #125eff26 !important;
  border-radius: 12px !important;
}
:deep(.n-menu-item-content--selected) {
  .n-menu-item-content-header {
    color: #125eff !important;
  }
}
:deep(.n-submenu-children) {
  .n-menu-item-content {
    padding-left: 70px !important;
  }
}

:deep(.n-menu-item::hover) {
  background: #fff !important;
  border: 1px solid #125eff26 !important;
  border-radius: 12px !important;
}
:deep(.n-menu-item-content::hover) {
  background: #fff !important;
  border: 1px solid #125eff26 !important;
  border-radius: 12px !important;
}

/* 收缩状态下的菜单样式 */
.collapsed-menu {
  padding: 8px 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.collapsed-menu-item {
  width: 48px;
  height: 48px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.collapsed-menu-item:hover {
  background: #fff;
  border: 1px solid #125eff26;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
}

.collapsed-menu-item.active {
  background: #fff;
  border: 1px solid #125eff26;
  box-shadow: 0 2px 8px rgba(18, 94, 255, 0.15);
}

.menu-icon-wrapper {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.menu-icon {
  width: 24px !important;
  height: 24px !important;
  object-fit: contain;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.debug-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  color: #125eff;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 4px;
  border-radius: 4px;
  pointer-events: none;
}

/* 悬停提示框样式 */
.tooltip-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  min-width: 160px;
  max-width: 220px;
  overflow: hidden;
}

.tooltip-header {
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.tooltip-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.tooltip-main-item {
  padding: 8px 12px;
  font-size: 13px;
  color: #125eff;
  background: rgba(18, 94, 255, 0.08);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  font-weight: 500;
}

.tooltip-main-item:hover {
  background: rgba(18, 94, 255, 0.15);
  transform: translateY(-1px);
}

.tooltip-children {
  padding: 8px 0;
}

.tooltip-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.06);
  margin: 8px 16px;
}

.tooltip-subtitle {
  padding: 4px 16px 8px;
  font-size: 11px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tooltip-child-item {
  padding: 8px 16px;
  font-size: 13px;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tooltip-child-item:hover {
  background: #f8fafc;
  color: #125eff;
}

.child-icon {
  color: #94a3b8;
  font-size: 12px;
  transition: all 0.2s ease;
}

.tooltip-child-item:hover .child-icon {
  color: #125eff;
  transform: translateX(2px);
}

.child-label {
  flex: 1;
  font-weight: 500;
}

/* 覆盖 Naive UI 的 tooltip 样式 */
:deep(.n-tooltip__content) {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
</style>
