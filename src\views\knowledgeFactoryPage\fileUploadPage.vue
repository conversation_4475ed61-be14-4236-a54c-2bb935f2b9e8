<template>
  <NSpin :show="loading">
    <div class="app">
      <header class="headers">
        <div class="left">
          <div class="gohome" @click="jumpPage()">
            <img src="@/assets/workShopPage/leftarrow.png" />
          </div>
          知识库工厂 / <span> 知识文件上传</span>
        </div>
        <div class="headbtnrow">
          <NButton @click="jumpPage()">取消</NButton>
          <NButton type="primary" @click="send">保存</NButton>
        </div>
      </header>
      <div class="setconbg">
        <div>
          <n-upload
            multiple
            directory-dnd
            :default-file-list="fileList"
            :action="ossAction"
            @before-upload="beforeUpload"
            :headers="getUploadHeaders"
            :data="setdata"
            :on-finish="handleFinish"
            :show-file-list="false"
          >
            <n-upload-dragger>
              <div class="uploadbox">
                <img src="@/assets/knowledgeFactoryPage/unloadicon.png" />
                <div class="uploadcon">
                  <div class="uptitle">
                    点击上传，或将文件拖放至此处（支持多文件）
                  </div>
                  <p class="upcon">
                    支持的文档类型有：pdf、docx、doc、pptx、ppt、txt、xlsx、xls、csv、json、xml、et、wps
                  </p>
                  <p class="upcon">支持的图片类型有：jpg、jpeg、png</p>
                  <n-divider />
                  <p class="upcon">上传的单个文件大小不超过30M</p>
                </div>
              </div>
            </n-upload-dragger>
          </n-upload>
        </div>
        <div style="margin-top: 24px">
          <n-data-table
            :columns="filecolumns"
            :data="filedata"
            :bordered="false"
            striped
          />
        </div>
      </div>
    </div>
  </NSpin>
</template>
  
  <script setup>
import { ref, computed, h, onMounted } from "vue";
import {
  NInput,
  NDivider,
  NDataTable,
  NButton,
  NUpload,
  NUploadDragger,
  useMessage,
  NSpin,
} from "naive-ui";
import { useRouter, useRoute } from "vue-router";
import { savefilesApi, downloadfileApi } from "@/api/knowledgeFactory";
import signMd5Utils from "@/utils/encryption/signMd5Utils";
import { ConfigEnum, RequestEnum } from "@/enums/httpEnum";
import { useCrypto } from "@/hooks/useSystem";
import { getToken } from "@/utils/microFrontEnd";
const encryptRequestResponse = import.meta.env
  .VITE_GLOB_ENCRYPT_REQUEST_RESPONSE;

const router = useRouter();
const route = useRoute();
var message = useMessage();

var id = ref("");
var fileList = ref([]);
const loading = ref(false);
const device = window.$Eucp?.auth?.getBrowserFingerprint();
const ossAction = ref(
  `${import.meta.env.VITE_GLOB_API_URL}/emind/knowledge_base/upload`
);

// 记录每个文件的唯一时间戳
const fileTimestampMap = new Map();

async function beforeUpload(data) {
  const allowedTypes = [
    // 图片
    "image/jpeg",
    "image/jpg",
    "image/png",
    // 文档
    "application/pdf",
    "application/msword", // doc
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // docx
    "application/vnd.ms-powerpoint", // ppt
    "application/vnd.openxmlformats-officedocument.presentationml.presentation", // pptx
    "application/vnd.ms-excel", // xls
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // xlsx
    "text/plain", // txt
    "text/csv", // csv
    "application/json", // json
    "application/xml", // xml
  ];
  // 允许的扩展名（全部小写）
  const allowedExts = [
    "pdf",
    "docx",
    "doc",
    "pptx",
    "ppt",
    "txt",
    "xlsx",
    "xls",
    "csv",
    "json",
    "xml",
    "et",
    "wps",
    "jpg",
    "jpeg",
    "png",
  ];

  const file = data.file.file;
  const fileType = file?.type;
  const fileName = file?.name || "";
  const ext = fileName.split(".").pop()?.toLowerCase();

  // 检查文件类型和扩展名
  if (!allowedTypes.includes(fileType) && !allowedExts.includes(ext)) {
    message.error(
      "只能上传jpg、jpeg、png图片或pdf、docx、doc、pptx、ppt、txt、xlsx、xls、csv、json、xml、et、wps文档"
    );
    return false;
  }

  // 检查文件大小 (30MB = 30 * 1024 * 1024 bytes)
  const maxSize = 30 * 1024 * 1024;

  if (file?.size > maxSize) {
    message.error("单个文件大小不能超过30M");
    return false;
  }

  // 为每个文件分配唯一timestamp（只用时间戳+随机数，保证唯一）
  const uniqueTimestamp =
    Date.now().toString() + Math.floor(Math.random() * 10000);
  fileTimestampMap.set(fileName, uniqueTimestamp);

  // 检查是否多文件上传
  if (data.fileList && data.fileList.length > 1) {
    console.log(data.fileList);
    console.log(
      fileTimestampMap,
      `您正在上传${data.fileList.length}个文件，每个文件将分配唯一时间戳`
    );
  }

  return true;
}
// 上传data
const setdata = ({ file: UploadFileInfo }) => {
  return {
    name: UploadFileInfo.name,
    guid: new Date().getTime(),
    chunk: "0",
    chunks: "1",
    storeId: id.value,
  };
};
// 上传header
function getUploadHeaders(options) {
  // options 由 n-upload 传入，包含当前文件信息
  let fileName = options?.file?.name;
  let timestamp = fileTimestampMap.get(fileName);
  if (!timestamp) {
    timestamp = Date.now().toString() + Math.floor(Math.random() * 10000);
    if (fileName) fileTimestampMap.set(fileName, timestamp);
  }
  const headers = {
    Authorization: `Bearer ${getToken()}`,
  };
  headers[ConfigEnum.TIMESTAMP] = timestamp;
  headers[ConfigEnum.Sign] = signMd5Utils.getSign(
    "/emind/knowledge_base/upload",
    {
      [ConfigEnum.TIMESTAMP]: timestamp,
    }
  );
  headers[ConfigEnum.DEVICEID] = device;
  headers[ConfigEnum.VERSION] = "v3";
  return headers;
}
const { sm4Decrypt, sm4Encrypt } = useCrypto();

function handleFinish(res) {
  if (JSON.parse(res.event.target.response).code != "0") {
    message.warning(JSON.parse(res.event.target.response).message);
    return;
  }
  console.log(res);

  let fSize, fileName, fileType, md5Hex, url, filePathLocal;
  if (encryptRequestResponse == "true") {
    ({ fSize, fileName, fileType, md5Hex, url, filePathLocal } = JSON.parse(
      sm4Decrypt(JSON.parse(res.event.target.response).data)
    ).result);
  } else {
    ({ fSize, fileName, fileType, md5Hex, url, filePathLocal } = JSON.parse(
      res.event.target.response
    ).data.result);
  }
  filedata.value.push({
    fileName,
    fileType,
    fSize,
    fileDesc: "",
    progress: "上传完成",
    md5Hex,
    filePath: url,
    filePathLocal,
  });
}
var filecolumns = [
  {
    title: "文件名称",
    key: "fileName",
    align: "center",
    render(row, index) {
      return h(NInput, {
        value: row.fileName,
        onUpdateValue(v) {
          filedata.value[index].fileName = v;
        },
      });
    },
  },
  {
    title: "文件类型",
    key: "fileType",
    align: "center",
  },
  {
    title: "文件大小",
    key: "fSize",
    align: "center",
  },
  {
    title: "文件描述",
    key: "fileDesc",
    align: "center",
    render(row, index) {
      return h(NInput, {
        value: row.fileDesc,
        onUpdateValue(v) {
          filedata.value[index].fileDesc = v;
        },
      });
    },
  },
  {
    title: "上传进度",
    key: "progress",
    align: "center",
  },
  {
    title: "操作",
    key: "actions",
    align: "center",
    render(row) {
      return [
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            class: "btn delbtn",
            size: "small",
            onClick: () => delbtnfun(row),
          },
          { default: () => "删除" }
        ),
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            class: "btn previewbtn",
            size: "small",
            onClick: () => previewbtnfun(row),
          },
          { default: () => "浏览" }
        ),
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            class: "btn downloadbtn",
            size: "small",
            onClick: () => downloadbtnfun(row),
          },
          { default: () => "下载" }
        ),
      ];
    },
  },
];
var filedata = ref([]);
function delbtnfun(row) {
  filedata.value.splice(filedata.value.indexOf(row), 1);
}
function previewbtnfun(row) {
  console.log(row);
  const previewPath = `/#/previewPage?fileName=${encodeURIComponent(
    row.fileName
  )}&filePath=${encodeURIComponent(row.filePath)}&fileType=${encodeURIComponent(
    row.fileType
  )}`;
  const url = `${window.location.origin}/zhijiao2?zjdx2m=${encodeURIComponent(
    previewPath
  )}`;
  // 在新标签页打开生成的 URL
  window.open(url, "_blank");
}
function downloadbtnfun(row) {
  loading.value = true;
  downloadfileApi({ filePath: row.filePath })
    .then((res) => {
      // 从响应中获取文件名
      let filename = row.fileName; // 默认使用行数据中的文件名

      // 如果响应头中有Content-Disposition，尝试从中提取文件名
      const contentDisposition = res.headers?.["content-disposition"] || "";
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
        if (filenameMatch && filenameMatch[1]) filename = filenameMatch[1];
      }

      // 创建Blob对象
      const blob = new Blob([res.data], { type: "application/octet-stream" });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;

      // 添加到DOM，触发点击，然后移除
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 释放URL对象
      window.URL.revokeObjectURL(url);

      message.success("文件下载成功");
    })
    .catch((error) => {
      message.error(`文件下载失败：${error.message}`);
    })
    .finally(() => {
      loading.value = false;
    });
}
function jumpPage(url) {
  if (url) {
    router.push(url);
  } else {
    router.go(-1);
  }
}
function send() {
  if (filedata.value.length == 0) {
    message.warning("请先上传文件");
    return;
  }
  loading.value = true;
  let data = filedata.value.map((item) => {
    return {
      storeId: id.value,
      fileName: item.fileName,
      fileType: item.fileType,
      fSize: Number(item.fSize) * 1024 + " KB",
      fileSize: Number(item.fSize) * 1024 * 1024,
      size: Number(item.fSize) * 1024 * 1024,
      fileDesc: item.fileDesc,
      progress: item.progress,
      md5Hex: item.md5Hex,
      filePath: item.filePath,
      filePathLocal: item.filePathLocal,
    };
  });
  savefilesApi(data)
    .then((res) => {
      console.log(res);
      if (res.code == "0") {
        jumpPage("/knowledgeFactoryPage");
      } else {
        message.error(res.message);
      }
    })
    .catch((err) => {
      message.error(err.message);
    })
    .finally(() => {
      loading.value = false;
    });
}
onMounted(() => {
  const params = route.query;
  id.value = params.id;
});
</script>
  
  <style scoped lang="less">
.app {
  background: url("@/assets/topbg.png") no-repeat;
  background-size: 90% 220px;
  background-position-x: 5%;
}
.app {
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 30px;
}
.red {
  color: #ff5f5f;
  margin-right: 4px;
}
.headers {
  height: 40px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;

  .left {
    color: #323233;
    font-size: 20px;
    font-weight: 500;
    line-height: 0;
    letter-spacing: 0;
    line-height: 40px;
    display: flex;
    align-items: center;
    .gohome {
      width: 40px;
      height: 40px;
      background: #fafbff;
      border: 1px solid #e9ecf3;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 11px;
      img {
        width: 20px;
        height: 20px;
      }
    }
    span {
      color: #909399;
      margin-left: 8px;
    }
  }
  .headbtnrow {
    button {
      width: 80px;
      height: 40px;
      border-radius: 10px;
      margin-left: 16px;
    }
  }
}
.setconbg {
  padding-top: 90px;
  padding-left: 24px;
  padding-right: 24px;
  background: url("@/assets/conbg.png") no-repeat;
  background-size: 100% 209px;
}
.rowtit {
  height: 21px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 15px;
  color: #323233;
  letter-spacing: 0;
  display: flex;
  align-items: center;
  margin-bottom: 9px;
}
.inputset-38 {
  height: 38px;
  /deep/ .n-input__input-el {
    height: 38px;
  }
}
.inputset-120 {
  height: 120px;
  /deep/ .n-input__input-el {
    height: 120px;
  }
}
.uploadbox {
  height: 247px;
  background-image: linear-gradient(90deg, #e7f9ff 0%, #f5f5ff 100%);
  border: 1px solid #125eff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  padding-top: 70px;
  img {
    width: 83px;
    height: 83px;
  }
  .uploadcon {
    width: 456px;
    text-align: left;
    .uptitle {
      height: 25px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      font-size: 18px;
      color: #125eff;
      letter-spacing: 0;
      line-height: 25px;
      margin-bottom: 2px;
    }
    .upcon {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #464646;
      letter-spacing: 0;
      line-height: 20px;
    }
  }
}
.n-upload-dragger {
  padding: 0;
}
.n-divider:not(.n-divider--vertical) {
  margin-top: 12px;
  margin-bottom: 8.5px;
}
/deep/.btn {
  height: 20px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #125eff;
  text-align: center;
  // margin-left: 10px;
  // margin-right: 10px;
  background-color: #fff;
}
/deep/.delbtn {
  color: #ff5f5f;
}
.gohome:hover {
  cursor: pointer;
}
</style>
  