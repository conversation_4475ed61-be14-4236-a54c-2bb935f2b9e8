{"id": "1752222023619-8sktsgs28", "name": "数据分类标注", "description": "基于Dify工作流转换的情感分析流程，支持单情感和多情感分析", "nodes": [{"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABWNJREFUWEfFWGtsFFUU/s6dWWlCVRBs0R+GikbBB2VnplvjIyCiqU9iQtGYCCaISlQMKon8MEiiRvERDEElPDQYUEsMxkfDw9gEie7uTLe0KYjRWIkRixAEH3G7e++xp8zCsn1s25T2JpPszJ57z3fP+c7jXsIARiwWOy+TyUy1LGuy1nqGUuoyABXMXCrLENHfAH42xvxoWdbXWuv9kUhkbzweP9FfNdRfQc/zapn5fmaOASgHoIrMNQDaiShORFuSyeTH/dHVJ6CamppR7e3tdyilFjHzzIIFjwE4BOAogP/C/0oAjANwEYCx+fJE9JUxZk15efkX9fX16d7A9QpI3JPNZp8EsKRg8WYAO4hInt8EUCaT6QIUiUS6ADHzxcx8KwB5rs1TLpt4o6SkZNWePXv+6glUj4AqKysnWpb1TrigyHQA2EVEa33f/7Q/ps/JuK57DzMvBHALgHMAsGxIa/1oU1NTW+Fa3QAJGNu232Lmu0Jhccnrxph1qVTqj4GAyclOmzbtQqXUAgBPhy6VAPhMPFAI6gxAoZuEfLeFOzlg2/ZD8Xj8u8EAKZwTi8Wqs9nsRgBXSFAC2G7bdm1+FJ4CJAQ+fPjws53RsyIU/p6IHvN9v2EowOS5cDozvw3gynDTz5eVla3MEf0UoGg0ei8RrQsJfNS27TuHyjK9WOrz0H3HmHlBY2PjJ125LA/5rjC0hcDLgyB4eSgtU7iW4zjPiR4huqQE3/eF9CcBSdIzxnwUTvrSGDO/NwI7jjOeiGQhj5k/sCxrcyKREOIPaIREfw/A7TJRKTVXkicJkbXW7zPz7C6ERLP7Cu1oNHojEQnxJwCQBFdPRKsGw7UwJWwL9W6zLGsehQrEOpJdm4MgmNrXVj3Pm2mMEUAX5MlJbtlgWdb6RCIhESnv/RqO4+wNk+chZp5Lrus+wsxrxGpEtNL3/aUDAJQFYIfyUrt+AbC6o6NjY0tLi2TlosN13VeZWaLbENEisdAWIroPwDEimuv7/s4BAJLw3Q9gPoBo3rw2IlqmlNpRjF+u685iZvHQWGb+kDzP840xDoB9SqnaZDLZ2l9AzLx60qRJTx08ePBSrfUSZn6gM6ufG86XWrXbGLMslUpJ/evRjZ7nXRVSYIpSKhCXHWFmqdC7Q0C/DwRQY2PjEzl5x3Ek2b0EYAaAMeH3fwHUaa2X91S7PM+bEAKSYDlKjuNIpR4FYKfWurapqenPwQKSeZWVlWMikcjNxhgBJiUiN9ZnMpnFzc3N/+SvL/KWZUmQzJKoHXFAc+bMsdra2l4xxiwmotSIu0ysJW5j5hs6O9IDZ5PU0kd/U4zUhfQY8bDvBsjzvIXGGMknQ5YYtdYbigVHb4Ez4qWjm4VGsrg6jnMJM99k23ZLIpGQmjb49oOZPSLapJTaUqw89OQex3HOJ6I3mXkegGQQBNWnAMkP13WHtUFzXXcpM78AQI5OiSAI5AB6umMczha2urp6ciaTkV69rPPcd4KIHvd9f9MZgIarya+qqrpOa70WwNUCgJlfTKfTK1pbW6V1Pm0heTnbx6Cwsm8GcM3J5pSSSqmafA4Oy0FRCAxADorPhK2v7H8HgEVBEPyUT/rhOEpLA/YwgLvDrkL0xy3LejCRSPzQLQ/1ljH7umwgou3SruRfNpSWllI6nZY2ZlznLYeQ9XoAchyvytNxnJnflVY5CIIjPekesuuYzhAWTnQBCqNnfIHCvUqp10aPHr21oaEhd33TDdPZvLDScmHFzL5SamtFRcXmuro6+dbn6DegXBTKlZ5SaooxZrplWZcz88TwSo+VUieMMb8S0T4i+tYYI710SxAEx4sByf3/P+DdY8jXxzHzAAAAAElFTkSuQmCC", "description": "情感分析工作流的起始点", "status": "idle", "config": {"variables": [{"name": "input_text", "type": "string", "description": "待分析的文本内容", "required": true, "defaultValue": ""}, {"name": "Multisentiment", "type": "string", "description": "是否进行多情感分析", "required": true, "defaultValue": "False", "options": ["True", "False"]}, {"name": "Categories", "type": "string", "description": "情感分类类别", "required": false, "defaultValue": "正面,负面,中性"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "icon": "data:image/png;base64,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", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "llm-single-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 699.1642714592357, "y": 98.32854291847144, "z": 0}, "handleBounds": {"source": [{"id": "llm-single-sentiment-output", "type": "source", "nodeId": "llm-single-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-single-sentiment-input", "type": "target", "nodeId": "llm-single-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 699.1642714592357, "y": 98.32854291847144}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "执行单情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本的情感倾向，并给出一个主要的情感标签。\n\n文本：{{input_text}}\n\n请从以下类别中选择最符合的情感：{{Categories}}\n\n请直接回答情感标签，不需要解释。", "systemPrompt": "你是一个专业的情感分析助手，能够准确识别文本的情感倾向。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "llm-multi-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 700, "y": 300, "z": 0}, "handleBounds": {"source": [{"id": "llm-multi-sentiment-output", "type": "source", "nodeId": "llm-multi-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-multi-sentiment-input", "type": "target", "nodeId": "llm-multi-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 700, "y": 300}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "执行多情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本可能包含的多种情感，并给出每种情感的强度评分（0-1之间）。\n\n文本：{{input_text}}\n\n请从以下类别中分析：{{Categories}}\n\n请以JSON格式返回结果，例如：\n{\n  \"正面\": 0.8,\n  \"负面\": 0.2,\n  \"中性\": 0.1\n}", "systemPrompt": "你是一个专业的情感分析助手，能够识别文本中的多种情感并给出强度评分。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "end-single", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 986.6283433477715, "y": 120.0574849783427, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-single-input-0", "type": "target", "nodeId": "end-single", "position": "left", "x": -2.0000572278552684, "y": 25.50001553117635, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 986.6283433477715, "y": 120.0574849783427}, "data": {"label": "结束节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABVlJREFUWEfFWFtsVFUUXfvcGaeTiAqkD/2hjQ8EibRz7+1YxASDiIhE1LTolxoJIomaaDQxJqgkhChRIyZoCMbHD2ITAwEhPIxNtMWZOXc6kPAMhvIjtlKL4odt557t7HJbh+lMKaR27k+Tzj5nr7Mfa69zCFfxxePxGwYHB+daljXL9/37lVK3Aahj5utlGyL6G8AZY8xpy7J+8H3/eDgcPpxIJP4arxsar6Hrui3M/BQzxwFUA1BXWGsAdBNRgoi2pVKpb8bja0xAS5YsiXR3dy9VSq1h5oUFG/YBOAegF8A/wW8VAKYDuBnA1Hx7IvreGLO5urr6u7179/aXAlcSkKQnm82+BOCVgs2PENE+AAeI6FcBNDg4OAQoHA4PAWLmWwAsYubFAO7Ocy6H+KCiouKj9vb2i8VAFQVUX19fa1nWpwAelNIAMADgIBFt0VrvHE/oh20cx3mUmVcBeADAdQAYwH7f91dnMpmuwr1GARIwoVBoEzMvC4wlJe8bY7Z2dnb+fjVghm0bGhoqlVIrAbwapFQaYJdkoBDUZYCCNEnxSajlJCdDodCziUTi52sBUrgmHo/fk81mPwcwM4j8vlAo1JLfhSOApIB7enpey3XPusD4BBG9oLVumwgweSlcwMyfALgzOPTaqqqqjcOFPgIoFos9TkRbgwLuDYVCj0xUZEpEaneQvj5mXplOp78d4rI85AeD1pYCftvzvA0TGZnCvWzbfkP8SKELJWitpegvARLSM8ZsDxbtMcY8U6qAHcdpIKJlvu8vVkoJ1xQSpDHG9FmWtY+Zd2mtO4sdLCj0LwA8LL8rpVYIeZIUsu/7XzLz8iGERMtLtbZt2/MBfAZARsZ4mPo0gOc8z/upGKiAEnYEfndYlvU0xWKx+4hIoiPsesTzvLklFjcw89cA7gh+PyukSEQyIkY+ZhagwtYzgn+eIqInS0XKtu3DAXmeY+YV5DjO88y8WU5MRBu11q8XA+S67lpjzFtix8wHlVJvMnOvZVmXAfJ9X/aZboxZT0RSF0Yp9U4qlZLuHfU5jvMeM0t3GyJaIxHaJicA0EdEK7TWB4otjMVi7UQ0D8BZImrRWifHKnrHcRqZWThtBjN3pNPpe0sAkhEjGZoqGSDXdbUxxgZwTCnVkkqljpZYeCy3cBaAdM62xfO8X8YCZNv2rQAEUCx30ONa69klIn+XMUbsZhORlpSdZ2bJ+Y8BoN9KADrBzDNlkdglk8kzYwFqbGysE0fM7BDRSa21EOGoz3XdmgCQ1HIv2bYtkzoi09v3/ZZMJnNhMgHV19ffZFmWRGgRgP6yA2pubra6urrezXHXy0TUWfaUBcRck+vc+TlFerLsRV1YHmVv+1GAXNddZYwROVAWYiwWobKOjryZOMT4ZR2utm3LlWoPgAuRSKSpo6Ojp2zyo6mpaVp/f/8WInoimPbztdbt5RRocr1aD6CCiJJ1dXXzWltb/bJIWMdx5ohiCG7AfxLR6txoEWnzn4SdLJHf2NjY5Pu+dPWw7towZcqUdW1tbUOXzUm9BjmOM5OZWwHMuSROKRUOhx86dOjQH8PtPykXRdu2bwQgF0URf1WB8/2WZb2YTCZP5XPR/36Vdl13oTFmNYClAKKB885IJNLc0dExSlNN2GNDNBpVAwMDEaXUtGw2W0NEcaXUY6KH8iJwMSdvP2bmDz3PO19M5kzYcwwRidYWXTUNQE3wd8SnUspj5k2VlZXbr+k5phD9tT5Y5e5dSaXUztra2q+EZ8ZSmaO67ErGw096SqnZxpgFlmXdzsy1wZOezCJRm3KdOaqUajfGHItGo5lSb0HF/P0LhaJvxBYEIy0AAAAASUVORK5CYII=", "description": "输出单情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_result", "type": "string", "source": "llm-single-sentiment.output", "description": "单情感分析结果"}, {"name": "analysis_type", "type": "string", "value": "single", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, {"id": "end-multi", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 993.3141716738858, "y": 319.22175643757845, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-multi-input-0", "type": "target", "nodeId": "end-multi", "position": "left", "x": -2.0000572278552684, "y": 25.50004103560415, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 993.3141716738858, "y": 319.22175643757845}, "data": {"label": "结束节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABVlJREFUWEfFWFtsVFUUXfvcGaeTiAqkD/2hjQ8EibRz7+1YxASDiIhE1LTolxoJIomaaDQxJqgkhChRIyZoCMbHD2ITAwEhPIxNtMWZOXc6kPAMhvIjtlKL4odt557t7HJbh+lMKaR27k+Tzj5nr7Mfa69zCFfxxePxGwYHB+daljXL9/37lVK3Aahj5utlGyL6G8AZY8xpy7J+8H3/eDgcPpxIJP4arxsar6Hrui3M/BQzxwFUA1BXWGsAdBNRgoi2pVKpb8bja0xAS5YsiXR3dy9VSq1h5oUFG/YBOAegF8A/wW8VAKYDuBnA1Hx7IvreGLO5urr6u7179/aXAlcSkKQnm82+BOCVgs2PENE+AAeI6FcBNDg4OAQoHA4PAWLmWwAsYubFAO7Ocy6H+KCiouKj9vb2i8VAFQVUX19fa1nWpwAelNIAMADgIBFt0VrvHE/oh20cx3mUmVcBeADAdQAYwH7f91dnMpmuwr1GARIwoVBoEzMvC4wlJe8bY7Z2dnb+fjVghm0bGhoqlVIrAbwapFQaYJdkoBDUZYCCNEnxSajlJCdDodCziUTi52sBUrgmHo/fk81mPwcwM4j8vlAo1JLfhSOApIB7enpey3XPusD4BBG9oLVumwgweSlcwMyfALgzOPTaqqqqjcOFPgIoFos9TkRbgwLuDYVCj0xUZEpEaneQvj5mXplOp78d4rI85AeD1pYCftvzvA0TGZnCvWzbfkP8SKELJWitpegvARLSM8ZsDxbtMcY8U6qAHcdpIKJlvu8vVkoJ1xQSpDHG9FmWtY+Zd2mtO4sdLCj0LwA8LL8rpVYIeZIUsu/7XzLz8iGERMtLtbZt2/MBfAZARsZ4mPo0gOc8z/upGKiAEnYEfndYlvU0xWKx+4hIoiPsesTzvLklFjcw89cA7gh+PyukSEQyIkY+ZhagwtYzgn+eIqInS0XKtu3DAXmeY+YV5DjO88y8WU5MRBu11q8XA+S67lpjzFtix8wHlVJvMnOvZVmXAfJ9X/aZboxZT0RSF0Yp9U4qlZLuHfU5jvMeM0t3GyJaIxHaJicA0EdEK7TWB4otjMVi7UQ0D8BZImrRWifHKnrHcRqZWThtBjN3pNPpe0sAkhEjGZoqGSDXdbUxxgZwTCnVkkqljpZYeCy3cBaAdM62xfO8X8YCZNv2rQAEUCx30ONa69klIn+XMUbsZhORlpSdZ2bJ+Y8BoN9KADrBzDNlkdglk8kzYwFqbGysE0fM7BDRSa21EOGoz3XdmgCQ1HIv2bYtkzoi09v3/ZZMJnNhMgHV19ffZFmWRGgRgP6yA2pubra6urrezXHXy0TUWfaUBcRck+vc+TlFerLsRV1YHmVv+1GAXNddZYwROVAWYiwWobKOjryZOMT4ZR2utm3LlWoPgAuRSKSpo6Ojp2zyo6mpaVp/f/8WInoimPbztdbt5RRocr1aD6CCiJJ1dXXzWltb/bJIWMdx5ohiCG7AfxLR6txoEWnzn4SdLJHf2NjY5Pu+dPWw7towZcqUdW1tbUOXzUm9BjmOM5OZWwHMuSROKRUOhx86dOjQH8PtPykXRdu2bwQgF0URf1WB8/2WZb2YTCZP5XPR/36Vdl13oTFmNYClAKKB885IJNLc0dExSlNN2GNDNBpVAwMDEaXUtGw2W0NEcaXUY6KH8iJwMSdvP2bmDz3PO19M5kzYcwwRidYWXTUNQE3wd8SnUspj5k2VlZXbr+k5phD9tT5Y5e5dSaXUztra2q+EZ8ZSmaO67ErGw096SqnZxpgFlmXdzsy1wZOezCJRm3KdOaqUajfGHItGo5lSb0HF/P0LhaJvxBYEIy0AAAAASUVORK5CYII=", "description": "输出多情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_scores", "type": "object", "source": "llm-multi-sentiment.output", "description": "多情感分析评分结果"}, {"name": "analysis_type", "type": "string", "value": "multi", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}], "edges": [{"id": "1752222047366-pa1bdf8g9", "type": "default", "source": "start-node", "target": "condition-node", "sourceHandle": "start-node-output", "targetHandle": "condition-node-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABWNJREFUWEfFWGtsFFUU/s6dWWlCVRBs0R+GikbBB2VnplvjIyCiqU9iQtGYCCaISlQMKon8MEiiRvERDEElPDQYUEsMxkfDw9gEie7uTLe0KYjRWIkRixAEH3G7e++xp8zCsn1s25T2JpPszJ57z3fP+c7jXsIARiwWOy+TyUy1LGuy1nqGUuoyABXMXCrLENHfAH42xvxoWdbXWuv9kUhkbzweP9FfNdRfQc/zapn5fmaOASgHoIrMNQDaiShORFuSyeTH/dHVJ6CamppR7e3tdyilFjHzzIIFjwE4BOAogP/C/0oAjANwEYCx+fJE9JUxZk15efkX9fX16d7A9QpI3JPNZp8EsKRg8WYAO4hInt8EUCaT6QIUiUS6ADHzxcx8KwB5rs1TLpt4o6SkZNWePXv+6glUj4AqKysnWpb1TrigyHQA2EVEa33f/7Q/ps/JuK57DzMvBHALgHMAsGxIa/1oU1NTW+Fa3QAJGNu232Lmu0Jhccnrxph1qVTqj4GAyclOmzbtQqXUAgBPhy6VAPhMPFAI6gxAoZuEfLeFOzlg2/ZD8Xj8u8EAKZwTi8Wqs9nsRgBXSFAC2G7bdm1+FJ4CJAQ+fPjws53RsyIU/p6IHvN9v2EowOS5cDozvw3gynDTz5eVla3MEf0UoGg0ei8RrQsJfNS27TuHyjK9WOrz0H3HmHlBY2PjJ125LA/5rjC0hcDLgyB4eSgtU7iW4zjPiR4huqQE3/eF9CcBSdIzxnwUTvrSGDO/NwI7jjOeiGQhj5k/sCxrcyKREOIPaIREfw/A7TJRKTVXkicJkbXW7zPz7C6ERLP7Cu1oNHojEQnxJwCQBFdPRKsGw7UwJWwL9W6zLGsehQrEOpJdm4MgmNrXVj3Pm2mMEUAX5MlJbtlgWdb6RCIhESnv/RqO4+wNk+chZp5Lrus+wsxrxGpEtNL3/aUDAJQFYIfyUrt+AbC6o6NjY0tLi2TlosN13VeZWaLbENEisdAWIroPwDEimuv7/s4BAJLw3Q9gPoBo3rw2IlqmlNpRjF+u685iZvHQWGb+kDzP840xDoB9SqnaZDLZ2l9AzLx60qRJTx08ePBSrfUSZn6gM6ufG86XWrXbGLMslUpJ/evRjZ7nXRVSYIpSKhCXHWFmqdC7Q0C/DwRQY2PjEzl5x3Ek2b0EYAaAMeH3fwHUaa2X91S7PM+bEAKSYDlKjuNIpR4FYKfWurapqenPwQKSeZWVlWMikcjNxhgBJiUiN9ZnMpnFzc3N/+SvL/KWZUmQzJKoHXFAc+bMsdra2l4xxiwmotSIu0ysJW5j5hs6O9IDZ5PU0kd/U4zUhfQY8bDvBsjzvIXGGMknQ5YYtdYbigVHb4Ez4qWjm4VGsrg6jnMJM99k23ZLIpGQmjb49oOZPSLapJTaUqw89OQex3HOJ6I3mXkegGQQBNWnAMkP13WHtUFzXXcpM78AQI5OiSAI5AB6umMczha2urp6ciaTkV69rPPcd4KIHvd9f9MZgIarya+qqrpOa70WwNUCgJlfTKfTK1pbW6V1Pm0heTnbx6Cwsm8GcM3J5pSSSqmafA4Oy0FRCAxADorPhK2v7H8HgEVBEPyUT/rhOEpLA/YwgLvDrkL0xy3LejCRSPzQLQ/1ljH7umwgou3SruRfNpSWllI6nZY2ZlznLYeQ9XoAchyvytNxnJnflVY5CIIjPekesuuYzhAWTnQBCqNnfIHCvUqp10aPHr21oaEhd33TDdPZvLDScmHFzL5SamtFRcXmuro6+dbn6DegXBTKlZ5SaooxZrplWZcz88TwSo+VUieMMb8S0T4i+tYYI710SxAEx4sByf3/P+DdY8jXxzHzAAAAAElFTkSuQmCC", "description": "情感分析工作流的起始点", "status": "idle", "config": {"variables": [{"name": "input_text", "type": "string", "description": "待分析的文本内容", "required": true, "defaultValue": ""}, {"name": "Multisentiment", "type": "string", "description": "是否进行多情感分析", "required": true, "defaultValue": "False", "options": ["True", "False"]}, {"name": "Categories", "type": "string", "description": "情感分类类别", "required": false, "defaultValue": "正面,负面,中性"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "targetNode": {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "icon": "data:image/png;base64,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", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 230.00007330584654, "sourceY": 229.49999002674855, "targetX": 396.50721797414946, "targetY": 236.07974904580254}, {"id": "1752222050385-38gwo2b1p", "type": "default", "source": "condition-node", "target": "llm-single-sentiment", "sourceHandle": "condition-node-true", "targetHandle": "llm-single-sentiment-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "icon": "data:image/png;base64,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", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "llm-single-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 699.1642714592357, "y": 98.32854291847144, "z": 0}, "handleBounds": {"source": [{"id": "llm-single-sentiment-output", "type": "source", "nodeId": "llm-single-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-single-sentiment-input", "type": "target", "nodeId": "llm-single-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 699.1642714592357, "y": 98.32854291847144}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "执行单情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本的情感倾向，并给出一个主要的情感标签。\n\n文本：{{input_text}}\n\n请从以下类别中选择最符合的情感：{{Categories}}\n\n请直接回答情感标签，不需要解释。", "systemPrompt": "你是一个专业的情感分析助手，能够准确识别文本的情感倾向。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 567.5072850283507, "sourceY": 250.37663550443187, "targetX": 693.164201793381, "targetY": 155.2192172134295}, {"id": "1752222053068-9xpq23p2d", "type": "default", "source": "condition-node", "target": "llm-multi-sentiment", "sourceHandle": "condition-node-false", "targetHandle": "llm-multi-sentiment-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "condition-node", "type": "condition", "dimensions": {"width": 164, "height": 147}, "computedPosition": {"x": 402.50718562229287, "y": 162.39221566560744, "z": 0}, "handleBounds": {"source": [{"id": "condition-node-true", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 82.98441983882442, "width": 10, "height": 10}, {"id": "condition-node-false", "type": "source", "nodeId": "condition-node", "position": "right", "x": 155.00009940605779, "y": 114.57818279915445, "width": 10, "height": 10}], "target": [{"id": "condition-node-input", "type": "target", "nodeId": "condition-node", "position": "left", "x": -5.999967648143428, "y": 69.68753338019509, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 402.50718562229287, "y": 162.39221566560744}, "data": {"label": "条件判断", "icon": "data:image/png;base64,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", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "llm-multi-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 700, "y": 300, "z": 0}, "handleBounds": {"source": [{"id": "llm-multi-sentiment-output", "type": "source", "nodeId": "llm-multi-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-multi-sentiment-input", "type": "target", "nodeId": "llm-multi-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 700, "y": 300}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "执行多情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本可能包含的多种情感，并给出每种情感的强度评分（0-1之间）。\n\n文本：{{input_text}}\n\n请从以下类别中分析：{{Categories}}\n\n请以JSON格式返回结果，例如：\n{\n  \"正面\": 0.8,\n  \"负面\": 0.2,\n  \"中性\": 0.1\n}", "systemPrompt": "你是一个专业的情感分析助手，能够识别文本中的多种情感并给出强度评分。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 567.5072850283507, "sourceY": 281.9703984647619, "targetX": 693.9999303341453, "targetY": 356.89067429495805}, {"id": "1752222062362-73xfl9iby", "type": "default", "source": "llm-single-sentiment", "target": "end-single", "sourceHandle": "llm-single-sentiment-output", "targetHandle": "end-single-input-0", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "llm-single-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 699.1642714592357, "y": 98.32854291847144, "z": 0}, "handleBounds": {"source": [{"id": "llm-single-sentiment-output", "type": "source", "nodeId": "llm-single-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-single-sentiment-input", "type": "target", "nodeId": "llm-single-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 699.1642714592357, "y": 98.32854291847144}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "执行单情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本的情感倾向，并给出一个主要的情感标签。\n\n文本：{{input_text}}\n\n请从以下类别中选择最符合的情感：{{Categories}}\n\n请直接回答情感标签，不需要解释。", "systemPrompt": "你是一个专业的情感分析助手，能够准确识别文本的情感倾向。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "end-single", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 986.6283433477715, "y": 120.0574849783427, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-single-input-0", "type": "target", "nodeId": "end-single", "position": "left", "x": -2.0000572278552684, "y": 25.50001553117635, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 986.6283433477715, "y": 120.0574849783427}, "data": {"label": "结束节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABVlJREFUWEfFWFtsVFUUXfvcGaeTiAqkD/2hjQ8EibRz7+1YxASDiIhE1LTolxoJIomaaDQxJqgkhChRIyZoCMbHD2ITAwEhPIxNtMWZOXc6kPAMhvIjtlKL4odt557t7HJbh+lMKaR27k+Tzj5nr7Mfa69zCFfxxePxGwYHB+daljXL9/37lVK3Aahj5utlGyL6G8AZY8xpy7J+8H3/eDgcPpxIJP4arxsar6Hrui3M/BQzxwFUA1BXWGsAdBNRgoi2pVKpb8bja0xAS5YsiXR3dy9VSq1h5oUFG/YBOAegF8A/wW8VAKYDuBnA1Hx7IvreGLO5urr6u7179/aXAlcSkKQnm82+BOCVgs2PENE+AAeI6FcBNDg4OAQoHA4PAWLmWwAsYubFAO7Ocy6H+KCiouKj9vb2i8VAFQVUX19fa1nWpwAelNIAMADgIBFt0VrvHE/oh20cx3mUmVcBeADAdQAYwH7f91dnMpmuwr1GARIwoVBoEzMvC4wlJe8bY7Z2dnb+fjVghm0bGhoqlVIrAbwapFQaYJdkoBDUZYCCNEnxSajlJCdDodCziUTi52sBUrgmHo/fk81mPwcwM4j8vlAo1JLfhSOApIB7enpey3XPusD4BBG9oLVumwgweSlcwMyfALgzOPTaqqqqjcOFPgIoFos9TkRbgwLuDYVCj0xUZEpEaneQvj5mXplOp78d4rI85AeD1pYCftvzvA0TGZnCvWzbfkP8SKELJWitpegvARLSM8ZsDxbtMcY8U6qAHcdpIKJlvu8vVkoJ1xQSpDHG9FmWtY+Zd2mtO4sdLCj0LwA8LL8rpVYIeZIUsu/7XzLz8iGERMtLtbZt2/MBfAZARsZ4mPo0gOc8z/upGKiAEnYEfndYlvU0xWKx+4hIoiPsesTzvLklFjcw89cA7gh+PyukSEQyIkY+ZhagwtYzgn+eIqInS0XKtu3DAXmeY+YV5DjO88y8WU5MRBu11q8XA+S67lpjzFtix8wHlVJvMnOvZVmXAfJ9X/aZboxZT0RSF0Yp9U4qlZLuHfU5jvMeM0t3GyJaIxHaJicA0EdEK7TWB4otjMVi7UQ0D8BZImrRWifHKnrHcRqZWThtBjN3pNPpe0sAkhEjGZoqGSDXdbUxxgZwTCnVkkqljpZYeCy3cBaAdM62xfO8X8YCZNv2rQAEUCx30ONa69klIn+XMUbsZhORlpSdZ2bJ+Y8BoN9KADrBzDNlkdglk8kzYwFqbGysE0fM7BDRSa21EOGoz3XdmgCQ1HIv2bYtkzoi09v3/ZZMJnNhMgHV19ffZFmWRGgRgP6yA2pubra6urrezXHXy0TUWfaUBcRck+vc+TlFerLsRV1YHmVv+1GAXNddZYwROVAWYiwWobKOjryZOMT4ZR2utm3LlWoPgAuRSKSpo6Ojp2zyo6mpaVp/f/8WInoimPbztdbt5RRocr1aD6CCiJJ1dXXzWltb/bJIWMdx5ohiCG7AfxLR6txoEWnzn4SdLJHf2NjY5Pu+dPWw7towZcqUdW1tbUOXzUm9BjmOM5OZWwHMuSROKRUOhx86dOjQH8PtPykXRdu2bwQgF0URf1WB8/2WZb2YTCZP5XPR/36Vdl13oTFmNYClAKKB885IJNLc0dExSlNN2GNDNBpVAwMDEaXUtGw2W0NEcaXUY6KH8iJwMSdvP2bmDz3PO19M5kzYcwwRidYWXTUNQE3wd8SnUspj5k2VlZXbr+k5phD9tT5Y5e5dSaXUztra2q+EZ8ZSmaO67ErGw096SqnZxpgFlmXdzsy1wZOezCJRm3KdOaqUajfGHItGo5lSb0HF/P0LhaJvxBYEIy0AAAAASUVORK5CYII=", "description": "输出单情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_result", "type": "string", "source": "llm-single-sentiment.output", "description": "单情感分析结果"}, {"name": "analysis_type", "type": "string", "value": "single", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "sourceX": 869.164316118509, "sourceY": 155.2192172134295, "targetX": 985.4640146606805, "targetY": 157.91478591716185}, {"id": "1752222065095-8pqqjnfda", "type": "default", "source": "llm-multi-sentiment", "target": "end-multi", "sourceHandle": "llm-multi-sentiment-output", "targetHandle": "end-multi-input-0", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "llm-multi-sentiment", "type": "llm", "dimensions": {"width": 164, "height": 114}, "computedPosition": {"x": 700, "y": 300, "z": 0}, "handleBounds": {"source": [{"id": "llm-multi-sentiment-output", "type": "source", "nodeId": "llm-multi-sentiment", "position": "right", "x": 162.00004465927324, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-multi-sentiment-input", "type": "target", "nodeId": "llm-multi-sentiment", "position": "left", "x": -6.000069665854617, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 700, "y": 300}, "data": {"label": "LLM节点", "icon": "data:image/png;base64,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", "description": "执行多情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本可能包含的多种情感，并给出每种情感的强度评分（0-1之间）。\n\n文本：{{input_text}}\n\n请从以下类别中分析：{{Categories}}\n\n请以JSON格式返回结果，例如：\n{\n  \"正面\": 0.8,\n  \"负面\": 0.2,\n  \"中性\": 0.1\n}", "systemPrompt": "你是一个专业的情感分析助手，能够识别文本中的多种情感并给出强度评分。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "end-multi", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 993.3141716738858, "y": 319.22175643757845, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "end-multi-input-0", "type": "target", "nodeId": "end-multi", "position": "left", "x": -2.0000572278552684, "y": 25.50004103560415, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 993.3141716738858, "y": 319.22175643757845}, "data": {"label": "结束节点", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAABVlJREFUWEfFWFtsVFUUXfvcGaeTiAqkD/2hjQ8EibRz7+1YxASDiIhE1LTolxoJIomaaDQxJqgkhChRIyZoCMbHD2ITAwEhPIxNtMWZOXc6kPAMhvIjtlKL4odt557t7HJbh+lMKaR27k+Tzj5nr7Mfa69zCFfxxePxGwYHB+daljXL9/37lVK3Aahj5utlGyL6G8AZY8xpy7J+8H3/eDgcPpxIJP4arxsar6Hrui3M/BQzxwFUA1BXWGsAdBNRgoi2pVKpb8bja0xAS5YsiXR3dy9VSq1h5oUFG/YBOAegF8A/wW8VAKYDuBnA1Hx7IvreGLO5urr6u7179/aXAlcSkKQnm82+BOCVgs2PENE+AAeI6FcBNDg4OAQoHA4PAWLmWwAsYubFAO7Ocy6H+KCiouKj9vb2i8VAFQVUX19fa1nWpwAelNIAMADgIBFt0VrvHE/oh20cx3mUmVcBeADAdQAYwH7f91dnMpmuwr1GARIwoVBoEzMvC4wlJe8bY7Z2dnb+fjVghm0bGhoqlVIrAbwapFQaYJdkoBDUZYCCNEnxSajlJCdDodCziUTi52sBUrgmHo/fk81mPwcwM4j8vlAo1JLfhSOApIB7enpey3XPusD4BBG9oLVumwgweSlcwMyfALgzOPTaqqqqjcOFPgIoFos9TkRbgwLuDYVCj0xUZEpEaneQvj5mXplOp78d4rI85AeD1pYCftvzvA0TGZnCvWzbfkP8SKELJWitpegvARLSM8ZsDxbtMcY8U6qAHcdpIKJlvu8vVkoJ1xQSpDHG9FmWtY+Zd2mtO4sdLCj0LwA8LL8rpVYIeZIUsu/7XzLz8iGERMtLtbZt2/MBfAZARsZ4mPo0gOc8z/upGKiAEnYEfndYlvU0xWKx+4hIoiPsesTzvLklFjcw89cA7gh+PyukSEQyIkY+ZhagwtYzgn+eIqInS0XKtu3DAXmeY+YV5DjO88y8WU5MRBu11q8XA+S67lpjzFtix8wHlVJvMnOvZVmXAfJ9X/aZboxZT0RSF0Yp9U4qlZLuHfU5jvMeM0t3GyJaIxHaJicA0EdEK7TWB4otjMVi7UQ0D8BZImrRWifHKnrHcRqZWThtBjN3pNPpe0sAkhEjGZoqGSDXdbUxxgZwTCnVkkqljpZYeCy3cBaAdM62xfO8X8YCZNv2rQAEUCx30ONa69klIn+XMUbsZhORlpSdZ2bJ+Y8BoN9KADrBzDNlkdglk8kzYwFqbGysE0fM7BDRSa21EOGoz3XdmgCQ1HIv2bYtkzoi09v3/ZZMJnNhMgHV19ffZFmWRGgRgP6yA2pubra6urrezXHXy0TUWfaUBcRck+vc+TlFerLsRV1YHmVv+1GAXNddZYwROVAWYiwWobKOjryZOMT4ZR2utm3LlWoPgAuRSKSpo6Ojp2zyo6mpaVp/f/8WInoimPbztdbt5RRocr1aD6CCiJJ1dXXzWltb/bJIWMdx5ohiCG7AfxLR6txoEWnzn4SdLJHf2NjY5Pu+dPWw7towZcqUdW1tbUOXzUm9BjmOM5OZWwHMuSROKRUOhx86dOjQH8PtPykXRdu2bwQgF0URf1WB8/2WZb2YTCZP5XPR/36Vdl13oTFmNYClAKKB885IJNLc0dExSlNN2GNDNBpVAwMDEaXUtGw2W0NEcaXUY6KH8iJwMSdvP2bmDz3PO19M5kzYcwwRidYWXTUNQE3wd8SnUspj5k2VlZXbr+k5phD9tT5Y5e5dSaXUztra2q+EZ8ZSmaO67ErGw096SqnZxpgFlmXdzsy1wZOezCJRm3KdOaqUajfGHItGo5lSb0HF/P0LhaJvxBYEIy0AAAAASUVORK5CYII=", "description": "输出多情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_scores", "type": "object", "source": "llm-multi-sentiment.output", "description": "多情感分析评分结果"}, {"name": "analysis_type", "type": "string", "value": "multi", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "sourceX": 870.0000446592733, "sourceY": 356.89067429495805, "targetX": 1000.5071283944376, "targetY": 358.75053996235397}], "createdAt": "2025-07-11T08:20:23.619Z", "updatedAt": "2025-07-11T08:24:08.807Z", "metadata": {"originalSource": "dify-workflow", "version": "1.0.0", "convertedAt": "2024-01-15T10:00:00.000Z", "description": "从Dify工作流YAML转换而来的情感分析流程", "tags": ["情感分析", "条件分支", "LLM", "多路输出"]}}