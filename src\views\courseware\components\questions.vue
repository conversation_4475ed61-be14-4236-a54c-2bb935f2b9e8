<script lang='ts' setup>
import {onMounted, ref} from "vue";
import {NButton, NInput, NSpin} from "naive-ui";
import Message from "@/views/tankChat/components/Message/index.vue";
import {useBasicLayout} from "@/hooks/useBasicLayout";
import {t} from "@/locales";
import {useScroll} from "@/views/tankChat/hooks/useScroll";
import {
	addTextbookAnnotations,
	agentById,
	answersStop,
	fetchChatAPIProcess,
	generateSummarizeProblem,
	getGenerateProblem,
} from "@/api/courseware";
import {useToolsStore} from "@/store";
import icon1 from "@/assets/toolboxPage/kj-logo.png";
import {marked} from "marked";

defineOptions({
	name: "Chat",
});

const props = defineProps<Props>();

const emit = defineEmits(["textConverse", "textLengthTap", "noteAdded"]);

const {scrollRef, scrollToBottom, scrollToBottomIfAtBottom} = useScroll();

interface Props {
	submitFlag?: boolean;
	show?: boolean;
	dialogue: any;
	main?: any;
}

const ToolsStore = useToolsStore();
const {isMobile} = useBasicLayout();
let controller: any = new AbortController();
const loading = ref(false);
// 总结问题生成loading状态
const summaryLoading = ref(false);
// 预设问题生成loading状态
const questionLoading = ref(false);

const prompt = ref();

const uuid: any = ref(null);

const dataSources: any = ref([]);
const addChat = (uuid: any, chat: any) => {
	dataSources.value.push({
		uuid,
		...chat,
	});
};
const updateChat = (uuid: any, index: any, chat: any) => {
	if (!uuid || uuid === 0) {
		if (dataSources.value.length) dataSources.value[0].data[index] = chat;
		return;
	}

	// const chatIndex = dataSources.value.findIndex(item => item.uuid === uuid)
	// // console.log(uuid, index, chatIndex)
	// if (chatIndex !== -1)
	dataSources.value[index] = chat;
};

const updateChatSome = (uuid: any, index: any, chat: any) => {
	dataSources.value[index] = {
		...dataSources.value[index],
		...chat,
	};
};

const onConversation = async (msg?: string) => {
	// 做对话前处理  后父组件调用 textConversation
	// emit("textConverse", {message: prompt.value});
	// const data = await addConversation({agentId: '1950834085600997378', title: prompt.value, category: '1'})
	if (inputFlag.value) {
		await textConversation({
			prompt: "coursewareAssistantByModelPrompt",
			problem: problem.value,
			question: msg ? msg : prompt.value,
			conversationId: props.dialogue.id,
			category: "1",
			agentId: "1950834085600997378",
		});
	} else {
		await textConversation({
			question: msg ? msg : prompt.value,
			conversationId: props.dialogue.id,
			category: "0",
			agentId: "1950834085600997378",
		});
	}
};

// 对话
const textConversation = async (obj: any, annotation?: any) => {
	console.log(obj);
	uuid.value = obj?.conversationId;
	loading.value = true;

	const message = obj?.question;
	addChat(uuid, {
		dateTime: new Date().toLocaleString(),
		text: message,
		answerList: [],
		endstatus: 1,
		inversion: true,
		error: false,
		conversationOptions: null,
		problem: obj.prompt === "coursewareAssistantByModelPrompt",
		requestOptions: {prompt: message, options: null},
	});
	console.log(obj.prompt === "coursewareAssistantByModelPrompt");
	prompt.value = "";
	controller = new AbortController();
	addChat(uuid.value, {
		dateTime: new Date().toLocaleString(),
		text: t("chat.thinking"),
		loading: true,
		answerList: [],
		endstatus: 1,
		inversion: false,
		error: false,
		conversationOptions: null,
	});
	await scrollToBottom();

	try {
		let lastText = "";
		const fetchChatAPIOnce = async () => {
			await fetchChatAPIProcess<Chat.ConversationResponse>({
				signal: controller.signal,
				...obj,
				onDownloadProgress: ({event}) => {
					const xhr = event.target;
					const {responseText} = xhr;
					// if (inputFlag.netFlag) {
					// 深度思考
					// 按行分割响应文本
					const lines = responseText
						.split("\n")
						.filter((line) => line.trim() !== "");

					// 重置文本,避免重复累加
					lastText = "";
					// 处理每一行数据
					for (const line of lines) {
						const trimmedLine = line.replace(/^data: /, "").trim();
						// info.setloading(false)
						try {
							// const data = JSON.parse(trimmedLine)
							const data = JSON.parse(trimmedLine?.substring(5));
							console.log(data);
							stopId.value.id = data.id;
							// 停止回答用
							// currectemitterId.value = data.emitterId

							// 直接使用当前响应文本,不进行累加
							const deltaContent = data.choices[0].message.content || "";

							lastText += deltaContent;

							updateChat(uuid.value, dataSources.value.length - 1, {
								dateTime: new Date().toLocaleString(),
								text: lastText, // 使用完整的lastText,不再和之前的文本拼接
								// answerList: data.answerList,
								inversion: false,
								error: false,
								loading: true,
								conversationContentId: data.conversationContentId,
								conversationOptions: {
									conversationId: data.conversationContentId || "",
									parentMessageId: data.id || "",
								},
							});

							// if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
							//   options.parentMessageId = data.id
							//   message = ''
							//   return fetchChatAPIOnce()
							// }

							scrollToBottomIfAtBottom();

							if (
								data.choices[0].finish_reason === "stop" ||
								data.choices[0].finish_reason === "STOP"
							) {
								if (annotation?.id) {
									annotation.note =
										obj.question +
										"\n" +
										dataSources.value[dataSources.value.length - 1].text;
								}
								// console.log(dataSources.value);
								// updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
								updateChatSome(uuid, dataSources.value.length - 1, {
									loading: false,
									answerList: data.answerList,
									btnFlag: obj?.prompt !== "coursewareAssistantByModelPrompt",
									annotation: annotation?.id ? annotation : null,
								});
								loading.value = false;
							}
						} catch (error) {
							console.log(error);
						}
					}
				},
			});
		};

		await fetchChatAPIOnce();
	} catch (error: any) {
		console.log(error);
	} finally {
		await scrollToBottom();
		// loading.value = false
	}
};

// 重新生成
const onRegenerate = async (flag: boolean, index: any, annotation: any) => {
	if (flag) {
		// 代表是预制问题的重新生成
		await textLength(index);
	} else {
		console.log(dataSources.value[index - 1].text);

		// console.log(obj);
		uuid.value = props.dialogue.id;
		loading.value = true;

		const message = dataSources.value[index - 1].text;
		controller = new AbortController();
		updateChat(uuid.value, index, {
			dateTime: new Date().toLocaleString(),
			text: t("chat.thinking"),
			loading: true,
			answerList: [],
			inversion: false,
			error: false,
			conversationOptions: null,
		});
		await scrollToBottom();

		try {
			let lastText = "";
			const fetchChatAPIOnce = async () => {
				await fetchChatAPIProcess<Chat.ConversationResponse>({
					signal: controller.signal,
					question: message,
					category: "0",
					conversationId: uuid.value,
					agentId: "1950834085600997378",
					onDownloadProgress: ({event}) => {
						const xhr = event.target;
						const {responseText} = xhr;
						// if (inputFlag.netFlag) {
						// 深度思考
						// 按行分割响应文本
						const lines = responseText
							.split("\n")
							.filter((line) => line.trim() !== "");

						// 重置文本,避免重复累加
						lastText = "";
						// 处理每一行数据
						for (const line of lines) {
							const trimmedLine = line.replace(/^data: /, "").trim();
							// info.setloading(false)
							try {
								// const data = JSON.parse(trimmedLine)
								const data = JSON.parse(trimmedLine?.substring(5));
								console.log(data);
								stopId.value.id = data.id;
								stopId.value.index = index;
								// 停止回答用
								// currectemitterId.value = data.emitterId

								// 直接使用当前响应文本,不进行累加
								const deltaContent = data.choices[0].message.content || "";

								lastText += deltaContent;

								updateChat(uuid.value, index, {
									dateTime: new Date().toLocaleString(),
									text: lastText, // 使用完整的lastText,不再和之前的文本拼接
									// answerList: data.answerList,
									inversion: false,
									error: false,
									loading: true,
									btnFlag: true,
									conversationContentId: data.conversationContentId,
									conversationOptions: {
										conversationId: data.conversationContentId || "",
										parentMessageId: data.id || "",
									},
								});

								// if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
								//   options.parentMessageId = data.id
								//   message = ''
								//   return fetchChatAPIOnce()
								// }

								scrollToBottomIfAtBottom();

								if (
									data.choices[0].finish_reason === "stop" ||
									data.choices[0].finish_reason === "STOP"
								) {
									if (annotation?.id) {
										annotation.note =
											message + "\n" + dataSources.value[index].text;
									}
									// console.log(dataSources.value);
									// updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
									updateChatSome(uuid, index, {
										loading: false,
										answerList: data.answerList,
										btnFlag: true,
										annotation: annotation?.id ? annotation : null,
									});
									loading.value = false;
								}
							} catch (error) {
								console.log(error);
							}
						}
					},
				});
			};

			await fetchChatAPIOnce();
		} catch (error: any) {
			console.log(error);
		} finally {
			await scrollToBottom();
			// loading.value = false
		}
	}
};

function handleEnter(event: KeyboardEvent) {
	if (!isMobile.value) {
		if (event.key === "Enter" && !event.shiftKey) {
			event.preventDefault();
			if (
				props.submitFlag &&
				prompt &&
				!loading.value &&
				!summaryLoading.value &&
				!questionLoading.value
			)
				onConversation();
		}
	} else {
		if (event.key === "Enter" && event.ctrlKey) {
			event.preventDefault();
			if (
				props.submitFlag &&
				prompt &&
				!loading.value &&
				!summaryLoading.value &&
				!questionLoading.value
			)
				onConversation();
		}
	}
}

// 跳转到最后一页 添加一个预制问题
let problem = ref();
let problemTypa = ref(false);
const generateProblem = async (data: any) => {
	summaryLoading.value = true;
	try {
		let res = await generateSummarizeProblem(data);
		console.log(res);
		// 暂存问题内容   接口要用
		problem.value = res.data;
		addChat(uuid.value, {
			dateTime: new Date().toLocaleString(),
			text: res.data,
			loading: false,
			answerList: [],
			inversion: false,
			error: false,
			conversationOptions: null,
			btnFlag: false,
		});
	} finally {
		summaryLoading.value = false;
	}
};

// 用于触发获取预制问题
const textLength = async (index?: any) => {
	if (
		!dataSources.value?.length ||
		(typeof index === "number" && !isNaN(index))
	) {
		questionLoading.value = true;
		try {
			let res = await agentById("1950834085600997378");
			console.log(props.main);
			let obj = {
				summary: props.main.summary,
				id: props.main.id,
				agent: {
					modelId: res.data.modelId,
					modelTemp: res.data.modelTemp,
					maxLength: res.data.maxLength,
				},
			};
			let Problems = await getGenerateProblem(obj);

			if (typeof index === "number" && !isNaN(index)) {
				updateChatSome(uuid.value, index, {
					dateTime: new Date().toLocaleString(),
					text: "", // 使用完整的lastText,不再和之前的文本拼接
					// answerList: data.answerList,
					inversion: false,
					error: false,
					loading: false,
					conversationOptions: null,
					questionArr: Problems.data,
					btnFlag: true,
				});
			} else {
				addChat(uuid.value, {
					dateTime: new Date().toLocaleString(),
					text: "",
					loading: false,
					answerList: [],
					inversion: false,
					error: false,
					conversationOptions: null,
					questionArr: Problems.data,
					btnFlag: true,
				});
			}
		} finally {
			questionLoading.value = false;
		}
	}
};

// 点击预制问题
const handleAnswer = (msg: string) => {
	// console.log(msg)
	if (!loading.value) onConversation(msg);
};

// 停止响应
const stopId = ref({});
const handleStop = async () => {
	if (loading.value) {
		controller.abort();
		loading.value = false;
		await answersStop({id: stopId.value.id});
	}
};

// 采纳并标记疑难点
const mark = async (annotation: any) => {
	console.log(annotation);
	let res = await addTextbookAnnotations({
		content: annotation.text,
		note: markdownToPlainText(annotation.note),
		position: JSON.stringify(annotation.position),
		page: annotation.page,
		color: annotation.color,
		textbookId: props.main.id,
	});
	emit("noteAdded", {});
};
const filterThinkTags = (content: string) => {
	// 正则表达式匹配think标签及其内容
	// 考虑了可能的空格和不同大小写情况
	const thinkTagRegex = /<\s*think\s*>[^<]*<\s*\/\s*think\s*>/gi;

	// 替换匹配到的内容为空字符串
	return content.replace(thinkTagRegex, "");
};

/**
 * 将 Markdown 转换为纯文本
 * @param {string} text - 包含 Markdown 格式的文本
 * @returns {string} - 提取的纯文本
 */
function markdownToPlainText(text: any) {
	if (!text || typeof text !== "string") {
		return "";
	}
	text = filterThinkTags(text);

	try {
		// 使用临时DOM元素来解析HTML，然后提取纯文本
		const tempDiv = document.createElement("div");
		tempDiv.innerHTML = marked.parse(text);

		// 获取纯文本内容
		let plainText = tempDiv.textContent || tempDiv.innerText || "";

		// 清理多余的空白和换行
		plainText = plainText
			.replace(/\n{3,}/g, "\n\n")
			.replace(/^\s+|\s+$/g, "")
			.trim();

		return plainText;
	} catch (error) {
		console.error("Markdown转换失败:", error);

		// 如果解析失败，使用简化的方法
		let plainText = text
			// 移除代码块
			.replace(/```[\s\S]*?```/g, "")
			// 移除行内代码
			.replace(/`([^`]+)`/g, "$1")
			// 移除链接，保留文本
			.replace(/\[([^\]]+)\]\([^)]+\)/g, "$1")
			// 移除图片
			.replace(/!\[([^\]]*)\]\([^)]+\)/g, "$1")
			// 移除标题标记
			.replace(/^#{1,6}\s+/gm, "")
			// 移除粗体和斜体标记
			.replace(/\*\*([^*]+)\*\*/g, "$1")
			.replace(/\*([^*]+)\*/g, "$1")
			.replace(/__([^_]+)__/g, "$1")
			.replace(/_([^_]+)_/g, "$1")
			// 移除删除线
			.replace(/~~([^~]+)~~/g, "$1")
			// 移除引用标记
			.replace(/^>\s+/gm, "")
			// 移除列表标记
			.replace(/^[\s]*[-*+]\s+/gm, "")
			.replace(/^[\s]*\d+\.\s+/gm, "")
			// 移除水平线
			.replace(/^---+$/gm, "")
			.replace(/^\*\*\*+$/gm, "")
			// 清理多余的空白
			.replace(/\n{3,}/g, "\n\n")
			.replace(/^\s+|\s+$/g, "");

		return plainText;
	}
}

onMounted(() => {
	ToolsStore.ToolInfo.icon = icon1;
});

// 预制问题用
// let lodingFlag = ref(false)
let inputFlag = ref(false);

// 清空聊天内容
const clearChat = () => {
	dataSources.value = [];
};

defineExpose({
	textConversation,
	generateProblem,
	textLength,
	clearChat,
});
</script>

<template>
	<NSpin :show="show || summaryLoading || questionLoading">
		<template #description>
			<span v-if="summaryLoading">正在生成问题，请稍候...</span>
			<span v-else-if="questionLoading">正在生成查询条件，请稍后...</span>
			<span v-else>正在生成PDF，请稍候...</span>
		</template>
		<div class="px-6">
			<div
				id="scrollRef"
				ref="scrollRef"
				:class="!problem ? 'contentList' : 'contentLista'"
				class="overflow-y-scroll"
			>
				<Message
					v-for="(item, index) of dataSources"
					:key="index"
					:annotation="item.annotation"
					:answer-list="item.answerList"
					:btnFlag="item.btnFlag"
					:category="item.category"
					:conversation-content-id="item.conversationContentId"
					:date-time="item.dateTime"
					:endstatus="item.endstatus"
					:error="item.error"
					:inversion="item.inversion"
					:loading="item.loading"
					:problem="item.problem"
					:questionArr="item.questionArr"
					:text="item.text"
					@delete="handleDelete(index)"
					@handleAnswer="handleAnswer"
					@mark="mark"
					@regenerate="(flag:boolean,annotation?:any)=>onRegenerate(flag,index,annotation)"
				/>
			</div>

			<div
				v-if="!problem"
				class="h-[64px] rounded-[12px] border-[1px] border-[#E5E5E4] flex align-center submit"
			>
				<NInput
					v-model:value="prompt"
					:bordered="false"
					placeholder="向课件学习助手提问"
					size="large"
					@keypress="handleEnter"
				>
					<template #suffix>
						<div class="">
							<NButton
								v-show="!loading"
								:color="
                  !(submitFlag && prompt && !loading && !problemTypa)
                    ? '#D2D4D8'
                    : '#EBF3FF'
                "
								:disabled="!(submitFlag && prompt && !loading && !problemTypa)"
								class="!p-[8px] !rounded-[50%] !w-[40px] !h-[40px]"
								@click="onConversation('')"
							>
								<img
									v-if="!(submitFlag && prompt && !loading && !problemTypa)"
									alt=""
									class="w-[20px] h-[20px]"
									src="@/assets/workShopPage/test-btn.png"
								/>
								<img
									v-else
									alt=""
									class="w-[20px] h-[20px]"
									src="@/assets/toolboxPage/btna.png"
								/>
							</NButton>
							<img
								v-show="loading"
								alt=""
								class="w-[40px] h-[40px] cursor-pointer"
								src="@/assets/toolboxPage/stop.png"
								@click="handleStop"
							/>
						</div>
					</template>
				</NInput>
			</div>
			<div
				v-else
				class="h-[134px] rounded-[12px] border-[1px] border-[#E5E5E4] submita"
				style="background: #fff"
			>
				<NInput
					v-model:value="prompt"
					:autosize="{
            minRows: 3,
            maxRows: 3,
          }"
					:bordered="false"
					placeholder="向课件学习助手提问"
					size="large"
					type="textarea"
					@keypress="handleEnter"
				>
				</NInput>

				<div class="flex w-full px-[20px] justify-between items-center">
					<div
						v-if="!inputFlag"
						class="w-[36px] h-[36px] border-[1px] border-[#E1E1E1] rounded-[50%] cursor-pointer flex items-center justify-center"
						@click="inputFlag = !inputFlag"
					>
						<img
							alt=""
							class="w-[20px] h-[20px]"
							src="@/assets/toolboxPage/hdwt.png"
						/>
					</div>
					<div
						v-if="inputFlag"
						class="w-[36px] h-[36px] border-[1px] border-[#E1E1E1] rounded-[50%] cursor-pointer flex items-center justify-center"
						@click="inputFlag = !inputFlag"
					>
						<img
							alt=""
							class="w-[20px] h-[20px]"
							src="@/assets/toolboxPage/hdwta.png"
						/>
					</div>
					<NButton
						v-show="!loading"
						:color="
              !(submitFlag && prompt && !loading && !problemTypa)
                ? '#D2D4D8'
                : '#EBF3FF'
            "
						:disabled="!(submitFlag && prompt && !loading && !problemTypa)"
						class="!p-[8px] !rounded-[50%] !w-[40px] !h-[40px]"
						@click="onConversation('')"
					>
						<img
							v-if="!(submitFlag && prompt && !loading && !problemTypa)"
							alt=""
							class="w-[20px] h-[20px]"
							src="@/assets/workShopPage/test-btn.png"
						/>
						<img
							v-else
							alt=""
							class="w-[20px] h-[20px]"
							src="@/assets/toolboxPage/btna.png"
						/>
					</NButton>
					<img
						v-show="loading"
						alt=""
						class="w-[40px] h-[40px] cursor-pointer"
						src="@/assets/toolboxPage/stop.png"
						@click="handleStop"
					/>
				</div>
			</div>
		</div>
	</NSpin>
</template>

<style lang='less' scoped>
.submit {
	:deep(.n-input .n-input__input-el) {
		height: 64px;
	}

	:deep(.n-input) {
		border-radius: 12px;
	}
}

.submita {
	:deep(.n-input) {
		border-radius: 12px;
	}
}

.contentList {
	height: calc(87vh - 114px);
}

.contentLista {
	height: calc(87vh - 184px);
}
</style>
