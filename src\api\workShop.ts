import {get, post} from '@/utils/request'

export function getagentlistApi<T = any>(data: any) {
	return get<T>({
		url: '/eaide/intelligent_agent/list',
		data,
	})
}


export function delagentlistApi<T = any>(data: any) {
	return get<T>({
		url: '/eaide/intelligent_agent/delete',
		data,
	})
}

export function setReleaseStatusApi<T = any>(data: any, id: any) {
	return post<T>({
		url: `/eaide/agent/${id}/update`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function addShop<T = any>(data: any) {
	return post<T>({
		url: '/emind/agent',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function addConversation<T = any>(data: any) {
	return post<T>({
		url: '/emind/conversation',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function editShop<T = any>(data: any, id: string) {
	return post<T>({
		url: `/emind/agent/${id}/update`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function agentKnowledge<T = any>(data?: any) {
	return get<T>({
		url: '/emind/agentKnowledge/sync',
		data,
	})
}

export function agentParam<T = any>(data?: any) {
	return get<T>({
		url: '/emind/agent/param',
		data,
	})
}

export function getAgentKnowledge<T = any>(data?: any) {
	return get<T>({
		url: '/emind/agentKnowledge',
		data
	})
}

export function museModels<T = any>(data?: any) {
	return get<T>({
		url: '/muse/models/all',
		data,
	})
}

export function getAgentlist<T = any>(data: any) {
	return get<T>({
		url: '/emind/agent',
		data,
	})
}

export function delModel<T = any>(id: any) {
	return post<T>({
		url: `/emind/agent/${id}/delete`,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function getAgentlistId<T = any>(id: any) {
	return get<T>({
		url: `/emind/agent/${id}`,
	})
}

export function getAgentlistAll<T = any>(data: any) {
	return get<T>({
		url: '/emind/agent/all',
		data,
	})
}

export function optimize<T = any>(data: any) {
	return get<T>({
		url: '/emind/agent/prompt_template_optimize',
		data,
	})
}

export function findVisitConfAgentId<T = any>(id: any) {
	return get<T>({
		url: `/emind/agent_visit_conf/find_visit_conf_by_agentId/${id}`,
	})
}

export function agentVisitConf<T = any>(data: any) {
	return post<T>({
		url: '/emind/agent_visit_key_conf',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function delAgentVisitConf<T = any>(id: any) {
	return post<T>({
		url: `/emind/agent_visit_key_conf/${id}/delete`,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function agentVisitConfUpdate<T = any>(id: any, data: any) {
	return post<T>({
		url: `/emind/agent_visit_conf/${id}/update`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}
