<script lang="ts" setup>
import {
  computed,
  onMounted,
  onUnmounted,
  onUpdated,
  ref,
  watch,
  nextTick,
} from "vue";
import MarkdownIt from "markdown-it";
import MdKatex from "@vscode/markdown-it-katex";
import MdLinkAttributes from "markdown-it-link-attributes";
import MdMermaid from "mermaid-it-markdown";
import hljs from "highlight.js";
import { NTooltip, NModal, NButton, NInput, NEllipsis } from "naive-ui";
import KnowledgeBase from "./KnowledgeBase.vue";
import { useToolsStore, usestretchoutStore } from "@/store";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { t } from "@/locales";
import { copyToClip } from "@/utils/copy";
import { conversationContentUpdate } from "@/api/courseware";

interface Props {
  inversion?: boolean;
  error?: boolean;
  text?: string;
  loading?: boolean;
  asRawText?: boolean;
  isPlay?: boolean;
  answerList: any;
  endstatus: any;
  questionArr?: any;
  annotation?: any;
  btnFlag?: boolean;
  problem?: boolean;
  conversationContentId: string;
  questionState?: number;
}

const props = defineProps<Props>();
/**********/
const emit = defineEmits([
  "handleCopy",
  "handleSound",
  "handleStopSound",
  "handleRegenerate",
  "mark",
  "handleAnswer",
]);
const useTretchOut = usestretchoutStore();
const ToolsStore = useToolsStore();

const copy = () => {
  emit("handleCopy");
};
const simulationtestfun = () => {
  useTretchOut.setconversationContentId(props.conversationContentId);
};
const sound = () => {
  emit("handleSound");
};
const regenerate = () => {
  goodsType.value = "0";
  emit("handleRegenerate", !!props.questionArr?.length);
};
const stopSound = () => {
  emit("handleStopSound");
};
/**********/
const { isMobile } = useBasicLayout();

const textRef = ref<HTMLElement>();
const questionContainerRef = ref<HTMLElement>();
// 逐字打印相关变量
const displayedText = ref<string>(""); // 当前显示的文本
const originalText = ref<string>(""); // 完整的原始文本
const remainText = ref<string>(""); // 剩余待显示文本
const isAnimating = ref<boolean>(false); // 是否正在动画中
const isFinished = ref<boolean>(false); // 是否已完成动画
const isStreamReceiving = ref<boolean>(false); // 是否正在接收流式数据
let lastProcessedLength = 0; // 上次处理的文本长度，用于判断是否有新内容
let animationFrameId: number | null = null; // 动画帧ID

// 思考内容的逐字显示相关变量
const displayedThinkText = ref<string>(""); // 当前显示的思考文本
const originalThinkText = ref<string>(""); // 原始完整的思考文本
const hasThinkContent = ref<boolean>(false); // 是否包含思考内容
const mdi = new MarkdownIt({
  html: true,
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language));
    if (validLang) {
      const lang = language ?? "";
      return highlightBlock(
        hljs.highlight(code, { language: lang }).value,
        lang
      );
    }
    return highlightBlock(hljs.highlightAuto(code).value, "");
  },
});

mdi
  .use(MdLinkAttributes, { attrs: { target: "_blank", rel: "noopener" } })
  .use(MdKatex)
  .use(MdMermaid);

const wrapClass = computed(() => {
  return [
    "text-wrap",
    "min-w-[20px]",
    // 'rounded-md',
    "rounded-[12px]",
    isMobile.value ? "p-2" : "px-3 py-2",
    props.inversion ? "px-4 py-3" : "px-8 py-7",
    props.inversion ? "bg-[#E0ECFD]" : "bg-[#FFFFFF]",
    props.inversion ? "dark:bg-[#a1dc95]" : "dark:bg-[#1e1e20]",
    props.inversion ? "message-request" : "message-reply",
    { "text-red-500": props.error },
  ];
});

// 深度思考内容
const thinkContent = computed(() => {
  if (!hasThinkContent.value) return "";

  // 使用动画中的思考文本或完整文本
  const textToRender =
    isAnimating.value && !isFinished.value
      ? displayedThinkText.value
      : originalThinkText.value;

  return mdi.render(textToRender);
});

// 文本处理和渲染，修改为使用displayedText
const processedText = computed(() => {
  let value = "";
  if (!props.loading) value = props.text || "";
  else value = props.inversion ? props.text ?? "" : displayedText.value;

  value = value.replace(/四川大学/g, "四川大学");

  const match = value.match(/<think>([\s\S]*?)<\/think>/);
  if (match) {
    value = value.replace(/<think>([\s\S]*?)<\/think>/, "").trim();
  } else {
    const partialMatch = value.match(/<think>([\s\S]*)/);
    if (partialMatch) value = "";
  }

  if (!props.asRawText) {
    // 对数学公式进行处理，自动添加 $$ 符号
    const escapedText = escapeBrackets(escapeDollarNumber(value));
    return mdi.render(escapedText);
  }
  return mdi.render(value);
});

// 监听text属性变化，处理新文本
watch(
  () => props.text,
  (newText, oldText) => {
    /*   console.log('[Text] text属性变化:', newText, oldText)
		if (props.inversion || props.loading) {
			// 用户消息或加载中状态，直接显示完整文本
			displayedText.value = newText || ''
			return
		}
	*/
    if (!props.loading) return;

    if (!newText) {
      // 文本为空，重置状态
      resetTextAnimation();
      return;
    }

    // 检查是否为新的对话（文本完全变化）
    const isNewConversation =
      !oldText || oldText.length === 0 || !newText.startsWith(oldText);
    if (isNewConversation) {
      console.log("[Text-isNewConversation] 新的对话，重置并开始新动画");
      // 新的对话，重置并开始新动画
      resetTextAnimation();
      // 提取并设置思考内容
      extractThinkContent(newText);
      startTextAnimation(newText);
    } else if (newText !== oldText) {
      // 流式数据更新，追加新内容
      extractThinkContent(newText);
      updateStreamContent(newText, oldText || "");
    }
  },
  { immediate: true }
);

// 监听loading状态变化
watch(
  () => props.loading,
  (isLoading) => {
    console.log("[Text] Loading状态变化:", isLoading);
    /*
	if (isLoading) {
		// 开始加载，重置状态
		resetTextAnimation()
	}
	else if (!props.inversion && props.text) {
		// 加载完成，可能是非流式返回的情况，确保动画正确开始
		if (!isAnimating.value) {
			console.log('[Text] 开始非流式文本动画')
			startTextAnimation(props.text)
		}
	} */
  }
);

// 监听questionArr变化，设置容器宽度
watch(
  () => props.questionArr,
  (newQuestionArr) => {
    if (newQuestionArr?.length) {
      nextTick(() => {
        setQuestionContainerWidth();
      });
    }
  },
  { immediate: true }
);

// 重置动画状态
function resetTextAnimation() {
  console.log("[Text] 重置动画状态");
  stopTextAnimation();
  displayedText.value = "";
  originalText.value = "";
  remainText.value = "";
  displayedThinkText.value = ""; // 重置思考内容显示
  originalThinkText.value = ""; // 重置原始思考内容
  hasThinkContent.value = false; // 重置思考内容标志
  isAnimating.value = false;
  isFinished.value = false;
  isStreamReceiving.value = false;
  lastProcessedLength = 0;
}

// 提取思考内容
function extractThinkContent(text: string) {
  const value = text ? text.replace(/四川大学/g, "四川大学") : "";

  // 清除之前的思考内容状态，除非已经有完整的思考内容
  if (
    !originalThinkText.value ||
    !originalThinkText.value.includes("</think>")
  ) {
    hasThinkContent.value = false;
    originalThinkText.value = "";
  } else {
    // 已经有完整的思考内容，不再更新
    return;
  }

  // 完整的<think>标签
  const match = value.match(/<think>([\s\S]*?)<\/think>/);
  if (match) {
    const thinkText = match[1];
    if (props.loading)
      originalThinkText.value = `${escapeBrackets(
        escapeDollarNumber(thinkText)
      )}</think>`;
    // 标记为完整思考内容
    else
      originalThinkText.value = `${escapeBrackets(
        escapeDollarNumber(thinkText)
      )}`; // 标记为完整思考内容

    hasThinkContent.value = true;
    return;
  }

  // 不完整的<think>标签（可能是流式传输中）
  const partialMatch = value.match(/<think>([\s\S]*)/);
  if (partialMatch) {
    const thinkText = partialMatch[1];
    originalThinkText.value = escapeBrackets(escapeDollarNumber(thinkText));
    hasThinkContent.value = true;
  }
}

// 开始文字动画
function startTextAnimation(fullText: string) {
  console.log("[Text] 开始文字动画, 文本长度:", fullText?.length);
  // 停止之前的动画
  stopTextAnimation();

  // 初始化动画状态
  const plainText = fullText || "";
  originalText.value = plainText;
  displayedText.value = "";
  remainText.value = plainText;
  displayedThinkText.value = ""; // 初始化思考内容显示为空
  isAnimating.value = true;
  isFinished.value = false;

  // 启动动画
  animateResponseText();
}

// 更新流式内容
function updateStreamContent(newText: string, oldText: string) {
  if (!newText || newText === oldText) return;

  // 获取新增的内容
  const addedContent = newText.slice(oldText.length);
  if (!addedContent) return;

  // console.log('[Text] 更新流式内容, 新增内容长度:', addedContent.length)

  // 更新原始文本
  originalText.value = newText;

  // 如果动画正在进行，将新内容添加到待显示文本
  if (isAnimating.value && !isFinished.value) {
    remainText.value += addedContent;
    isStreamReceiving.value = true;
  } else {
    // 如果动画已经结束或未开始，则开始新的动画
    displayedText.value = oldText; // 保留已显示的内容
    remainText.value = addedContent;
    isAnimating.value = true;
    isFinished.value = false;
    isStreamReceiving.value = true;

    if (!animationFrameId) animateResponseText();
  }
}

// 动画函数
function animateResponseText() {
  // 如果请求被中止或动画已完成且无新内容，则结束动画
  if (isFinished.value && !isStreamReceiving.value) {
    console.log("[Text] 动画完成");
    displayedText.value = originalText.value;
    displayedThinkText.value = originalThinkText.value.replace("</think>", ""); // 设置完整思考内容并移除结束标记
    animationFrameId = null;

    return;
  }

  if (remainText.value.length > 0) {
    // 动态计算每帧处理的字符数，根据剩余文本长度和是否在接收流调整速度
    let fetchCount = Math.max(1, Math.round(remainText.value.length / 60));

    // 如果正在接收流数据，可以适当减慢速度，提供更平滑的体验
    if (isStreamReceiving.value)
      fetchCount = Math.max(1, Math.min(fetchCount, 3)); // 限制每帧最多处理3个字符

    const fetchText = remainText.value.slice(0, fetchCount);
    displayedText.value += fetchText;
    remainText.value = remainText.value.slice(fetchCount);

    // 同步更新思考内容的显示进度，但仅当思考内容未完成时
    if (hasThinkContent.value && originalThinkText.value) {
      // 检查思考内容是否已经完整（包含结束标签）
      const isThinkComplete = originalThinkText.value.includes("</think>");

      if (
        isThinkComplete &&
        displayedThinkText.value.length >=
          originalThinkText.value.replace("</think>", "").length
      ) {
        // 思考内容已完整且已显示完全，不再更新
        displayedThinkText.value = originalThinkText.value.replace(
          "</think>",
          ""
        );
      } else if (!isThinkComplete) {
        // 思考内容仍在接收中，正常更新显示进度
        const thinkProgress = Math.min(
          1,
          displayedText.value.length / originalText.value.length
        );
        const thinkLength = Math.floor(
          originalThinkText.value.length * thinkProgress
        );
        displayedThinkText.value = originalThinkText.value.slice(
          0,
          thinkLength
        );
      }
    }

    // 如果处理完当前所有文本，但可能还在接收新内容
    if (remainText.value.length === 0) {
      // 检查一段时间内是否有新内容到达
      if (lastProcessedLength === originalText.value.length) {
        console.log("[Text] 流接收结束");
        isStreamReceiving.value = false; // 无新内容，认为流接收结束
      }

      lastProcessedLength = originalText.value.length;

      if (!isStreamReceiving.value) {
        isFinished.value = true; // 确认完成
        // 确保思考内容完整显示
        displayedThinkText.value = originalThinkText.value.replace(
          "</think>",
          ""
        );
      }
    }
  } else if (isStreamReceiving.value) {
    // 当前无内容但仍在接收流，等待新内容
  } else {
    // 文本已全部显示且不再接收新内容
    console.log("[Text] 动画结束");
    isAnimating.value = false;
    isFinished.value = true;
    // 确保思考内容完整显示
    displayedThinkText.value = originalThinkText.value.replace("</think>", "");
    animationFrameId = null;
    return;
  }

  // 继续下一帧
  animationFrameId = requestAnimationFrame(animateResponseText);
}

// 停止动画
function stopTextAnimation() {
  console.log("[Text] 停止动画");
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }

  // 如果动画被中断，直接显示完整文本
  if (isAnimating.value && !isFinished.value) {
    displayedText.value = originalText.value;
    displayedThinkText.value = originalThinkText.value.replace("</think>", ""); // 显示完整思考内容并移除结束标记
    isAnimating.value = false;
    isFinished.value = true;
    isStreamReceiving.value = false;
  }
}

// 设置猜你想问容器宽度与scrollRef一致
function setQuestionContainerWidth() {
  if (questionContainerRef.value) {
    let referenceElement = null;

    referenceElement =
      document.querySelector(".max-w-screen-xl") ||
      document.querySelector("#scrollRef") ||
      document.querySelector('[ref="scrollRef"]');

    if (!referenceElement) {
      const markdownBody =
        questionContainerRef.value.parentElement?.querySelector(
          ".markdown-body"
        );
      if (markdownBody) {
        referenceElement = markdownBody;
      }
    }

    if (!referenceElement) {
      referenceElement =
        questionContainerRef.value.closest(".text-wrap") ||
        questionContainerRef.value.closest(".message-reply");
    }

    if (referenceElement) {
      const referenceWidth = referenceElement.clientWidth;
      const targetWidth = referenceWidth - 100; // 比参考容器宽38px
      questionContainerRef.value.style.width = `${targetWidth}px`;
      questionContainerRef.value.style.maxWidth = `${targetWidth}px`;
      console.log(
        `[Text] 设置猜你想问容器宽度: ${targetWidth}px (参考宽度: ${referenceWidth}px + 38px)`
      );
    }
  }
}

function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">${t(
    "chat.copyCode"
  )}</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;
}

function addCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll(".code-block-header__copy");
    copyBtn.forEach((btn) => {
      btn.addEventListener("click", () => {
        const code = btn.parentElement?.nextElementSibling?.textContent;
        if (code) {
          copyToClip(code).then(() => {
            btn.textContent = t("chat.copied");
            setTimeout(() => {
              btn.textContent = t("chat.copyCode");
            }, 1000);
          });
        }
      });
    });
  }
}

function removeCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll(".code-block-header__copy");
    copyBtn.forEach((btn) => {
      btn.removeEventListener("click", () => {});
    });
  }
}

function escapeDollarNumber(text: string) {
  let escapedText = "";

  for (let i = 0; i < text.length; i += 1) {
    let char = text[i];
    const nextChar = text[i + 1] || " ";

    if (char === "$" && nextChar >= "0" && nextChar <= "9") char = "\\$";

    escapedText += char;
  }

  return escapedText;
}

function escapeBrackets(text: string) {
  const pattern =
    /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g;
  return text.replace(
    pattern,
    (match, codeBlock, squareBracket, roundBracket) => {
      if (codeBlock) return codeBlock;
      else if (squareBracket) return `$$${squareBracket}$$`;
      else if (roundBracket) return `$${roundBracket}$`;
      return match;
    }
  );
}

// 点赞点踩
let goodsType = ref("0");
let showModal = ref(false);
let loserValue = ref("");
let list = ref([
  { tit: "有害/不安全", id: "0" },
  { tit: "虚假信息", id: "1" },
  { tit: "没有帮助", id: "2" },
  { tit: "其他", id: "3" },
]);
let listActive = ref(null);
const good = async (type: any) => {
  console.log(type);
  if (type === "-1") {
    showModal.value = true;
    // await conversationContentUpdate(props.conversationContentId, {
    // 	appraise: type
    // })
    // goodsType.value = type
  } else {
    await conversationContentUpdate(props.conversationContentId, {
      appraise: type,
    });
    goodsType.value = type;
  }
};
const loserSubmit = async () => {
  await conversationContentUpdate(props.conversationContentId, {
    appraise: "-1",
    feedbackType: listActive.value,
    feedbackFeedbackContent: loserValue.value,
  });
  showModal.value = false;
  goodsType.value = "-1";
  loserValue.value = "";
  listActive.value = null;
};

onMounted(() => {
  addCopyEvents();
  // 设置猜你想问容器宽度
  nextTick(() => {
    setQuestionContainerWidth();
  });
});

onUpdated(() => {
  addCopyEvents();
  // 每次更新后重新设置宽度
  nextTick(() => {
    setQuestionContainerWidth();
  });
});

onUnmounted(() => {
  stopTextAnimation();
  removeCopyEvents();
});
</script>

<template>
  <div :class="wrapClass" class="text-black relative">
    <div ref="textRef" class="leading-relaxed break-words">
      <div v-if="!inversion">
        <div
          v-if="hasThinkContent && thinkContent"
          :class="{ 'markdown-body-typing': isAnimating && !isFinished }"
          class="text-[14px] text-[#909399] border-[#3232334d;] mb-[24px] leading-[28px]"
          v-html="thinkContent"
        />
        <div
          v-if="!asRawText"
          :class="{
            'markdown-body-generate': loading,
            'markdown-body-typing': isAnimating && !isFinished,
          }"
          class="markdown-body"
          v-html="processedText"
        />
        <div v-else class="whitespace-pre-wrap" v-text="text" />
        <div
          v-if="props.questionArr?.length"
          ref="questionContainerRef"
          class=""
        >
          <p class="caini">猜你想问</p>
          <div class="flex flex-col gap-[10px] mt-[10px]">
            <div
              v-for="(item, index) in props.questionArr"
              :key="index"
              class="border border-[#EAEDF0] rounded-[8px] flex h-[42px] items-center px-[11px] gap-[7px] cursor-pointer items"
              @click="$emit('handleAnswer', item)"
            >
              <img alt="" src="@/assets/chat/chat-icon.png" />
              <n-ellipsis style="width: 100%">
                {{ item }}
              </n-ellipsis>
              <!--							<p class="text-[14px] w-full text-ellipsis">{{ item }}</p>-->
            </div>
          </div>
        </div>
        <div class="flex items-center gap-2 mt-5">
          <!--					{{ props.btnFlag && props.conversationContentId }}-->
          <NTooltip
            v-if="
              props.conversationContentId &&
              props.btnFlag &&
              !props.loading &&
              !props.questionArr?.length
            "
            trigger="hover"
          >
            <template #trigger>
              <div
                class="iconbox cursor-pointer"
                @click="good(goodsType === '1' ? '0' : '1')"
              >
                <img
                  v-if="goodsType !== '1'"
                  alt=""
                  class="w-[20px] h-[20px]"
                  src="@/assets/toolboxPage/good.png"
                />
                <img
                  v-if="goodsType === '1'"
                  alt=""
                  class="w-[20px] h-[20px]"
                  src="@/assets/toolboxPage/gooda.png"
                />
              </div>
            </template>
            满意
          </NTooltip>

          <NTooltip
            v-if="
              props.conversationContentId &&
              !props.loading &&
              props.btnFlag &&
              !props.questionArr?.length
            "
            trigger="hover"
          >
            <template #trigger>
              <div
                class="iconbox cursor-pointer"
                @click="good(goodsType === '-1' ? '0' : '-1')"
              >
                <img
                  v-if="goodsType !== '-1'"
                  alt=""
                  class="w-[20px] h-[20px]"
                  src="@/assets/toolboxPage/loser.png"
                />
                <img
                  v-if="goodsType === '-1'"
                  alt=""
                  class="w-[20px] h-[20px]"
                  src="@/assets/toolboxPage/losera.png"
                />
              </div>
            </template>
            不满意
          </NTooltip>

          <NTooltip
            v-if="
              (props.conversationContentId || props.questionArr?.length) &&
              !props.loading &&
              props.btnFlag
            "
            trigger="hover"
          >
            <template #trigger>
              <div class="iconbox cursor-pointer" @click="regenerate()">
                <img
                  alt=""
                  class="w-[20px] h-[20px]"
                  src="../../../../assets/regenerate.png"
                />
              </div>
            </template>
            重新生成
          </NTooltip>

          <NTooltip v-if="!!props.annotation?.id" trigger="hover">
            <template #trigger>
              <div class="iconbox cursor-pointer" @click="$emit('mark')">
                <img
                  alt=""
                  class="w-[20px] h-[20px]"
                  src="../../../../assets/toolboxPage/ynd.png"
                />
              </div>
            </template>
            采纳并标记疑难点
          </NTooltip>
        </div>
        <div>
          <KnowledgeBase
            v-if="answerList?.length > 0"
            :answer-list="answerList"
          />
        </div>
      </div>
      <div v-else class="relative talkMsg">
        <!-- 状态2用户回复气泡的左上角图标 -->
        <div v-if="problem" class="absolute -left-5 -top-5 z-10">
          <img
            alt="状态2图标"
            class="w-4 h-4"
            src="@/assets/toolboxPage/xiu.png"
          />
        </div>
        <div class="whitespace-pre-wrap" v-text="text" />
      </div>
    </div>
  </div>
  <n-modal
    v-model:show="showModal"
    :bordered="false"
    :segmented="{
      content: 'soft',
      footer: 'soft',
    }"
    :style="'width: 600px'"
    class="custom-card"
    preset="card"
    size="huge"
    title="反馈"
  >
    <div class="w-full">
      <div class="flex w-full gap-2 mb-5">
        <div
          v-for="(item, index) in list"
          :key="index"
          :class="listActive === item.id ? 'listActive' : 'defute'"
          class="px-[24px] py-[5px] cursor-pointer"
          @click="listActive = item.id"
        >
          {{ item.tit }}
        </div>
      </div>
      <n-input
        v-model:value="loserValue"
        placeholder="我们想知道你对此回答不满意的原因，你认为更好的回答是什么？"
        type="textarea"
      />
    </div>
    <template #footer>
      <div class="w-full flex flex-row-reverse gap-4">
        <n-button type="info" @click="loserSubmit"> 提交</n-button>
        <n-button
          @click="
            showModal = false;
            loserValue = '';
            listActive = null;
          "
          >取消
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<style lang="less">
@import url(./style.less);

.iconbox {
  width: 30px;
  height: 30px;
  background: #e9f1ff;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.iconbox:hover {
  border: 1px solid #125eff;
}

.listActive {
  background: #ebf3ff;
  color: #1263ff;
}

.defute {
  border: 1px solid #e9ecf3;
}

.text-ellipsis {
  /* 不换行 */
  white-space: nowrap;
  /* 溢出隐藏 */
  overflow: hidden;
  /* 溢出显示省略号 */
  text-overflow: ellipsis;
  /* 可选：设置宽度限制，否则可能不会触发省略效果 */
}
.caini {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #0c0d0d;
  letter-spacing: 0;
  line-height: 26px;
}
.items {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #323233;
  letter-spacing: 0;
}
.talkMsg {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #0c0d0d;
  letter-spacing: 0;
  text-align: right;
}
.markdown-body {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #0c0d0d;
  letter-spacing: 0;
  line-height: 26px;
}
</style>
