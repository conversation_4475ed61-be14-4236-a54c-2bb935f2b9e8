{"id": "1752222297155-8w029tpgu", "name": "知识检索聊天机器人", "description": "基于Dify工作流转换的知识检索+聊天机器人流程，支持从知识库检索信息并智能回答", "nodes": [{"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "description": "知识检索聊天机器人的起始点", "status": "idle", "config": {"variables": [{"name": "query", "type": "string", "description": "用户查询问题", "required": true, "defaultValue": ""}], "fileUpload": {"enabled": false, "allowedTypes": ["image"], "allowedExtensions": [".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG"], "maxFileSize": 15, "maxImageSize": 10, "maxCount": 3}}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, {"id": "knowledge-retrieval-node", "type": "knowledge", "dimensions": {"width": 239, "height": 122}, "computedPosition": {"x": 357.3778444210218, "y": 166.5708**********, "z": 0}, "handleBounds": {"source": [{"id": "knowledge-retrieval-node-output", "type": "source", "nodeId": "knowledge-retrieval-node", "position": "right", "x": 237.00009934076644, "y": 56.8906357241018, "width": 8, "height": 8}], "target": [{"id": "knowledge-retrieval-node-input", "type": "target", "nodeId": "knowledge-retrieval-node", "position": "left", "x": -6.000018656999022, "y": 56.8906357241018, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 357.3778444210218, "y": 166.5708**********}, "data": {"label": "知识检索", "description": "从知识库中检索与用户问题相关的文本内容", "status": "idle", "config": {"database": "default", "searchType": "semantic", "maxResults": 5, "retrievalMode": "single", "singleOutput": true, "queryVariable": "{{query}}", "datasetIds": [], "model": {"provider": "openai", "name": "gpt-3.5-turbo", "mode": "chat", "completionParams": {"temperature": 0, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}}, "variables": [{"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "llm-answer-node", "type": "llm", "dimensions": {"width": 217, "height": 114}, "computedPosition": {"x": 693.3141716738858, "y": 169.07804399172167, "z": 0}, "handleBounds": {"source": [{"id": "llm-answer-node-output", "type": "source", "nodeId": "llm-answer-node", "position": "right", "x": 215.00008194062562, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-answer-node-input", "type": "target", "nodeId": "llm-answer-node", "position": "left", "x": -5.999967648143428, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 693.3141716738858, "y": 169.07804399172167}, "data": {"label": "LLM节点", "description": "基于检索到的上下文信息回答用户问题", "status": "idle", "config": {"model": "gpt-3.5-turbo", "provider": "openai", "temperature": 0.7, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0, "prompt": "You are a helpful assistant. \nUse the following context as your learned knowledge, inside <context></context> XML tags.\n<context>\n{{#context#}}\n</context>\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.", "systemPrompt": "You are a helpful assistant that answers questions based on provided context.", "memory": {"enabled": false, "windowSize": 50, "rolePrefix": {"user": "", "assistant": ""}}, "context": {"enabled": true, "variable": "{{#knowledge-retrieval-node.result#}}"}, "vision": {"enabled": false}, "variables": [{"name": "context", "type": "string", "source": "knowledge-retrieval-node.result"}, {"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, {"id": "answer-node", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 1000, "y": 200, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "answer-node-input-0", "type": "target", "nodeId": "answer-node", "position": "left", "x": -1.9999552101440796, "y": 25.499990026748552, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 1000, "y": 200}, "data": {"label": "结束节点", "description": "输出最终的智能回答结果", "status": "idle", "config": {"outputVariables": [{"name": "answer", "type": "string", "source": "llm-answer-node.text", "description": "基于知识库的智能回答"}, {"name": "context", "type": "string", "source": "knowledge-retrieval-node.result", "description": "检索到的相关上下文"}, {"name": "query", "type": "string", "source": "start-node.query", "description": "原始用户查询"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}], "edges": [{"id": "1752222309776-us7g3oxfy", "type": "default", "source": "start-node", "target": "knowledge-retrieval-node", "sourceHandle": "start-node-output", "targetHandle": "knowledge-retrieval-node-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "start-node", "type": "start", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 100, "y": 200, "z": 0}, "handleBounds": {"source": [{"id": "start-node-output", "type": "source", "nodeId": "start-node", "position": "right", "x": 122.00007330584654, "y": 25.499990026748552, "width": 8, "height": 8}], "target": null}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "description": "知识检索聊天机器人的起始点", "status": "idle", "config": {"variables": [{"name": "query", "type": "string", "description": "用户查询问题", "required": true, "defaultValue": ""}], "fileUpload": {"enabled": false, "allowedTypes": ["image"], "allowedExtensions": [".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG"], "maxFileSize": 15, "maxImageSize": 10, "maxCount": 3}}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "targetNode": {"id": "knowledge-retrieval-node", "type": "knowledge", "dimensions": {"width": 239, "height": 122}, "computedPosition": {"x": 357.3778444210218, "y": 166.5708**********, "z": 0}, "handleBounds": {"source": [{"id": "knowledge-retrieval-node-output", "type": "source", "nodeId": "knowledge-retrieval-node", "position": "right", "x": 237.00009934076644, "y": 56.8906357241018, "width": 8, "height": 8}], "target": [{"id": "knowledge-retrieval-node-input", "type": "target", "nodeId": "knowledge-retrieval-node", "position": "left", "x": -6.000018656999022, "y": 56.8906357241018, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 357.3778444210218, "y": 166.5708**********}, "data": {"label": "知识检索", "description": "从知识库中检索与用户问题相关的文本内容", "status": "idle", "config": {"database": "default", "searchType": "semantic", "maxResults": 5, "retrievalMode": "single", "singleOutput": true, "queryVariable": "{{query}}", "datasetIds": [], "model": {"provider": "openai", "name": "gpt-3.5-turbo", "mode": "chat", "completionParams": {"temperature": 0, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}}, "variables": [{"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 230.00007330584654, "sourceY": 229.49999002674855, "targetX": 351.37782576402276, "targetY": 227.46149409353063}, {"id": "1752222312014-7o0s02g89", "type": "default", "source": "knowledge-retrieval-node", "target": "llm-answer-node", "sourceHandle": "knowledge-retrieval-node-output", "targetHandle": "llm-answer-node-input", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "knowledge-retrieval-node", "type": "knowledge", "dimensions": {"width": 239, "height": 122}, "computedPosition": {"x": 357.3778444210218, "y": 166.5708**********, "z": 0}, "handleBounds": {"source": [{"id": "knowledge-retrieval-node-output", "type": "source", "nodeId": "knowledge-retrieval-node", "position": "right", "x": 237.00009934076644, "y": 56.8906357241018, "width": 8, "height": 8}], "target": [{"id": "knowledge-retrieval-node-input", "type": "target", "nodeId": "knowledge-retrieval-node", "position": "left", "x": -6.000018656999022, "y": 56.8906357241018, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 357.3778444210218, "y": 166.5708**********}, "data": {"label": "知识检索", "description": "从知识库中检索与用户问题相关的文本内容", "status": "idle", "config": {"database": "default", "searchType": "semantic", "maxResults": 5, "retrievalMode": "single", "singleOutput": true, "queryVariable": "{{query}}", "datasetIds": [], "model": {"provider": "openai", "name": "gpt-3.5-turbo", "mode": "chat", "completionParams": {"temperature": 0, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}}, "variables": [{"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "llm-answer-node", "type": "llm", "dimensions": {"width": 217, "height": 114}, "computedPosition": {"x": 693.3141716738858, "y": 169.07804399172167, "z": 0}, "handleBounds": {"source": [{"id": "llm-answer-node-output", "type": "source", "nodeId": "llm-answer-node", "position": "right", "x": 215.00008194062562, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-answer-node-input", "type": "target", "nodeId": "llm-answer-node", "position": "left", "x": -5.999967648143428, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 693.3141716738858, "y": 169.07804399172167}, "data": {"label": "LLM节点", "description": "基于检索到的上下文信息回答用户问题", "status": "idle", "config": {"model": "gpt-3.5-turbo", "provider": "openai", "temperature": 0.7, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0, "prompt": "You are a helpful assistant. \nUse the following context as your learned knowledge, inside <context></context> XML tags.\n<context>\n{{#context#}}\n</context>\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.", "systemPrompt": "You are a helpful assistant that answers questions based on provided context.", "memory": {"enabled": false, "windowSize": 50, "rolePrefix": {"user": "", "assistant": ""}}, "context": {"enabled": true, "variable": "{{#knowledge-retrieval-node.result#}}"}, "vision": {"enabled": false}, "variables": [{"name": "context", "type": "string", "source": "knowledge-retrieval-node.result"}, {"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "sourceX": 602.3779437617882, "sourceY": 227.46149409353063, "targetX": 687.3142040257424, "targetY": 225.96871828667972}, {"id": "1752222314094-8wffced3y", "type": "default", "source": "llm-answer-node", "target": "answer-node", "sourceHandle": "llm-answer-node-output", "targetHandle": "answer-node-input-0", "data": {}, "events": {}, "label": "", "animated": false, "selected": false, "markerEnd": {"type": "arrow", "color": "#64748b", "width": 20, "height": 20}, "style": {"stroke": "#64748b", "strokeWidth": 2}, "sourceNode": {"id": "llm-answer-node", "type": "llm", "dimensions": {"width": 217, "height": 114}, "computedPosition": {"x": 693.3141716738858, "y": 169.07804399172167, "z": 0}, "handleBounds": {"source": [{"id": "llm-answer-node-output", "type": "source", "nodeId": "llm-answer-node", "position": "right", "x": 215.00008194062562, "y": 52.890674294958046, "width": 8, "height": 8}], "target": [{"id": "llm-answer-node-input", "type": "target", "nodeId": "llm-answer-node", "position": "left", "x": -5.999967648143428, "y": 52.890674294958046, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 693.3141716738858, "y": 169.07804399172167}, "data": {"label": "LLM节点", "description": "基于检索到的上下文信息回答用户问题", "status": "idle", "config": {"model": "gpt-3.5-turbo", "provider": "openai", "temperature": 0.7, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0, "prompt": "You are a helpful assistant. \nUse the following context as your learned knowledge, inside <context></context> XML tags.\n<context>\n{{#context#}}\n</context>\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.", "systemPrompt": "You are a helpful assistant that answers questions based on provided context.", "memory": {"enabled": false, "windowSize": 50, "rolePrefix": {"user": "", "assistant": ""}}, "context": {"enabled": true, "variable": "{{#knowledge-retrieval-node.result#}}"}, "vision": {"enabled": false}, "variables": [{"name": "context", "type": "string", "source": "knowledge-retrieval-node.result"}, {"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": true}, "targetNode": {"id": "answer-node", "type": "end", "dimensions": {"width": 124, "height": 59}, "computedPosition": {"x": 1000, "y": 200, "z": 0}, "handleBounds": {"source": null, "target": [{"id": "answer-node-input-0", "type": "target", "nodeId": "answer-node", "position": "left", "x": -1.9999552101440796, "y": 25.499990026748552, "width": 8, "height": 8}]}, "draggable": true, "selectable": true, "selected": false, "dragging": false, "resizing": false, "initialized": false, "isParent": false, "position": {"x": 1000, "y": 200}, "data": {"label": "结束节点", "description": "输出最终的智能回答结果", "status": "idle", "config": {"outputVariables": [{"name": "answer", "type": "string", "source": "llm-answer-node.text", "description": "基于知识库的智能回答"}, {"name": "context", "type": "string", "source": "knowledge-retrieval-node.result", "description": "检索到的相关上下文"}, {"name": "query", "type": "string", "source": "start-node.query", "description": "原始用户查询"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "events": {}, "deletable": false}, "sourceX": 916.3142536145114, "sourceY": 225.96871828667972, "targetX": 998.0000447898559, "targetY": 229.49999002674855}], "createdAt": "2025-07-11T08:24:57.155Z", "updatedAt": "2025-07-11T08:25:14.759Z", "metadata": {"originalSource": "dify-workflow", "version": "1.0.0", "convertedAt": "2024-01-15T10:30:00.000Z", "description": "从Dify工作流YAML转换而来的知识检索聊天机器人流程", "tags": ["知识检索", "聊天机器人", "RAG", "问答系统"], "features": {"fileUpload": false, "speechToText": false, "textToSpeech": false, "suggestedQuestions": false}, "originalConfig": {"mode": "advanced-chat", "icon": "📑", "iconBackground": "#EFF1F5"}}}