<template>
  <div class="api-node-config">
    <!-- 请求方法配置 -->
    <n-form-item label="请求方法" path="config.method">
      <n-select
        v-model:value="formData.config.method"
        :options="methodOptions"
        placeholder="选择请求方法"
      />
    </n-form-item>

    <!-- 接口地址配置 -->
    <n-form-item label="接口地址" path="config.url">
      <n-input
        v-model:value="formData.config.url"
        placeholder="请输入API接口地址"
      />
    </n-form-item>

    <!-- 超时时间配置 -->
    <n-form-item label="超时时间(秒)" path="config.timeout">
      <n-input-number
        v-model:value="formData.config.timeout"
        placeholder="请求超时时间"
        :min="1"
        :max="300"
        :step="1"
        style="width: 100%"
      />
    </n-form-item>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { NFormItem, NSelect, NInput, NInputNumber } from 'naive-ui';
import { useNodeConfig } from './composables/useNodeConfig';
import type { NodeConfigProps, NodeConfigEvents } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const { methodOptions } = useNodeConfig(props);

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'api') {
      const config = newNode.data.config;
      
      // 初始化API节点默认配置
      if (!config.method) {
        config.method = 'GET';
      }
      if (!config.url) {
        config.url = '';
      }
      if (!config.timeout) {
        config.timeout = 30;
      }
    }
  },
  { immediate: true, deep: true }
);
</script>

<style scoped lang="less">
.api-node-config {
  :deep(.n-form-item) {
    .n-form-item-label {
      height: 22px;
      min-height: 22px;
      padding-bottom: 0px;
    }
    
    .n-form-item-blank {
      height: 38px;
      
      .n-input {
        height: 100%;
        background: #f5f5f6;
        border-radius: 8px;

        .n-input__border {
          display: none;
        }
      }
      
      .n-input__input-el {
        height: 100%;
        background: #f5f5f6;
      }
      
      .n-input-number {
        height: 100%;
        background: #f5f5f6;
        border-radius: 8px;
      }
    }
  }

  :deep(.n-select) {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;

    .n-base-selection__border {
      display: none;
    }
    
    .n-base-selection {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      .n-base-selection-label {
        height: 100%;
        background: #f5f5f6;
        border-radius: 8px !important;
      }
    }
  }
}
</style>
