import type { AxiosProgressEvent, GenericAbortSignal } from 'axios'

import { get, post } from '@/utils/request'
//流程运行测试接口
export function testRunApiProcess<T = any>(
  params: {
    signal?: GenericAbortSignal
    query: any
    graph: any
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  const data: Record<string, any> = {
    query: params.query,
    graph: params.graph,
  }
  return post<T>({
    url: '/emind/workflow/test/run',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
