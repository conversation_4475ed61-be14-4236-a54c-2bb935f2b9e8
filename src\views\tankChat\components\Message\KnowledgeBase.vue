<script lang='ts' setup>
import { NCollapse, NCollapseItem } from 'naive-ui'
import { Marked } from 'marked'
import { markedHighlight } from 'marked-highlight'
import hljs from 'highlight.js'
// 引入markdown样式
import 'highlight.js/styles/atom-one-dark.css'

// onMounted(async()=>{
// 	const content=await getArticleDetails(1).then(res=>res.content)
// 	articleDetails.value= marked.parse(content)
// })
/** *****************************************父子组件************************/
import { defineProps, toRef } from 'vue'

const props = defineProps({
  answerList: Array,
})

// 预处理函数：将 ~ 替换为 -
const processText = (text: any) => {
  text =	text.replace(/四川大学/g, 'xx大学')
  // 常见的 URL 后缀列表
  const commonExtensions = [
    'com', 'cn', 'org', 'net', 'pdf', 'gov', 'io', 'co', 'me', 'info', 'biz', 'wps', 'txt', 'rtf', 'bmp',
    'xyz', 'us', 'uk', 'ca', 'au', 'de', 'fr', 'ru', 'doc', 'docx', 'dotx', 'docm', 'dotm', 'dots', 'xlsx',
    'xml', 'xyz', 'xls', 'ppt', 'pptx', 'png', 'jpg', 'jpeg', 'gif', 'wav', 'flac', 'mp3',
    'mp4', 'avi',
  ]
  // 构建正则表达式
  const extensionRegex = commonExtensions.map(ext => `\\.${ext}`).join('|')
  const linkRegex = new RegExp(`(https?:\\/\\/[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=-]+(?:${extensionRegex})(?:\\/[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=-]*)?)`, 'g')
  // 使用 replace 方法替换匹配到的链接，在其后添加一个空格
  const addSpaceAfterLinks = text.replace(linkRegex, match => `${match} `)
  // const addSpaceAfterLinks = text.replace(/(https?:\/\/[^;；]+|ftp:\/\/[^;；]+)/g, match => `${match} `)
  // 然后将 ~ 替换为 -
  const replaceTildes = addSpaceAfterLinks.replace(/~/g, '-')
  return replaceTildes
}

const marked = new Marked(
  markedHighlight({
    langPrefix: 'hljs language-',
    highlight(code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'shell'
      return hljs.highlight(code, { language }).value
    },
  }),
)
const answerList = toRef(props, 'answerList')

if (answerList.value.length > 0) {
  answerList.value.forEach((item) => {
    if (item.fileName)
      item.fileName = item.fileName.replace(/四川大学/g, 'xx大学')
  })
}

/** *****************************************变量************************/

/** *****************************************普通方法************************/

/** *****************************************后台接口************************/

/** *****************************************生命周期************************/
</script>

<template>
  <div class="bg-[#ECECEC80] rounded-2xl p-5 mt-5">
    <div class="flex items-center gap-2 mb-5">
      <img src="../../../../assets/chat/lianjie.png" class="w-[22px] h-[22px]" alt="">
      <div class="text-[#0C0D0D] text-[16px] font-semibold">
        基于{{ answerList.length }}个搜索来源
      </div>
    </div>
    <NCollapse accordion>
      <template #arrow>
        <img src="../../../../assets/chat/rightrow.png" class="h-[14px] w-[14px]" alt="">
      </template>
      <NCollapseItem v-for="(item, index) in answerList" :title="`${index + 1}、${item.chunkTitle}`" :name="index">
        <div class="markdown-body" v-html="marked.parse(processText(item.chunkDetail))" />
      </NCollapseItem>
    </NCollapse>
  </div>
</template>

<style lang="less" scoped>
</style>
