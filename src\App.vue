<script setup lang="ts">
import { NConfigProvider } from 'naive-ui'
import { NaiveProvider } from '@/components/common'
import { useTheme } from '@/hooks/useTheme'
import { useLanguage } from '@/hooks/useLanguage'
import { useChatStore } from '@/store'
import { infoStore } from '@/store/modules/info'
const chatStore = useChatStore()
const { theme, themeOverrides } = useTheme()
const { language } = useLanguage()
const info = infoStore()
chatStore.setState({ userId: info.userInfo?.userId })
</script>

<template>
  <NConfigProvider
    class="h-full"
    :theme="theme"
    :theme-overrides="themeOverrides"
    :locale="language"
  >
    <NaiveProvider>
      <RouterView />
    </NaiveProvider>
  </NConfigProvider>
</template>
