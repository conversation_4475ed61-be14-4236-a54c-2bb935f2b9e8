// auth.ts
export const auth = {
  // 获取当前 token
  getToken(): string {
    return window.$EucpAuth?.getToken() || ''
  },

  // 获取用户信息
  getUserInfo(): any {
    return window.$EucpAuth?.getUserInfo() || null
  },

  // 检查角色权限
  hasRole(roles: string | string[]): boolean {
    return window.$EucpAuth?.hasRole(roles) || false
  },

  // 检查功能权限
  hasPermission(permissions: string | string[]): boolean {
    return window.$EucpAuth?.hasPermission(permissions) || false
  },

  // 退出登录
  logout(): void {
    window.$EucpAuth?.logout()
  },

  // 获取浏览器指纹
  getBrowserFingerprint(): string {
    return window.$Eucp.auth.getBrowserFingerprint()
  },
}
