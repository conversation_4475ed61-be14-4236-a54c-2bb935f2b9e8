<template>
	<div class="loadingPagebg">
		<header class="headers">
			<div class="left">
				<div class="gohome" @click="gohome()">
					<img src="@/assets/workShopPage/leftarrow.png">
				</div>
				{{ showstep.title }}
			</div>
		</header>
		<div class="documentbg flex justify-center  w-full">
			<transition name="slide-fade" class="w-[63.5%]">
				<div class="leftbox h-full ml-[24px]  bg-[#FFFFFF] p-[30px] overflow-y-auto">
					<div v-if="isloading" class="loading">
						<img :src="showstep.loadingIcon" class="loadingicon"/>
						<!-- 添加进度条组件 -->
						<div class="progressbox">
							<n-progress
								:percentage="loadingProgress"
								indicator-placement="inside"
								processing
								type="line"
							/>
						</div>
						<div class="loadingtitle">{{ showstep.loadTitle }}</div>
					</div>
					<div v-else class="h-full">
						<div v-if="!previewUrl && pageType == 'courseware'" class="loading">
						<img :src="showstep.loadingIcon" class="loadingicon"/>
						<div class="loadingtitle">材料格式转换中...</div>
						</div>
						<markPdf v-if="previewUrl && pageType == 'courseware'" ref="markPdfRef" :enableLog="false" :forbid="true"
										 :pdfUrl="previewUrl"/>
						<!-- 视频播放器组件 -->
						<VideoPlayer
							v-if="pageType == 'videoLearning'"
							ref="videoPlayerRef"
							:videoSrc="previewUrl"
						/>
					</div>

				</div>
			</transition>
			<div class="transition-all duration-500  rightbox w-[35%]">
				<div class="steps-container">
					<div class="steps-title">正在智能解析中...</div>
					<div class="steps-content">
						<div
							v-for="step in showstep.steps"
							:key="step.stepNumbers"
							:class="{ 'completed': currentStep > step.stepNumbers }"
							class="step-item"
						>
							<div
								:class="{'xuanzhuan': currentStep == step.stepNumbers}" class="step-circlebox"
							>
								<div :class="{ 'active': currentStep == step.stepNumbers, 'completed': currentStep > step.stepNumbers }"
										 class="step-circle">
									{{ step.number }}
								</div>
							</div>
							<div :class="{ 'active': currentStep == step.stepNumbers, 'completed': currentStep > step.stepNumbers }"
									 class="step-box">
								<div class="step-icon">
									<img :alt="step.title" :src="currentStep >= step.stepNumbers ? step.icon : step.iconGray">
								</div>
								<div class="step-content">
									<div class="step-text">{{ step.title }}</div>
									<div v-if="currentStep > step.stepNumbers" class="step-status">
										<span class="status-text">{{ step.status }}</span>
									</div>
								</div>
								<img v-if="currentStep > step.stepNumbers" :src="wanchengIcon" alt="completed" class="check-icon">
								<img v-if="currentStep == step.stepNumbers && processError" :src="shibai" class="check-icon">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>


<script setup>
import {NDivider, NButton, NRadio, NRadioGroup, NSpace, NSpin, useMessage, NProgress, NStep, NSteps, c} from "naive-ui";
import {ref, onMounted, computed, watch, useTemplateRef, nextTick} from "vue";
import shangchuan from '@/assets/loadingPage/shangchuan.png'
import shangchuanhui from '@/assets/loadingPage/shangchuanhui.png'
import zhuanhuan from '@/assets/loadingPage/zhuanhuan.png'
import zhuanhuanhui from '@/assets/loadingPage/zhuanhuanhui.png'
import lijie from '@/assets/loadingPage/lijie.png'
import lijiehui from '@/assets/loadingPage/lijiehui.png'
import chouqu from '@/assets/loadingPage/chouqu.png'
import chouquhui from '@/assets/loadingPage/chouquhui.png'
import zhanshi from '@/assets/loadingPage/zhanshi.png'
import zhanshihui from '@/assets/loadingPage/zhanshihui.png'
import wanchengIcon from '@/assets/loadingPage/wanchengIcon.png'
import shibai from '@/assets/loadingPage/shibai.png'
import loadingicon from '@/assets/loadingicon.png'
import videoLoadingIcon from '@/assets/videoLoadingIcon.png'
import videoshangchuan from '@/assets/loadingPage/videoshangchuan.png'
import videoshangchuanhui from '@/assets/loadingPage/videoshangchuanhui.png'
import videofenxi from '@/assets/loadingPage/videofenxi.png'
import videofenxihui from '@/assets/loadingPage/videofenxihui.png'
import videozhuanyi from '@/assets/loadingPage/videozhuanyi.png'
import videozhuanyihui from '@/assets/loadingPage/videozhuanyihui.png'
import videolijie from '@/assets/loadingPage/videolijie.png'
import videolijiehui from '@/assets/loadingPage/videolijiehui.png'
import videochouqu from '@/assets/loadingPage/videochouqu.png'
import videochouquhui from '@/assets/loadingPage/videochouquhui.png'
import videobiaoji from '@/assets/loadingPage/videobiaoji.png'
import videobiaojihui from '@/assets/loadingPage/videobiaojihui.png'
// import markPdf from '@/views/loadingPage/components/index-pdf.vue'
import markPdf from '@/views/chat/components/mark/index-pdf.vue'
import VideoPlayer from "@/components/common/VideoPlayer/index.vue";

import {uploadApiProcess} from "@/api/loadingPage"

import {useRouter} from "vue-router";
import {loadingPageStore} from '@/store'

const router = useRouter();
const loadingStorage = loadingPageStore();
const markPdfRef = useTemplateRef('markPdfRef')
// VideoPlayer组件引用
const videoPlayerRef = ref();

var uploadStatus = computed(() => {
	return loadingStorage.loadingPageData.uploadStatus;
});
var pdfUrl = ref("")
var previewUrl = computed(() => {
	if (pageType.value == 'courseware') {
		return pdfUrl.value;
	} else {
		return loadingStorage.loadingPageData.previewUrl;
	}
});
var fileUrl = computed(() => {
	return loadingStorage.loadingPageData.fileUrl;
});
var fileId = computed(() => {
	return loadingStorage.loadingPageData.fileId;
});
var size = computed(() => {
	return loadingStorage.loadingPageData.size;
});
var pageType = ref("courseware")
var processError = ref(false)
const message = useMessage();
var isloading = ref(true);
var loadingProgress = ref(0);
var currentStep = ref(1);
// 添加一个标志位，用于跟踪动画是否执行完成
var animationCompleted = ref(false);
watch(previewUrl, (newVal) => {
	if (newVal) {
		if (pageType.value == 'courseware') {
			nextTick(() => {
				setTimeout(() => {
					markPdfRef.value.init(newVal)
				}, 500);
			})
		}
	}
})
// 监听uploadStatus变化
watch(uploadStatus, (newVal) => {
	if (newVal === true) {
		// 如果uploadStatus变为true，无论动画是否完成，都设置进度为100%
		// 但不立即设置isloading为false，而是等待进度条动画完成
		loadingProgress.value = 100;
		// 停止当前动画
		animationCompleted.value = true;

		// 添加一个小延迟确保进度条有时间显示到100%
		setTimeout(() => {
			isloading.value = false;
			currentStep.value += 1;
			uploadProcessFun();
		}, 500); // 500毫秒延迟，确保进度条动画可见
	}
});

const gohome = () => {
	if (pageType.value == "courseware") {
		router.replace({
			path: '/courseware',
		})
	} else {
		router.replace({
			path: '/videoStudyHelper',
		})
	}

}
const jumpPage = () => {
	console.log('跳转页面执行了192');
	// 获取当前路由路径
	const currentPath = router.currentRoute.value.path;

	// 验证当前页面是否为 loadingPage
	if (!currentPath.includes('loadingPage')) {
		return; // 不是 loadingPage 则阻止跳转
	}

	var path
	if (pageType.value == "courseware") {
		path = "/coursewareChat"
	} else if (pageType.value == "videoLearning") {
		path = "/videoStudyHelperChat"
	}
	router.push({
		path: path,
		query: {
			id: fileId.value
		}
	})
}
// 在setuploaded函数中也做相应修改
const setuploaded = () => {
	// 获取文件大小(字节)
	const fileSizeInBytes = size.value;
	if (!fileSizeInBytes) return;

	// 将字节转换为KB
	const fileSizeInKB = fileSizeInBytes / 1024;

	// 按照100KB/秒计算上传时间(秒)
	const uploadTimeInSeconds = fileSizeInKB / 100;

	// 确保至少有2秒的动画时间，避免太快完成
	const actualUploadTime = Math.max(uploadTimeInSeconds, 2);

	// 计算每毫秒需要增加的进度值
	const totalProgress = 98;
	const progressPerMs = totalProgress / (actualUploadTime * 1000);

	// 重置进度为0和动画完成标志
	loadingProgress.value = 0;
	animationCompleted.value = false;

	// 使用定时器实现进度条动画
	const startTime = Date.now();
	const updateProgress = () => {
		// 如果动画已被标记为完成，则停止更新
		if (animationCompleted.value) return;

		const elapsedTime = Date.now() - startTime;
		const progress = Math.min(elapsedTime * progressPerMs, totalProgress);
		loadingProgress.value = Math.round(progress);

		if (progress < totalProgress) {
			requestAnimationFrame(updateProgress);
		} else {
			// 动画执行完成
			animationCompleted.value = true;
			// 检查uploadStatus
			if (uploadStatus.value === true) {
				// 如果uploadStatus为true，设置进度为100%
				loadingProgress.value = 100;
				// 添加延迟确保进度条动画可见
				setTimeout(() => {
					isloading.value = false;
					currentStep.value += 1;
					uploadProcessFun();
				}, 500);
			} else {
				// 如果uploadStatus为false，保持进度为98%
				loadingProgress.value = 98;
			}
		}
	};

	// 开始动画
	requestAnimationFrame(updateProgress);
}
onMounted(() => {
	pageType.value = router.currentRoute.value.query.type;
	setuploaded()
})
var showstep = computed(() => {
	return stepsArr.value.find(item => item.type == pageType.value)
})
const stepsArr = ref([
	{
		type: "courseware", loadingIcon: loadingicon, loadTitle: "正在上传课件材料，请稍等…", title: "课件学习助手",
		steps: [
			{
				stepNumbers: 1,
				number: '01',
				title: '课件材料上传',
				icon: shangchuan,
				iconGray: shangchuanhui,
				status: '已完成上传',
				nextStepNumbers: 2,
				stepName: ""
			},
			{
				stepNumbers: 2,
				number: '02',
				title: '材料格式转换',
				icon: zhuanhuan,
				iconGray: zhuanhuanhui,
				status: '已完成 Markdown 格式转换',
				nextStepNumbers: 3,
				stepName: "toPdfCodeNode"
			},
			{
				stepNumbers: 3,
				number: '03',
				title: '材料内容识别理解',
				icon: lijie,
				iconGray: lijiehui,
				status: '已完成材料内容的学习阅读',
				nextStepNumbers: 4,
				stepName: "extractFilesNode"
			},
			{
				stepNumbers: 4,
				number: '04',
				title: '知识点解析抽取',
				icon: chouqu,
				iconGray: chouquhui,
				status: '已完成知识点的提炼与解析',
				nextStepNumbers: 5,
				stepName: "knowledgePointExtNode"
			},
			{
				stepNumbers: 5,
				number: '05',
				title: '知识点标记展示',
				icon: zhanshi,
				iconGray: zhanshihui,
				status: '已完成在材料中的知识点标记',
				nextStepNumbers: 6,
				stepName: "annotationCodeNode"
			}]
	},
	{
		type: "videoLearning", loadingIcon: videoLoadingIcon, loadTitle: "正在上传视频材料，请稍等…", title: "视频学习助手",
		steps: [
			{
				stepNumbers: 1,
				number: '01',
				title: '视频材料上传',
				icon: videoshangchuan,
				iconGray: videoshangchuanhui,
				status: '已完成上传',
				nextStepNumbers: 2,
				stepName: ""
			},
			{
				stepNumbers: 2,
				number: '02',
				title: '视频画面分析',
				icon: videofenxi,
				iconGray: videofenxihui,
				status: '已完成视频关键帧画面的抽取',
				nextStepNumbers: 3,
				stepName: "extAndSumVideoContentNode"
			},
			{
				stepNumbers: 3,
				number: '03',
				title: '音频提取',
				icon: videozhuanyi,
				iconGray: videozhuanyihui,
				status: '已完成音频的提取与文字转换',
				nextStepNumbers: 4,
				stepName: "videoToAudioCodeNode"
			},
			{
				stepNumbers: 4,
				number: '04',
				title: '音频转译',
				icon: videolijie,
				iconGray: videolijiehui,
				status: '已完成音频转译',
				nextStepNumbers: 5,
				stepName: "extractAudioTextNode"
			},
			{
				stepNumbers: 5,
				number: '05',
				title: '知识点解析抽取',
				icon: videochouqu,
				iconGray: videochouquhui,
				status: '已完成知识点的提炼与解析',
				nextStepNumbers: 6,
				stepName: "knowledgePointExtNode"
			},
			{
				stepNumbers: 6,
				number: '06',
				title: '知识点打点标记',
				icon: videobiaoji,
				iconGray: videobiaojihui,
				status: '已完成在视频中的知识打点',
				nextStepNumbers: 7,
				stepName: "videoTaggingNode"
			},
		]
	}
])
let controller = new AbortController();

const uploadProcessFun = async () => {
	controller = new AbortController();
	const uploadApiOnce = async () => {
		await uploadApiProcess({
			signal: controller.signal,
			fileUrl: fileUrl.value,
			fileId: fileId.value,
			//这里缺少视频的code传参 暂时先用个假的
			code: pageType.value == 'courseware' ? 'CoursewareLearning' : 'VideoLearning',
			onDownloadProgress: ({event}) => {
				const xhr = event.target;
				const {responseText} = xhr;
				// 按行分割响应文本
				const lines = responseText
					.split("\n")
					.filter((line) => line.trim() !== "");
				// 处理每一行数据
				for (const line of lines) {
					const trimmedLine = line.replace(/^data: /, "").trim();
					try {
						const data = JSON.parse(trimmedLine?.substring(5));

						// 直接使用当前响应文本,不进行累加
						const deltaContent = JSON.parse(data.info);


						if (data.event === "workflow_started") {

						} else if (data.event === "workflow_end") {
							jumpPage()
						}
						if (data.event === "node_finished") {
							if (deltaContent.node.nodeId == 'toPdfCodeNode') {
								let rowdata = JSON.parse(deltaContent.data.rawOutput);
								pdfUrl.value = rowdata.fileUrl;
							}
							if (deltaContent.node.nodeStatus == '1') {
								message.error('解析失败，请重新上传')
								processError.value = true;
							}
							let item = showstep.value.steps.find(item => item.stepName == deltaContent.node.nodeId);
							if (item && deltaContent.node.nodeStatus == '0') {
								currentStep.value = item.nextStepNumbers;
							}
						}
					} catch (error) {
					}
				}
			},
		});
	};

	await uploadApiOnce();
};
</script>
<style scoped lang="less" >
.documentbg{
	height: calc(100vh - 124px);
}
.loadingPagebg {
	background-image: linear-gradient(180deg, #FFFFFF 0%, #F6F8FE 100%);
	padding-bottom: 24px;
	padding-top: 30px;
	.headers {
		margin-bottom: 24px;
		height: 40px;

		.left {
			color: #323233;
			font-size: 20px;
			font-weight: 500;
			line-height: 0;
			letter-spacing: 0;
			line-height: 40px;
			display: flex;
			align-items: center;
			margin-left: 24px;

			.gohome {
				width: 40px;
				height: 40px;
				background: #FAFBFF;
				border: 1px solid #E9ECF3;
				border-radius: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 11px;
				cursor: pointer;

				img {
					width: 20px;
					height: 20px;
				}
			}

			span {
				color: #909399;
				margin-left: 8px;

			}
		}

	}

	.loading {
		width: 100%;
		height: 100%;
		background: url('@/assets/loadingbg.png');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		padding-top: 197px;
		margin: 0 auto;
		padding-bottom: 21px;

		.loadingicon {
			width: 172px;
			height: 172px;
			margin: 0 auto;
		}

	}

	.progressbox {
		width: 322.59px;
		height: 14px;
		border-radius: 8px;
		margin: 0 auto;
		margin-top: 21px;
	}

	.loadingtitle {
		height: 22px;
		font-family: PingFangSC-Regular;
		font-weight: 400;
		font-size: 16px;
		color: #606266;
		letter-spacing: 0;
		text-align: center;
		margin-top: 20px;
	}

	.leftbox {
		background: #FFFFFF;
		box-shadow: 0 0 10px 0 #00000021;
		border-radius: 4px;
	}

	.rightbox {
		padding-left: 24px;
		padding-right: 30px;
		overflow-y: auto;

		.steps-container {
			height: 100%;
			width: 100%;

			.steps-title {
				// width: 522px;
			width: 100%;
				height: 50px;
				background: #F1F4FE;
				border-radius: 6px;
				font-family: PingFangSC-Medium;
				font-weight: 500;
				font-size: 16px;
				color: #2F3033;
				letter-spacing: 0;
				text-align: center;
				margin-bottom: 20px;
				line-height: 50px;
			}

			.steps-content {
				position: relative;

				.step-item {
					position: relative;
					display: flex;
					align-items: flex-start;
					margin-bottom: 40px;

					&:last-child {
						margin-bottom: 0;
					}

					.step-circlebox {
						width: 38px;
						height: 38px;
						margin-right: 13px;
						display: flex;
						align-items: center;
						justify-content: center;

						.step-circle {
							width: 30px;
							height: 30px;
							border: 1px solid #C8C8C8;
							font-family: DINAlternate-Bold;
							font-weight: 700;
							font-size: 16px;
							color: #C8C8C8;
							letter-spacing: 0;
							text-align: center;
							line-height: 30px;
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							flex-shrink: 0;
							z-index: 3;

							&.active {
								border: 0px;
								background-image: linear-gradient(180deg, #7E9DF8 0%, #4374F6 100%);
								color: #FFFFFF;
							}

							&.completed {
								border: 0px;
								background-image: linear-gradient(180deg, #7E9DF8 0%, #4374F6 100%);
								color: #FFFFFF;
							}
						}

					}

					.xuanzhuan {
						background: url('@/assets/loadingPage/xuanzhuan.png');
						background-size: 100%;
						background-repeat: no-repeat;
					}


					.step-box {
						// width: 469px;
						flex: 1;
						height: 128px;
						background: #F4F6FE;
						display: flex;
						align-items: center;
						position: relative;
						z-index: 2;
						padding-left: 30px;
						padding-right: 34px;
						border-radius: 10px;

						&.completed {
							background: #F8F9FD;
							background-image: linear-gradient(180deg, #ffffff80 0%, #b4cfff61 100%);
							border: 1px solid #B8CFFD;

							.step-content {
								.step-text {
									color: #2F3033;
								}
							}
						}

						&.active {
							border: 1px solid #B8CFFD;

							.step-content {
								.step-text {
									color: #125EFF;
								}
							}
						}

						.step-icon {
							display: flex;
							align-items: center;
							justify-content: center;
							flex-shrink: 0;
							margin-right: 28px;

							img {
								width: 60px;
								height: 60px;
							}
						}

						.step-content {
							flex: 1;

							.step-text {
								font-family: PingFangSC-Medium;
								font-weight: 500;
								font-size: 16px;
								letter-spacing: 0;
								line-height: 22px;
								color: #A6AAB2;
							}

							.step-status {
								margin-top: 8px;
								display: flex;
								align-items: center;
								justify-content: space-between;

								.status-text {
									font-family: PingFangSC-Regular;
									font-weight: 400;
									font-size: 14px;
									color: #00AD76;
									letter-spacing: 0;
									line-height: 20px;
								}

							}
						}

						.check-icon {
							width: 28px;
							height: 28px;
							flex-shrink: 0;
						}
					}

					// 连接线
					&::after {
						content: '';
						position: absolute;
						left: 17px;
						top: 40px;
						bottom: -24px;
						// width: 2px;
						border-left: 1px dashed #C8C8C8;
						// background: #E4E7ED;
						z-index: 1;
					}

					&:last-child::after {
						display: none;
					}

					&.completed::after {
						border-left: 1px dashed #125EFF;

					}
				}
			}
		}
	}
}

</style>
