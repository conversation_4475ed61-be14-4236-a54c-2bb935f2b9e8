<template>
  <!-- 右侧滑出变量管理面板 -->
  <div
    class="variable-panel-overlay"
    v-show="visible"
    @click="handleOverlayClick"
  >
    <div class="variable-panel" :class="{ 'panel-open': visible }" @click.stop>
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-content">
          <div class="panel-info">
            <div class="panel-name">变量管理</div>
            <div class="panel-hint">变量修改后需要保存流程到后端</div>
          </div>
          <div class="header-actions">
            <button class="close-btn" @click="handleClose" title="关闭">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 面板内容 -->
      <div class="panel-content">
        <!-- 搜索和过滤区域 -->
        <div class="search-filter-section">
          <div class="search-row">
            <n-input
              v-model:value="searchKeyword"
              placeholder="搜索变量名或描述..."
              clearable
              class="search-input"
            >
              <template #prefix>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
              </template>
            </n-input>
            <n-select
              v-model:value="selectedDataType"
              placeholder="数据类型"
              clearable
              :options="dataTypeOptions"
              class="type-filter"
            />
          </div>
          <div class="action-row">
            <n-space>
              <n-button @click="importVariables" size="small">
                导入变量
              </n-button>
              <n-button @click="exportVariables()" size="small">
                导出全部
              </n-button>
              <n-button @click="clearSearch" size="small" quaternary>
                清空筛选
              </n-button>
            </n-space>
          </div>
        </div>

        <n-scrollbar style="max-height: calc(100vh - 200px);">
          <!-- 标签页 -->
          <n-tabs v-model:value="activeTab" type="line" class="variable-tabs">
            <!-- 环境变量标签页 -->
            <n-tab-pane name="envVariable" tab="环境变量">
              <div class="tab-content">
                <div class="tab-description">
                  <div class="description-icon">🔒</div>
                  <div class="description-text">
                    <div class="description-title">环境变量</div>
                    <div class="description-detail">
                      系统常量，在流程执行过程中只读，通常用于存储API密钥、服务端点等配置参数。
                      <strong>变量将随流程一起保存到后端。</strong>
                    </div>
                  </div>
                </div>

                <div class="add-variable-section">
                  <n-button @click="addEnvironmentVariable" dashed type="primary">
                    + 添加环境变量
                  </n-button>
                  <n-button @click="exportVariables('envVariable')" size="small" quaternary>
                    导出环境变量
                  </n-button>
                </div>

                <!-- 环境变量列表 -->
                <div class="variable-list">
                  <div v-if="environmentVariables.length === 0" class="empty-state">
                    <div class="empty-icon">📝</div>
                    <div class="empty-text">暂无环境变量</div>
                    <div class="empty-hint">点击上方按钮添加第一个环境变量</div>
                  </div>
                  <div
                    v-for="variable in environmentVariables"
                    :key="variable.id"
                    class="variable-card"
                  >
                    <div class="variable-header">
                      <div class="variable-info">
                        <div class="variable-name">{{ variable.name }}</div>
                        <div class="variable-meta">
                          <n-tag size="small" :type="getDataTypeColor(variable.dataType)">
                            {{ getDataTypeLabel(variable.dataType) }}
                          </n-tag>
                          <span class="readonly-badge">只读</span>
                        </div>
                      </div>
                      <div class="variable-actions">
                        <button class="action-btn edit-btn" @click="editVariable(variable)" title="编辑">
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m18 2 4 4-14 14H4v-4L18 2z"></path>
                          </svg>
                        </button>
                        <n-popconfirm @positive-click="removeVariable(variable)">
                          <template #trigger>
                            <button class="action-btn delete-btn" title="删除">
                              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"></polyline>
                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2,2h4a2,2 0 0,1,2,2v2"></path>
                              </svg>
                            </button>
                          </template>
                          确定要删除变量 "{{ variable.name }}" 吗？
                        </n-popconfirm>
                      </div>
                    </div>
                    <div v-if="variable.description" class="variable-description">
                      {{ variable.description }}
                    </div>
                    <div class="variable-value">
                      <span class="value-label">默认值:</span>
                      <span class="value-content">{{ formatVariableValue(variable.defaultValue) }}</span>
                    </div>
                    <div v-if="variable.tags && variable.tags.length > 0" class="variable-tags">
                      <n-tag v-for="tag in variable.tags" :key="tag" size="small" class="tag-item">
                        {{ tag }}
                      </n-tag>
                    </div>
                  </div>
                </div>
              </div>
            </n-tab-pane>

            <!-- 用户变量标签页 -->
            <n-tab-pane name="user" tab="用户变量">
              <div class="tab-content">
                <div class="tab-description">
                  <div class="description-icon">🔄</div>
                  <div class="description-text">
                    <div class="description-title">用户变量</div>
                    <div class="description-detail">
                      全局变量，在整个流程的所有节点间传递和共享，支持读取和修改。
                      <strong>变量将随流程一起保存到后端。</strong>
                    </div>
                  </div>
                </div>

                <div class="add-variable-section">
                  <n-button @click="addUserVariable" dashed type="primary">
                    + 添加用户变量
                  </n-button>
                  <n-button @click="exportVariables('user')" size="small" quaternary>
                    导出用户变量
                  </n-button>
                </div>

                <!-- 用户变量列表 -->
                <div class="variable-list">
                  <div v-if="userVariables.length === 0" class="empty-state">
                    <div class="empty-icon">📝</div>
                    <div class="empty-text">暂无用户变量</div>
                    <div class="empty-hint">点击上方按钮添加第一个用户变量</div>
                  </div>
                  <div
                    v-for="variable in userVariables"
                    :key="variable.id"
                    class="variable-card"
                  >
                    <div class="variable-header">
                      <div class="variable-info">
                        <div class="variable-name">{{ variable.name }}</div>
                        <div class="variable-meta">
                          <n-tag size="small" :type="getDataTypeColor(variable.dataType)">
                            {{ getDataTypeLabel(variable.dataType) }}
                          </n-tag>
                          <span class="readwrite-badge">可读写</span>
                        </div>
                      </div>
                      <div class="variable-actions">
                        <button class="action-btn edit-btn" @click="editVariable(variable)" title="编辑">
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m18 2 4 4-14 14H4v-4L18 2z"></path>
                          </svg>
                        </button>
                        <n-popconfirm @positive-click="removeVariable(variable)">
                          <template #trigger>
                            <button class="action-btn delete-btn" title="删除">
                              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"></polyline>
                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2,2h4a2,2 0 0,1,2,2v2"></path>
                              </svg>
                            </button>
                          </template>
                          确定要删除变量 "{{ variable.name }}" 吗？
                        </n-popconfirm>
                      </div>
                    </div>
                    <div v-if="variable.description" class="variable-description">
                      {{ variable.description }}
                    </div>
                    <div class="variable-value">
                      <span class="value-label">默认值:</span>
                      <span class="value-content">{{ formatVariableValue(variable.defaultValue) }}</span>
                    </div>
                    <div v-if="variable.tags && variable.tags.length > 0" class="variable-tags">
                      <n-tag v-for="tag in variable.tags" :key="tag" size="small" class="tag-item">
                        {{ tag }}
                      </n-tag>
                    </div>
                  </div>
                </div>
              </div>
            </n-tab-pane>
          </n-tabs>
        </n-scrollbar>
      </div>
    </div>
  </div>

  <!-- 变量编辑弹窗 -->
  <NModal v-model:show="showVariableModal">
    <NCard
      style="width: 600px"
      :title="isEditMode ? '编辑变量' : '新增变量'"
      :bordered="false"
      size="huge"
    >
      <NForm
        ref="variableFormRef"
        :model="variableForm"
        :rules="variableRules"
        label-placement="left"
        label-width="100px"
      >
        <NFormItem label="变量名" path="name">
          <NInput
            v-model:value="variableForm.name"
            placeholder="请输入变量名"
          />
        </NFormItem>
        <NFormItem label="数据类型" path="dataType">
          <NSelect
            v-model:value="variableForm.dataType"
            :options="dataTypeOptions"
            placeholder="请选择数据类型"
          />
        </NFormItem>
        <NFormItem label="描述" path="description">
          <NInput
            v-model:value="variableForm.description"
            type="textarea"
            placeholder="请输入变量描述（可选）"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="默认值" path="defaultValue">
          <NInput
            v-model:value="variableForm.defaultValue"
            placeholder="请输入默认值"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <div class="modal-footer">
          <NButton @click="cancelVariableEdit">取消</NButton>
          <NButton type="primary" @click="saveVariable">
            {{ isEditMode ? '保存' : '新增' }}
          </NButton>
        </div>
      </template>
    </NCard>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  NButton,
  NInput,
  NSelect,
  NScrollbar,
  NModal,
  NCard,
  NForm,
  NFormItem,
  NTabs,
  NTabPane,
  NSpace,
  NTag,
  NPopconfirm,
  useMessage,
} from "naive-ui";
import { useOrchestrationStore } from '@/store'
import type { FlowVariable, VariableRequest } from '@/types/backend'

const props = defineProps<{
  show: boolean;
}>();

const emit = defineEmits<{
  "update:show": [value: boolean];
}>();

const message = useMessage();
const orchestrationStore = useOrchestrationStore();

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 当前活动标签页
const activeTab = ref('envVariable');

// 搜索和过滤
const searchKeyword = ref('');
const selectedDataType = ref<string>('');

// 变量数据类型选项
const dataTypeOptions = [
  { label: "字符串", value: "string" },
  { label: "数字", value: "number" },
  { label: "布尔值", value: "boolean" },
  { label: "数组", value: "array" },
  { label: "对象", value: "object" }
];

// 获取环境变量列表
const environmentVariables = computed(() => {
  return orchestrationStore.getVariablesByType('envVariable').filter(variable => {
    const matchesSearch = !searchKeyword.value ||
      variable.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      (variable.description && variable.description.toLowerCase().includes(searchKeyword.value.toLowerCase()));

    const matchesType = !selectedDataType.value || variable.dataType === selectedDataType.value;

    return matchesSearch && matchesType;
  });
});

// 获取用户变量列表
const userVariables = computed(() => {
  return orchestrationStore.getVariablesByType('user').filter(variable => {
    const matchesSearch = !searchKeyword.value ||
      variable.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      (variable.description && variable.description.toLowerCase().includes(searchKeyword.value.toLowerCase()));

    const matchesType = !selectedDataType.value || variable.dataType === selectedDataType.value;

    return matchesSearch && matchesType;
  });
});

// 变量编辑弹窗相关
const showVariableModal = ref(false);
const isEditMode = ref(false);
const editingVariableId = ref<string>('');
const currentVariableType = ref<'envVariable' | 'user'>('envVariable');
const variableFormRef = ref();

// 变量表单数据
const variableForm = ref<VariableRequest>({
  name: "",
  type: "envVariable",
  dataType: "string",
  description: "",
  defaultValue: "",
  tags: []
});

// 表单验证规则
const variableRules = {
  name: {
    required: true,
    message: "请输入变量名",
    trigger: "blur"
  },
  dataType: {
    required: true,
    message: "请选择数据类型",
    trigger: "change"
  }
};

// 获取数据类型标签颜色
const getDataTypeColor = (dataType: string) => {
  const colorMap: Record<string, string> = {
    string: 'info',
    number: 'success',
    boolean: 'warning',
    array: 'error',
    object: 'default'
  };
  return colorMap[dataType] || 'default';
};

// 获取数据类型标签文本
const getDataTypeLabel = (dataType: string) => {
  const labelMap: Record<string, string> = {
    string: '字符串',
    number: '数字',
    boolean: '布尔值',
    array: '数组',
    object: '对象'
  };
  return labelMap[dataType] || dataType;
};

// 格式化变量值显示
const formatVariableValue = (value: any) => {
  if (value === null || value === undefined) return '未设置';
  if (typeof value === 'string') return value || '空字符串';
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

// 处理遮罩层点击
const handleOverlayClick = () => {
  handleClose();
};

// 关闭面板
const handleClose = () => {
  visible.value = false;
};

// 添加环境变量
const addEnvironmentVariable = () => {
  currentVariableType.value = 'envVariable';
  isEditMode.value = false;
  variableForm.value = {
    name: "",
    type: "envVariable",
    dataType: "string",
    description: "",
    defaultValue: "",
    tags: []
  };
  showVariableModal.value = true;
};

// 添加用户变量
const addUserVariable = () => {
  currentVariableType.value = 'user';
  isEditMode.value = false;
  variableForm.value = {
    name: "",
    type: "user",
    dataType: "string",
    description: "",
    defaultValue: "",
    tags: []
  };
  showVariableModal.value = true;
};

// 编辑变量
const editVariable = (variable: FlowVariable) => {
  currentVariableType.value = variable.type;
  isEditMode.value = true;
  editingVariableId.value = variable.id;
  variableForm.value = {
    name: variable.name,
    type: variable.type,
    dataType: variable.dataType,
    description: variable.description || "",
    defaultValue: variable.defaultValue || "",
    tags: variable.tags || []
  };
  showVariableModal.value = true;
};

// 删除变量
const removeVariable = async (variable: FlowVariable) => {
  try {
    orchestrationStore.deleteVariable(variable.id);
    message.success("变量删除成功");
  } catch (error) {
    message.error(error instanceof Error ? error.message : "删除失败");
  }
};

// 保存变量
const saveVariable = async () => {
  try {
    await variableFormRef.value?.validate();

    if (isEditMode.value) {
      // 编辑模式
      orchestrationStore.updateVariable(editingVariableId.value, {
        name: variableForm.value.name,
        dataType: variableForm.value.dataType,
        description: variableForm.value.description,
        defaultValue: variableForm.value.defaultValue,
        tags: variableForm.value.tags
      });
      message.success("变量编辑成功");
    } else {
      // 新增模式
      orchestrationStore.addVariable({
        name: variableForm.value.name,
        type: variableForm.value.type,
        dataType: variableForm.value.dataType,
        description: variableForm.value.description,
        defaultValue: variableForm.value.defaultValue,
        readonly: variableForm.value.type === 'envVariable',
        tags: variableForm.value.tags
      });
      message.success("变量新增成功");
    }

    showVariableModal.value = false;
  } catch (error) {
    message.error(error instanceof Error ? error.message : "操作失败");
  }
};

// 取消编辑
const cancelVariableEdit = () => {
  showVariableModal.value = false;
};

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = '';
  selectedDataType.value = '';
};

// 导出变量
const exportVariables = (type?: 'envVariable' | 'user') => {
  try {
    const exportData = orchestrationStore.exportVariables(type);
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `variables_${type || 'all'}_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    message.success("变量导出成功");
  } catch (error) {
    message.error("导出失败");
  }
};

// 导入变量
const importVariables = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          if (data.variables && Array.isArray(data.variables)) {
            orchestrationStore.importVariables(data.variables, false);
            message.success("变量导入成功");
          } else {
            message.error("文件格式不正确");
          }
        } catch (error) {
          message.error("文件解析失败");
        }
      };
      reader.readAsText(file);
    }
  };
  input.click();
};
</script>

<script lang="ts">
export default {
  name: 'VariableManagementPanel'
}
</script>

<style scoped lang="less">
.variable-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
}

.variable-panel {
  width: 480px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;

  &.panel-open {
    transform: translateX(0);
  }
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .panel-info {
    .panel-name {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 4px;
    }

    .panel-hint {
      font-size: 12px;
      color: #6b7280;
      background: #f3f4f6;
      padding: 4px 8px;
      border-radius: 4px;
      border-left: 3px solid #3b82f6;
    }
  }

  .close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #6b7280;
    transition: all 0.2s ease;

    &:hover {
      background: #f3f4f6;
      color: #374151;
    }
  }
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-filter-section {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .search-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    .search-input {
      flex: 1;
    }

    .type-filter {
      width: 120px;
    }
  }

  .action-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.variable-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;

  :deep(.n-tabs-content) {
    flex: 1;
    overflow: hidden;
  }

  :deep(.n-tab-pane) {
    height: 100%;
    overflow: hidden;
  }
}

.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 24px;
}

.tab-description {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  margin: 16px 0;

  .description-icon {
    font-size: 20px;
    margin-top: 2px;
  }

  .description-text {
    flex: 1;

    .description-title {
      font-size: 14px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 4px;
    }

    .description-detail {
      font-size: 12px;
      color: #6b7280;
      line-height: 1.5;
    }
  }
}

.add-variable-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.variable-list {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .empty-hint {
    font-size: 14px;
  }
}

.variable-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.variable-info {
  flex: 1;

  .variable-name {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 6px;
  }

  .variable-meta {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.readonly-badge, .readwrite-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.readonly-badge {
  background: #fef3c7;
  color: #92400e;
}

.readwrite-badge {
  background: #d1fae5;
  color: #065f46;
}

.variable-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;

  &:hover {
    background: #f3f4f6;
  }

  &.edit-btn:hover {
    color: #3b82f6;
  }

  &.delete-btn:hover {
    color: #ef4444;
  }
}

.variable-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  line-height: 1.5;
}

.variable-value {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  .value-label {
    font-size: 12px;
    color: #9ca3af;
    font-weight: 500;
  }

  .value-content {
    font-size: 12px;
    color: #374151;
    background: #f9fafb;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
}

.variable-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;

  .tag-item {
    font-size: 11px;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
