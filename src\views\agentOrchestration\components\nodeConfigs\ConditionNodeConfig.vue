<template>
  <div class="condition-node-config">
    <!-- 条件分支配置 -->
    <div
      v-for="(item, index) in formData.config.conditionBranchArr"
      :key="index"
    >
      <!-- 分支标题 -->
      <n-form-item label-placement="left">
        <div class="flex justify-between items-center w-full">
          <div class="flex items-center">
            <span class="text-[#000000] text-[16px]">{{
              index == 0
                ? "如果"
                : index == formData.config.conditionBranchArr.length - 1
                ? "否则"
                : "否则如果"
            }}</span>
            <span class="condition-branch-label">
              {{ item.name }}
            </span>
          </div>
          <div
            v-if="
              formData.config.conditionBranchArr.length > 2 &&
              index != formData.config.conditionBranchArr.length - 1
            "
            class="ml-[10px]"
          >
            <img
              @click="removeElseBranch(index)"
              class="w-[16px] cursor-pointer"
              src="@/assets/agentOrchestration/delIcon2.png"
              title="删除"
            />
          </div>
        </div>
      </n-form-item>

      <!-- 条件配置区域 -->
      <div class="condition-section">
        <!-- 条件组容器 -->
        <div
          class="condition-group"
          v-if="item.conditionArr && item.conditionArr.length > 0"
        >
          <!-- 左侧花括号和逻辑操作符 -->
          <div
            class="condition-bracket-container"
            v-if="item.conditionArr.length > 1"
          >
            <div
              class="condition-bracket"
              :style="{
                height:
                  (item.conditionArr.length - 2) * 116 +
                  (item.conditionArr.length - 1) * 12 +
                  116 +
                  'px',
              }"
            >
              <div class="bracket-top"></div>
              <div class="bracket-middle">
                <n-select
                  v-model:value="item.conditionLogic"
                  :options="logicOperatorOptions"
                  placeholder="且"
                  size="small"
                  class="group-logic-selector"
                  @update:value="handleLogicOperatorChange"
                />
              </div>
              <div class="bracket-bottom"></div>
            </div>
          </div>

          <!-- 条件列表 -->
          <div class="condition-list">
            <div
              v-for="(condition, childrenindex) in item.conditionArr"
              :key="childrenindex"
              class="condition-item-wrapper"
            >
              <!-- 条件配置行 -->
              <div class="flex items-center">
                <div class="flex-1">
                  <!-- 第一行：变量选择和操作符 -->
                  <div class="flex h-[38px]">
                    <div class="flex-1">
                      <AggregationSelector
                        v-model="condition.variable"
                        :options="aggregationOptions"
                        placeholder="请选择变量"
                      />
                    </div>
                    <div class="w-[114px] ml-[12px]">
                      <n-select
                        v-model:value="condition.operator"
                        :options="conditionOperatorOptions"
                        placeholder="等于"
                        size="small"
                      />
                    </div>
                  </div>
                  
                  <!-- 第二行：字段类型和值 -->
                  <div class="flex mt-[12px] h-[38px]">
                    <div class="w-[114px]">
                      <n-select
                        v-model:value="condition.field"
                        :options="variableOptions"
                        placeholder="0"
                        size="small"
                      />
                    </div>
                    <div class="flex-1 ml-[12px] h-[38px] outputrow">
                      <AggregationSelector
                        v-if="condition.field == '0'"
                        v-model="condition.value"
                        :options="aggregationOptions"
                        placeholder="请选择变量"
                      />
                      <n-input
                        v-else
                        v-model:value="condition.value"
                        type="text"
                        placeholder="变量值"
                      />
                    </div>
                  </div>
                </div>
                
                <!-- 删除按钮 -->
                <div
                  v-if="item.conditionArr.length > 1"
                  class="ml-[10px]"
                >
                  <img
                    @click="removeCondition(index, childrenindex)"
                    class="action-icon"
                    src="@/assets/agentOrchestration/delIcon2.png"
                    title="删除"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加条件按钮 -->
        <div
          class="add-condition-wrapper"
          v-if="index != formData.config.conditionBranchArr.length - 1"
        >
          <n-button
            @click="addCondition(index)"
            dashed
            class="add-condition-button"
          >
            + 添加条件
          </n-button>
        </div>
      </div>
      
      <!-- 添加否则分支按钮 -->
      <div
        class="add-else-branch-wrapper"
        v-if="index == formData.config.conditionBranchArr.length - 1"
      >
        <n-button
          @click="addElseBranch"
          type="info"
          color="#125EFF"
          class="add-else-branch-button"
        >
          + 添加分支
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { NFormItem, NSelect, NInput, NButton } from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import { useNodeConfig } from './composables/useNodeConfig';
import type { NodeConfigProps, NodeConfigEvents, ConditionBranch, ConditionConfig } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const { 
  variableOptions,
  conditionOperatorOptions,
  logicOperatorOptions,
  aggregationOptions, 
  updateAggregationOptions, 
  handleAggregationChange 
} = useNodeConfig(props);

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'condition') {
      const config = newNode.data.config;
      
      // 为条件节点初始化默认条件
      if (!config.conditionBranchArr) {
        config.conditionBranchArr = [
          {
            name: "分支1",
            disabled: false,
            conditionArr: [
              { variable: "", operator: "等于", field: "0", value: "" },
            ],
            conditionLogic: "",
          },
          {
            name: "分支2",
            disabled: true,
            conditionArr: [],
            conditionLogic: "",
          },
        ];
        config.conditionLogic = "且";
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听显示状态，更新聚合选项
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.node) {
      updateAggregationOptions();
    }
  },
  { immediate: true }
);

// 添加条件
const addCondition = (index: number) => {
  props.formData.config.conditionBranchArr[index].conditionArr.push({
    variable: "",
    operator: "等于",
    field: "0",
    value: "",
  });
};

// 删除否则分支
const removeElseBranch = (index: number) => {
  if (props.formData.config.conditionBranchArr) {
    props.formData.config.conditionBranchArr.splice(index, 1);
  }
};

// 删除条件
const removeCondition = (index: number, childrenindex: number) => {
  if (props.formData.config.conditionBranchArr) {
    props.formData.config.conditionBranchArr[index].conditionArr.splice(
      childrenindex,
      1
    );
  }
};

// 处理逻辑操作符变更
const handleLogicOperatorChange = (value: string) => {
  console.log("主条件逻辑操作符变更为:", value);
};

// 添加否则分支
const addElseBranch = () => {
  props.formData.config.conditionBranchArr.splice(
    props.formData.config.conditionBranchArr.length - 1,
    0,
    {
      name: "分支" + props.formData.config.conditionBranchArr.length,
      disabled: false,
      conditionArr: [{ variable: "", operator: "等于", field: "0", value: "" }],
      conditionLogic: "",
    }
  );
};
</script>

<style scoped lang="less">
.condition-node-config {
  .condition-branch-label {
    margin-left: 8px;
    padding: 2px 8px;
    background: #f0f8ff;
    border-radius: 4px;
    font-size: 14px;
    color: #125eff;
  }

  .condition-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #fafbfc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  .condition-group {
    display: flex;
    align-items: flex-start;
    gap: 16px;
  }

  .condition-bracket-container {
    flex-shrink: 0;
    width: 60px;
    display: flex;
    justify-content: center;
    margin-top: 19px;
  }

  .condition-bracket {
    position: relative;
    width: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .bracket-top,
  .bracket-bottom {
    width: 20px;
    height: 20px;
    border: 2px solid #125eff;
    border-radius: 4px 0 0 4px;
  }

  .bracket-top {
    border-bottom: none;
    border-radius: 4px 0 0 0;
  }

  .bracket-bottom {
    border-top: none;
    border-radius: 0 0 0 4px;
  }

  .bracket-middle {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-left: 2px solid #125eff;
    padding: 8px 0;
    min-height: 40px;
  }

  .group-logic-selector {
    width: 50px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    :deep(.n-base-selection) {
      border: 1px solid #125eff;
      background: white;
    }
  }

  .condition-list {
    flex: 1;
  }

  .condition-item-wrapper {
    margin-bottom: 12px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .outputrow {
    .n-input {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      :deep(.n-input__border) {
        display: none;
      }
    }

    .n-input__input-el {
      height: 100%;
      border: 0;
    }
  }

  .action-icon {
    width: 16px;
    height: 16px;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }

  .add-condition-wrapper {
    margin-top: 12px;
  }

  .add-condition-button {
    width: 100%;
    border: 1px dashed #125eff;
    color: #125eff;
    background: transparent;

    &:hover {
      background: #f0f8ff;
    }
  }

  .add-else-branch-wrapper {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }

  .add-else-branch-button {
    min-width: 120px;
  }

  :deep(.n-select) {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;

    .n-base-selection__border {
      display: none;
    }

    .n-base-selection {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      .n-base-selection-label {
        height: 100%;
        background: #f5f5f6;
        border-radius: 8px !important;
      }
    }
  }
}
</style>
