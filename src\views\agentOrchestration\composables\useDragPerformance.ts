import { ref, shallowRef, nextTick, onMounted, onUnmounted } from 'vue'
import type { Node, Edge } from '@vue-flow/core'

/**
 * Vue Flow 拖拽性能优化 Composable
 * 解决节点拖拽卡顿和连线跟随问题
 */
export function useDragPerformance(vueFlowInstance?: any) {
  // 拖拽状态管理
  const isDragging = ref(false)
  const dragStartTime = ref(0)
  const dragNodeId = ref<string | null>(null)
  
  // 性能监控
  const frameCount = ref(0)
  const lastFrameTime = ref(0)
  const fps = ref(60)
  
  // RAF ID 用于清理
  let rafId: number | null = null
  let performanceMonitorId: number | null = null
  let edgeUpdateId: number | null = null

  /**
   * 启动拖拽性能优化
   */
  const startDragOptimization = (nodeId: string) => {
    isDragging.value = true
    dragNodeId.value = nodeId
    dragStartTime.value = performance.now()

    // 启用硬件加速
    enableHardwareAcceleration(nodeId)

    // 开始性能监控
    startPerformanceMonitoring()

    // 启动连线实时更新
    startEdgeUpdateLoop(nodeId)

    console.log(`[DragPerformance] 开始拖拽优化: ${nodeId}`)
  }

  /**
   * 停止拖拽性能优化
   */
  const stopDragOptimization = () => {
    if (!isDragging.value) return

    const duration = performance.now() - dragStartTime.value
    console.log(`[DragPerformance] 拖拽完成，耗时: ${duration.toFixed(2)}ms, 平均FPS: ${fps.value}`)

    isDragging.value = false
    const nodeId = dragNodeId.value
    dragNodeId.value = null

    // 禁用硬件加速
    if (nodeId) {
      disableHardwareAcceleration(nodeId)
    }

    // 停止性能监控
    stopPerformanceMonitoring()

    // 停止连线更新循环
    stopEdgeUpdateLoop()
  }

  /**
   * 启用硬件加速
   */
  const enableHardwareAcceleration = (nodeId: string) => {
    nextTick(() => {
      const nodeElement = document.querySelector(`[data-id="${nodeId}"]`) as HTMLElement
      if (nodeElement) {
        nodeElement.style.willChange = 'transform'
        nodeElement.style.transform = nodeElement.style.transform || 'translate3d(0, 0, 0)'
        
        // 为连接的边也启用硬件加速
        const edges = document.querySelectorAll(`.vue-flow__edge[data-source="${nodeId}"], .vue-flow__edge[data-target="${nodeId}"]`)
        edges.forEach((edge: Element) => {
          const edgeElement = edge as HTMLElement
          edgeElement.style.willChange = 'auto'
        })
      }
    })
  }

  /**
   * 禁用硬件加速
   */
  const disableHardwareAcceleration = (nodeId: string) => {
    nextTick(() => {
      const nodeElement = document.querySelector(`[data-id="${nodeId}"]`) as HTMLElement
      if (nodeElement) {
        nodeElement.style.willChange = 'auto'
        
        // 恢复边的样式
        const edges = document.querySelectorAll(`.vue-flow__edge[data-source="${nodeId}"], .vue-flow__edge[data-target="${nodeId}"]`)
        edges.forEach((edge: Element) => {
          const edgeElement = edge as HTMLElement
          edgeElement.style.willChange = 'auto'
        })
      }
    })
  }

  /**
   * 开始性能监控
   */
  const startPerformanceMonitoring = () => {
    frameCount.value = 0
    lastFrameTime.value = performance.now()
    
    const monitorFrame = () => {
      if (!isDragging.value) return
      
      frameCount.value++
      const currentTime = performance.now()
      
      // 每秒计算一次FPS
      if (currentTime - lastFrameTime.value >= 1000) {
        fps.value = Math.round((frameCount.value * 1000) / (currentTime - lastFrameTime.value))
        frameCount.value = 0
        lastFrameTime.value = currentTime
      }
      
      performanceMonitorId = requestAnimationFrame(monitorFrame)
    }
    
    performanceMonitorId = requestAnimationFrame(monitorFrame)
  }

  /**
   * 停止性能监控
   */
  const stopPerformanceMonitoring = () => {
    if (performanceMonitorId) {
      cancelAnimationFrame(performanceMonitorId)
      performanceMonitorId = null
    }
  }

  /**
   * 启动连线实时更新循环
   */
  const startEdgeUpdateLoop = (nodeId: string) => {
    const updateEdges = () => {
      if (!isDragging.value || dragNodeId.value !== nodeId) {
        return
      }

      // 强制更新连接到该节点的所有边
      try {
        // 方法1: 触发Vue Flow的内部更新机制
        const vueFlowElement = document.querySelector('.vue-flow')
        if (vueFlowElement) {
          // 触发一个自定义事件来强制Vue Flow重新计算
          const updateEvent = new CustomEvent('vueflow:update-edges', {
            detail: { nodeId },
            bubbles: true
          })
          vueFlowElement.dispatchEvent(updateEvent)
        }

        // 方法2: 直接操作连线的DOM元素
        const connectedEdges = document.querySelectorAll(
          `.vue-flow__edge[data-source="${nodeId}"], .vue-flow__edge[data-target="${nodeId}"]`
        )

        connectedEdges.forEach((edgeElement) => {
          const pathElement = edgeElement.querySelector('.vue-flow__edge-path') as SVGPathElement
          if (pathElement) {
            // 强制触发路径重新计算
            const currentD = pathElement.getAttribute('d')
            if (currentD) {
              // 临时修改然后恢复，触发重绘
              pathElement.setAttribute('d', currentD + ' ')
              requestAnimationFrame(() => {
                pathElement.setAttribute('d', currentD)
              })
            }
          }
        })

      } catch (error) {
        console.warn('[EdgeUpdate] 连线更新失败:', error)
      }

      // 继续下一帧更新
      edgeUpdateId = requestAnimationFrame(updateEdges)
    }

    edgeUpdateId = requestAnimationFrame(updateEdges)
  }

  /**
   * 停止连线更新循环
   */
  const stopEdgeUpdateLoop = () => {
    if (edgeUpdateId) {
      cancelAnimationFrame(edgeUpdateId)
      edgeUpdateId = null
    }
  }

  /**
   * 优化节点更新
   * 使用RAF来批量处理节点位置更新
   */
  const optimizedNodeUpdate = (callback: () => void) => {
    if (rafId) {
      cancelAnimationFrame(rafId)
    }
    
    rafId = requestAnimationFrame(() => {
      callback()
      rafId = null
    })
  }

  /**
   * 节流函数 - 限制更新频率
   */
  const throttle = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0
    return (...args: Parameters<T>) => {
      const now = performance.now()
      if (now - lastCall >= delay) {
        lastCall = now
        func(...args)
      }
    }
  }

  /**
   * 防抖函数 - 延迟执行
   */
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: number | null = null
    return (...args: Parameters<T>) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = window.setTimeout(() => func(...args), delay)
    }
  }

  /**
   * 批量更新节点位置
   */
  const batchUpdateNodes = (nodes: Node[], callback?: () => void) => {
    optimizedNodeUpdate(() => {
      // 批量更新逻辑
      callback?.()
    })
  }

  /**
   * 优化边的重绘
   */
  const optimizeEdgeRendering = (edges: Edge[]) => {
    // 使用DocumentFragment来批量更新DOM
    const fragment = document.createDocumentFragment()
    
    edges.forEach(edge => {
      const edgeElement = document.querySelector(`[data-id="${edge.id}"]`)
      if (edgeElement) {
        // 优化边的渲染
        const clonedElement = edgeElement.cloneNode(true)
        fragment.appendChild(clonedElement)
      }
    })
  }

  // 清理函数
  onUnmounted(() => {
    if (rafId) {
      cancelAnimationFrame(rafId)
    }
    if (performanceMonitorId) {
      cancelAnimationFrame(performanceMonitorId)
    }
    if (edgeUpdateId) {
      cancelAnimationFrame(edgeUpdateId)
    }
  })

  return {
    // 状态
    isDragging,
    dragNodeId,
    fps,
    
    // 方法
    startDragOptimization,
    stopDragOptimization,
    optimizedNodeUpdate,
    throttle,
    debounce,
    batchUpdateNodes,
    optimizeEdgeRendering,
  }
}
