<script setup lang='ts'>
import { computed, defineEmits, onMounted, onUnmounted, ref, watch, h } from "vue";
import {
  NInput,
  NScrollbar,
  NPopover,
  NEllipsis,
  NTooltip,
  useMessage,
  useDialog,
} from "naive-ui";
import { useRoute,useRouter } from "vue-router";
import { SvgIcon } from "@/components/common";
import { useAppStore, useChatStore } from "@/store";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import {
  getHistory,
  delHistory,
  updateHistory,
} from "@/api/index";
import { infoStore } from "@/store/modules/info";
import delIcon from "@/assets/chat/delIcon.png";
import noTopUpIcon from "@/assets/chat/noTopUpIcon.png";
import topUpIcon from "@/assets/chat/topUpIcon.png";
import renameIcon from "@/assets/chat/renameIcon.png";
import rowtopupicon from "@/assets/chat/rowtopupicon.png";
import allhistoryicon from "@/assets/chat/allhistoryicon.png";
import icon1 from '@/assets/applicationPage/icon1.png'

const router = useRouter();
const chatStore = useChatStore();
const appStore = useAppStore();
const route = useRoute();
const message = useMessage();
const dialog = useDialog();
// 调整为从store获取
var dataSources = ref<any[]>([]);

// 获取折叠状态
const collapsed = computed(() => appStore.siderCollapsed);

const emit = defineEmits(["onSelect"]);
var isnewAddHistory = computed(() => chatStore.newAddHistory);
watch(isnewAddHistory, () => {
  console.log(isnewAddHistory);
  if (isnewAddHistory.value) {
    setTimeout(() => {
      getHistoryfun();
    }, 100);
  }
});

watch(
  () => route.fullPath,
  (newVal, oldVal) => {
    if (route.name != "tankChat") {
      dataSources.value.forEach((items: any) => {
        items.ischeck = false;
        items.showPopover = false;
        items.showActions = false; // 重置操作按钮显示状态
      });
    }else{
      dataSources.value.forEach((items: any) => {
      if(items.id==chatStore.active){
      items.ischeck = true;
      }
      items.showPopover = false;
      items.showActions = false; // 重置操作按钮显示状态
      });
    }
  },
  { immediate: true }
);

import { useToolsStore } from "@/store";
const ToolsStore = useToolsStore();
async function handleSelect(item: any) {
  dataSources.value.forEach((items: any) => {
    items.ischeck = false;
  });
  // 再将当前点击的项的 ischeck 设为 true
  item.ischeck = true;


    const toolitem = {
    name: item.agentName,
    title: item.openingWords,
    des: item.description,
    agentId: item.agentId,
    openingQuestionArr: [],
    icon: icon1,
    modelSessionId: item.modelId,
    modelTemp: item.modelTemp,
    maxLength: item.maxLength,
    promptTemplate: item.promptTemplate,
  }
  toolitem.openingQuestionArr = toolitem.openingQuestionArr.concat(JSON.parse(item.preQuestions || '[]'))
  ToolsStore.updateToolInfo(toolitem)
  chatStore.activeHistory(item.id);
  
  router.push({
    path: '/tankChat',
    query: {
      id: item.agentId,
      uuid: item.id,
    },
  })

}
function getHistoryfun() {
  getHistory({}).then((res: any) => {
    res.data.forEach((item: any) => {
      item.showPopover = false;
      item.isEdit = false;
      item.ischeck = false;
      item.showActions = false; // 新增：控制操作按钮显示
      if(item.id==chatStore.active){
      item.ischeck = true;
      }
      //图标后台说还没有  暂时先给个空表的
      item.icon = icon1;
    });
    dataSources.value = res.data;
    if (isnewAddHistory.value) {
      console.log(route);
      
      const firstTopUpZeroItem = dataSources.value.find(item => item.id == route.query.uuid);
    if (firstTopUpZeroItem && route.query.uuid) {
      firstTopUpZeroItem.ischeck = true;
      chatStore.setnewAddHistory(false);
    }
    }

    if (isnewAddHistory.value) {
      const firstTopUpZeroItem = dataSources.value.find(item => item.topUp == '0');
    if (firstTopUpZeroItem && route.name != "historyPage") {
      firstTopUpZeroItem.ischeck = true;
    }
      chatStore.setnewAddHistory(false);
    }
  });
}
// 点击外部关闭菜单
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement;
  if (!target.closest('.modern-history-item') && !target.closest('.modern-popover')) {
    dataSources.value.forEach((item: any) => {
      item.showPopover = false;
      item.showActions = false;
    });
  }
}

onMounted( () => {
  getHistoryfun();
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

function changeshowPopover(item: any) {
  console.log(item);

  dataSources.value.forEach((items: any, index: number) => {
    if (item.id != items.id) {
      items.showPopover = false;
      items.showActions = false; // 隐藏其他项的操作按钮
    } else {
      items.showPopover = !items.showPopover;
      items.showActions = items.showPopover; // 保持操作按钮显示状态与弹窗一致
    }
  });
}
function changeedit(item: any) {
  changeshowPopover(item);
  var updatetitle = "";
  dialog.warning({
    title: "编辑对话名称",
    content: () =>
      h(NInput, {
        "default-value": item.title,
        "on-update:value": (value) => {
          updatetitle = value;
        },
      }),
    positiveText: "确定",
    negativeText: "取消",
    draggable: true,
    positiveButtonProps: {
      class: "siderdelbtn",
    },
    onPositiveClick: () => {
      if (updatetitle) {
        updateHistory({ id: item.id, title: updatetitle }).then((res: any) => {
          getHistoryfun();
          if(route.name== "historyPage"){
            chatStore.setrefreshHistory(true);
          }
        });
      }
    },
    onNegativeClick: () => {},
  });
}
function delHistoryfun(item: any) {
  if (item.ischeck) {
    message.warning("不可删除选中的历史记录");
    changeshowPopover(item);
    return;
  }
  changeshowPopover(item);
  dialog.warning({
    title: "确定删除对话吗?",
    content: "删除后，聊天记录将不可恢复。",
    positiveText: "删除",
    negativeText: "取消",
    draggable: true,
    positiveButtonProps: {
      class: "siderdelbtn",
    },
    onPositiveClick: () => {
      delHistory({ id: item.id }).then((res: any) => {
        console.log(res);
        getHistoryfun();
        if(route.name== "historyPage"){
            chatStore.setrefreshHistory(true);
          }
      });
    },
    onNegativeClick: () => {},
  });
}

function setTopUpfun(item: any) {
  item.isEdit = false;
  updateHistory({ id: item.id, topUp: item.topUp == "0" ? "1" : "0" }).then(
    (res: any) => {
      console.log(res);
      getHistoryfun();
      if(route.name== "historyPage"){
            chatStore.setrefreshHistory(true);
          }
    }
  );
}
function jumlallhistory(){
  router.push('/historyPage')
}
</script>

<template>
  <!-- 展开状态 -->
  <div v-if="!collapsed">
    <NScrollbar class="px-4">
      <div class="flex flex-col text-sm bg-[#F9FBFF] parentbox">
        <template v-if="!dataSources.length">
          <div class="flex flex-col items-center mt-4 text-center">
            <SvgIcon icon="ri:inbox-line" class="mb-2 text-3xl" />
            <span>暂无数据</span>
          </div>
        </template>
        <template v-else>
       <div class="childrenbox">
        <div class="w-full bg-[#E2E2E2] h-[1px]" />
        <div class="allhistoryrow">
          <div class="section-title">历史会话</div>
          <div class="history-action-btn" @click="jumlallhistory">
            <img class="allhistoryicon" :src="allhistoryicon">
          </div>
        </div>
       </div>
        <div class="historybox">
          <div v-for="(item, index) of dataSources" :key="index" class="history-item-wrapper">
            <div
              class="modern-history-item"
              :class="{
                'history-item-active': item.ischeck,
                'history-item-pinned': item.topUp == '1'
              }"
              @click="handleSelect(item)"
              @mouseenter="item.showActions = true"
              @mouseleave="item.showActions = false"
            >
              <!-- 置顶标识 -->
              <div class="pin-indicator" v-if="item.topUp == '1'">
                <img class="pin-icon" :src="rowtopupicon" />
              </div>

              <!-- 智能体图标 -->
              <div class="agent-icon-wrapper">
                <img class="agent-icon" :src="item.icon" />
              </div>

              <!-- 会话内容 -->
              <div class="history-content">
                <n-ellipsis class="history-title">
                  {{ item.title }}
                </n-ellipsis>
              </div>

              <!-- 操作按钮区域 -->
              <div class="history-actions" :class="{ 'actions-visible': item.showActions || item.showPopover }">
                <div class="action-btn" @click.stop="changeshowPopover(item)">
                  <SvgIcon icon="ri:more-2-fill" class="action-icon" />
                </div>

                <!-- 操作菜单 -->
                <n-popover
                  placement="right"
                  trigger="manual"
                  :show="item.showPopover"
                  class="modern-popover"
                >
                  <template #trigger>
                    <div></div>
                  </template>
                  <div class="modern-menu">
                    <div
                      class="menu-item"
                      @click="setTopUpfun(item)"
                      v-if="item.topUp == 0"
                    >
                      <img class="menu-icon" :src="topUpIcon" />
                      <span>置顶</span>
                    </div>
                    <div
                      class="menu-item"
                      @click="setTopUpfun(item)"
                      v-else
                    >
                      <img class="menu-icon" :src="noTopUpIcon" />
                      <span>取消置顶</span>
                    </div>
                    <div class="menu-item" @click="changeedit(item)">
                      <img class="menu-icon" :src="renameIcon" />
                      <span>重命名</span>
                    </div>
                    <div class="menu-item menu-item-danger" v-if="!item.ischeck" @click="delHistoryfun(item)">
                      <img class="menu-icon" :src="delIcon" />
                      <span>删除</span>
                    </div>
                  </div>
                </n-popover>
              </div>
            </div>
          </div>
        </div>
        </template>
      </div>
    </NScrollbar>
  </div>

  <!-- 折叠状态 -->
  <div v-else class="collapsed-history">
    <!-- 分隔线 -->
    <div class="collapsed-divider"></div>

    <div class="collapsed-history-header">
      <div class="collapsed-title">会话</div>
    </div>

    <div class="collapsed-history-list">
      <n-tooltip
        v-for="(item, index) of dataSources.slice(0, 5)"
        :key="index"
        placement="right"
        :delay="500"
      >
        <template #trigger>
          <div
            class="collapsed-history-item"
            :class="{ 'collapsed-item-active': item.ischeck }"
            @click="handleSelect(item)"
          >
            <div class="collapsed-item-icon">
              <img :src="item.icon" />
            </div>
            <div class="collapsed-item-indicator" v-if="item.topUp == '1'">
              <div class="pin-dot"></div>
            </div>
          </div>
        </template>
        <span>{{ item.title }}</span>
      </n-tooltip>

      <n-tooltip v-if="dataSources.length > 5" placement="right">
        <template #trigger>
          <div class="collapsed-more-btn" @click="jumlallhistory">
            <SvgIcon icon="ri:more-2-fill" class="more-icon" />
          </div>
        </template>
        <span>查看全部历史会话</span>
      </n-tooltip>
    </div>
  </div>
</template>
<style lang="less" scoped>
// 现代化历史会话列表样式
.parentbox {
  position: relative;
  padding-top: 54px;
}

.childrenbox {
  position: absolute;
  top: 0px;
  width: 100%;
}

.allhistoryrow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 28px;
  margin-bottom: 8px;
  margin-top: 16px;
  padding: 0 4px;

  .section-title {
    font-family: PingFangSC-Medium;
    font-weight: 600;
    font-size: 13px;
    color: #6b7280;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }

  .history-action-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(18, 94, 255, 0.08);
      transform: translateY(-1px);
    }

    .allhistoryicon {
      width: 14px;
      height: 14px;
      opacity: 0.7;
      transition: opacity 0.2s ease;
    }

    &:hover .allhistoryicon {
      opacity: 1;
    }
  }
}

.historybox {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.history-item-wrapper {
  width: 100%;
}

.modern-history-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  &:hover {
    background: #ffffff;
    border-color: rgba(18, 94, 255, 0.15);
    box-shadow: 0 4px 16px rgba(18, 94, 255, 0.08);
    transform: translateY(-1px);
  }

  &.history-item-active {
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
    border-color: #125EFF;
    box-shadow: 0 4px 16px rgba(18, 94, 255, 0.15);

    .history-title {
      color: #125EFF;
      font-weight: 600;
    }

    .agent-icon-wrapper {
      background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .agent-icon {
        // 保持原始图标，不使用filter
        opacity: 1;
      }
    }
  }

  &.history-item-pinned {
    background: linear-gradient(135deg, #fff7e6 0%, #ffffff 100%);
    border-color: rgba(255, 193, 7, 0.2);

    &:hover {
      border-color: rgba(255, 193, 7, 0.4);
      box-shadow: 0 4px 16px rgba(255, 193, 7, 0.1);
    }
  }
}

// 置顶标识
.pin-indicator {
  position: absolute;
  top: -2px;
  left: -2px;
  z-index: 2;

  .pin-icon {
    width: 20px;
    height: 19px;
    filter: drop-shadow(0 2px 4px rgba(255, 193, 7, 0.3));
  }
}

// 智能体图标
.agent-icon-wrapper {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6edf7;
  border-radius: 6px;
  transition: all 0.3s ease;
  flex-shrink: 0;

  .agent-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    transition: all 0.3s ease;
  }
}

// 会话内容
.history-content {
  flex: 1;
  min-width: 0;

  .history-title {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #2f3033;
    letter-spacing: 0;
    line-height: 1.4;
    transition: all 0.3s ease;
  }
}

// 操作按钮区域
.history-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transform: translateX(8px);
  transition: all 0.3s ease;

  &.actions-visible {
    opacity: 1;
    transform: translateX(0);
  }

  .action-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(107, 114, 128, 0.1);

    &:hover {
      background: rgba(18, 94, 255, 0.1);
      transform: scale(1.05);

      .action-icon {
        color: #125EFF;
      }
    }

    .action-icon {
      font-size: 12px;
      color: #6b7280;
      transition: all 0.2s ease;
    }
  }
}

// 现代化菜单样式
:deep(.modern-popover) {
  .n-popover {
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }
}

.modern-menu {
  padding: 4px;
  min-width: 120px;

  .menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 12px;
    color: #374151;

    &:hover {
      background: rgba(18, 94, 255, 0.08);
      color: #125EFF;

      .menu-icon {
        transform: scale(1.1);
      }
    }

    &.menu-item-danger {
      &:hover {
        background: rgba(239, 68, 68, 0.08);
        color: #ef4444;
      }
    }

    .menu-icon {
      width: 14px;
      height: 14px;
      transition: all 0.2s ease;
      flex-shrink: 0;
    }

    span {
      flex: 1;
      white-space: nowrap;
    }
  }
}

// 折叠状态样式
.collapsed-history {
  padding: 0 4px 8px 4px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.collapsed-divider {
  width: calc(100% - 8px);
  height: 1px;
  background: #E2E2E2;
  margin: 16px 4px 8px 4px;
}

.collapsed-history-header {
  padding: 8px 12px;
  margin-bottom: 12px;

  .collapsed-title {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-align: center;
    letter-spacing: 0.5px;
  }
}

.collapsed-history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  overflow-y: auto;

  // 隐藏滚动条
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.collapsed-history-item {
  position: relative;
  width: 48px;
  height: 48px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border: 1px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    background: #ffffff;
    border-color: rgba(18, 94, 255, 0.15);
    box-shadow: 0 4px 16px rgba(18, 94, 255, 0.08);
    transform: translateY(-1px);
  }

  &.collapsed-item-active {
    background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
    border-color: #125EFF;
    box-shadow: 0 4px 16px rgba(18, 94, 255, 0.25);

    .collapsed-item-icon {
      background: rgba(255, 255, 255, 0.2);

      img {
        // 保持原始图标，不使用filter
        opacity: 1;
      }
    }
  }

  .collapsed-item-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 36px;
      height: 36px;
      object-fit: contain;
      transition: all 0.3s ease;
    }
  }

  .collapsed-item-indicator {
    position: absolute;
    top: -2px;
    right: -2px;

    .pin-dot {
      width: 8px;
      height: 8px;
      background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
      border-radius: 50%;
      border: 2px solid #ffffff;
      box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
    }
  }
}

.collapsed-more-btn {
  width: 48px;
  height: 36px;
  margin: 8px auto 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(18, 94, 255, 0.1);
    transform: translateY(-1px);

    .more-icon {
      color: #125EFF;
    }
  }

  .more-icon {
    font-size: 16px;
    color: #6b7280;
    transition: all 0.2s ease;
  }
}
</style>
<style>
.siderdelbtn {
  background-color: #125eff !important; /* 可以根据需要调整背景色 */
}
.siderdelbtn .n-button__border {
  border: none !important;
}
</style>
