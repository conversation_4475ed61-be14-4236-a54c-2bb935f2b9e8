{"name": "chatgpt-web", "version": "2.11.1", "private": false, "description": "ChatGPT Web", "author": "ChenZhaoYu <<EMAIL>>", "keywords": ["chatgpt-web", "chatgpt", "chatbot", "vue"], "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "bootstrap": "pnpm install && pnpm run common:prepare", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml", "common:prepare": "husky install", "deploy": "npm run build-only && zip -r -q agent.zip agent && node deploy/deploy.js deploy/deploy.json"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@vitejs/plugin-legacy": "^7.2.1", "@vscode/markdown-it-katex": "^1.0.3", "@vue-flow/background": "^1.3.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.41.2", "@vue-flow/minimap": "^1.5.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue/composition-api": "^1.7.2", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^9.13.0", "core-js": "^3.45.0", "dayjs": "^1.11.13", "eucp-baselib": "^0.1.3", "github-markdown-css": "^5.8.1", "highlight.js": "^11.7.0", "hls.js": "^1.6.8", "html-to-image": "^1.11.11", "html2pdf.js": "^0.10.3", "js-base64": "^3.7.7", "katex": "^0.16.4", "markdown-it": "^13.0.1", "marked": "^15.0.12", "marked-highlight": "^2.2.1", "md5": "^2.3.0", "mermaid-it-markdown": "^1.0.8", "naive-ui": "^2.34.3", "pcm-player": "^0.0.18", "pdfjs-dist": "5.1.91", "pinia": "^2.0.33", "regenerator-runtime": "^0.14.1", "vite-plugin-wasm-pack": "0.1.11", "vue": "^3.2.47", "vue-demi": "0.13.11", "vue-i18n": "^9.2.2", "vue-pdf-embed": "^2.1.2", "vue-router": "^4.1.6", "xss": "^1.0.15"}, "devDependencies": {"@antfu/eslint-config": "^0.35.3", "@babel/core": "^7.28.3", "@babel/plugin-transform-runtime": "^7.28.3", "@babel/preset-env": "^7.28.3", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@iconify/vue": "^4.1.0", "@types/crypto-js": "^4.1.1", "@types/katex": "^0.16.0", "@types/markdown-it": "^12.2.3", "@types/markdown-it-link-attributes": "^3.0.1", "@types/node": "^18.14.6", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "axios": "^1.3.4", "babel-polyfill": "^6.26.0", "crypto-js": "^4.1.1", "es6-promise": "^4.2.8", "eslint": "^8.35.0", "husky": "^8.0.3", "less": "^4.1.3", "lint-staged": "^13.1.2", "markdown-it-link-attributes": "^4.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "rimraf": "^4.3.0", "ssh2": "^1.16.0", "tailwindcss": "^3.2.7", "typescript": "~4.9.5", "vite": "^4.2.0", "vite-plugin-pwa": "^0.14.4", "vue-tsc": "^1.2.0"}, "lint-staged": {"*.{ts,tsx,vue}": ["pnpm lint:fix"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11"]}