// 现代化组件公共样式
// 用于统一项目中的搜索框、卡片、标签等组件样式

// 现代化搜索框样式
.modern-search-input {
  :deep(.n-input-wrapper) {
    padding-right: 4px;
    padding-left: 16px;
    border: none;
    background: transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    &.n-input-wrapper--focus {
      box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }
  
  :deep(.n-input__input-el) {
    font-size: 15px;
    color: #2f3033;
    
    &::placeholder {
      color: #9ca3af;
      font-weight: 400;
    }
  }
}

.search-prefix-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.search-suffix-btn {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  img {
    filter: brightness(0) invert(1);
  }
}

// 章节标题样式
.section-title {
  height: 36px;
  font-weight: 600;
  font-size: 20px;
  color: #1f2937;
  line-height: 36px;
  display: flex;
  align-items: center;
  margin-top: 32px;
  margin-bottom: 16px;
  
  &.section-title-top {
    margin-top: 40px;
  }
}

// 收藏标签样式
.collect-tag {
  min-width: 120px;
  height: 40px;
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border: 1px solid #d1e3ff;
  border-radius: 20px;
  font-weight: 500;
  font-size: 14px;
  color: #125EFF;
  line-height: 40px;
  text-align: center;
  margin-right: 12px;
  margin-bottom: 12px;
  padding: 0 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
  }
}

// 分类标签样式
.category-tag {
  width: 128px;
  height: 40px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  font-weight: 500;
  font-size: 14px;
  color: #6b7280;
  line-height: 40px;
  text-align: center;
  margin-right: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #125EFF;
    color: #125EFF;
    box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
  }
  
  &.category-tag-active {
    background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
    border-color: #125EFF;
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
  }
}

// 现代化应用卡片设计
.application-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #e8ecf0;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  
  &:hover {
    background: #ffffff;
    border-color: #125EFF;
    box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
    transform: translateY(-2px);
  }
  
  &.application-card-disabled {
    background: #f7f7f7;
    opacity: 0.7;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      border-color: #e8ecf0;
    }
  }
}

.card-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.app-icon-wrapper {
  position: relative;
  margin-right: 20px;
  
  // 默认状态的微妙背景
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(30, 127, 255, 0.02) 100%);
    border-radius: 18px;
    opacity: 1;
    transition: all 0.3s ease;
  }
  
  .application-card:hover &::before {
    background: linear-gradient(135deg, rgba(18, 94, 255, 0.15) 0%, rgba(30, 127, 255, 0.08) 100%);
    transform: scale(1.05);
  }
}

.app-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  
  .application-card:hover & {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: scale(1.02);
  }
}

.app-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.app-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  line-height: 24px;
  margin: 0 0 8px 0;
  transition: color 0.2s ease;
  
  .application-card:hover & {
    color: #125EFF;
  }
}

.app-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 20px;
  margin: 0;
}

.card-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);
  margin: 16px 0;
}

.app-meta {
  display: flex;
  align-items: center;
  gap: 20px;
}

.heat-info,
.collect-action {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #6b7280;
  transition: color 0.2s ease;
  
  .meta-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    opacity: 0.8;
  }
}

.collect-action {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(18, 94, 255, 0.08);
    color: #125EFF;
    
    .meta-icon {
      opacity: 1;
    }
  }
}

// 应用卡片网格布局
.applicationrow {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
  gap: 20px;
  margin-top: 32px;
}

@media (max-width: 768px) {
  .bothends {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .applicationrow {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: 18px;
    margin-top: 24px;
  }
}
