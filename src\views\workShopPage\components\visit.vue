<script lang="ts" setup>
import {NButton, NInput, useMessage, NTabs, NTabPane, NSwitch, NTooltip, NDataTable, NSpin} from "naive-ui";
import {SvgIcon} from "@/components/common";
import {useRouter, useRoute} from 'vue-router'
import {copyToClip} from "@/utils/copy";
import {t} from "@/locales";
import {computed, onMounted, onUnmounted, onUpdated, ref, useTemplateRef, watch, h} from 'vue'
import {agentVisitConf, agentVisitConfUpdate, delAgentVisitConf, findVisitConfAgentId} from "@/api/workShop";
import copyImg from '@/assets/chat/copy.png'
import delImg from '@/assets/workShopPage/del.png'

const message = useMessage()
const router = useRouter()
const route = useRoute()

const agentId = route.query.id
const show = ref(false)

async function copy(value: any) {
	try {
		await copyToClip(value || '')
		message.success(t('chat.copied'))
	} catch {
		message.error(t('chat.copyFailed'))
	}
}

// 表格
const columns = [
	{
		title: '密钥',
		key: 'visitKey'
	}, {
		title: '创建时间',
		key: 'createdAt'
	}, {
		title: '最后使用',
		key: 'useTime'
	}, {
		title: ' ',
		key: 'useTime',
		render(row: any) {
			return h('div', {class: 'flex gap-2'}, [
				// 复制按钮的Tooltip
				h(NTooltip, {trigger: 'hover'}, {
					default: () => '复制', // Tooltip内容（默认插槽）
					trigger: () => h('img', {
						alt: '',
						class: 'w-[22px] h-[22px] cursor-pointer',
						src: copyImg,
						onClick: () => copy(row.visitKey) // 绑定点击事件
					})
				}),

				// 删除按钮的Tooltip
				h(NTooltip, {trigger: 'hover'}, {
					default: () => '删除', // Tooltip内容（默认插槽）
					trigger: () => h('img', {
						alt: '',
						class: 'w-[22px] h-[22px] cursor-pointer',
						src: delImg,
						onClick: () => delAgentVisit(row) // 绑定点击事件
					})
				})
			])
		}
	},
]
const data = ref([])

// 获取详情
const model = ref({})
const init = async () => {
	show.value = true
	let res = await findVisitConfAgentId(agentId)
	model.value = res.data
	data.value = res.data.keyList.map((item: any) => ({
		...item,
		useTime: item.useTime || '从未'
	}));
	show.value = false
}
init()

// 修改状态
const update = async () => {
	show.value = true
	try {
		await agentVisitConfUpdate(model.value.id, {
			apiVisitStatus: model.value.apiVisitStatus,
			publicVisitStatus: model.value.publicVisitStatus
		})
		show.value = false
	} catch (err) {
		show.value = false
	}
}

//创建密钥
const addVisit = async () => {
	if (data.value.length >= 10) {
		message.warning('API密钥最多只能创建10条')
		return
	}
	await agentVisitConf({visitConfId: model.value.id})
	await init()
	message.success('创建成功')
}
// 删除密钥
const delAgentVisit = async (row: any) => {
	await delAgentVisitConf(row.id)
	await init()
	message.success('删除成功')
}
</script>

<template>
	<div class="p-6">
		<div class="flex items-center justify-between mb-[20px]">
			<div class="flex items-center">
				<img
					alt=""
					class="w-[36px] cursor-pointer"
					src="@/assets/workShopPage/rest.png"
					@click="router.go(-1)"
				/>
				<div class="flex items-center">
					<p class="text-[20px] ml-4">
						{{ route.query.name }}
					</p>
				</div>
			</div>
		</div>

		<n-tabs animated type="line">
			<n-tab-pane name="oasis" tab="访问设置">
				<n-spin :show="show">
					<div class="mt-[20px] w-1/2">
						<div class="">
							<div
								class="text-[14px] text-[#666] flex justify-between"
							>
								<div class="flex items-center gap-[8px]">
									<div class="w-[2px] h-[14px] bg-[#666]"></div>
									<p>公开访问URL</p>
								</div>
								<NSwitch
									v-model:value="model.publicVisitStatus"
									:checked-value="1"
									:unchecked-value="0"
									@update:value="update"
								/>
							</div>
							<n-input
								v-model:value="model.publicVisitUrl"
								:disabled="!model.publicVisitStatus"
								:input-props="{ type: 'url' }"
								class="mt-[10px]"
								placeholder=""
								readonly
							>
								<template #suffix>
									<NTooltip trigger="hover">
										<template #trigger>
											<img alt="" class="w-[22px] h-[22px] cursor-pointer" src="@/assets/chat/copy.png"
													 @click="copy(model.publicVisitUrl)">
										</template>
										复制
									</NTooltip>

								</template>
							</n-input>
						</div>
						<div class="mt-8">
							<div
								class="text-[14px] text-[#666] flex justify-between"
							>
								<div class="flex items-center gap-[8px]">
									<div class="w-[2px] h-[14px] bg-[#666]"></div>
									<p>后端服务API</p>
								</div>
								<NSwitch
									v-model:value="model.apiVisitStatus"
									:checked-value="1"
									:unchecked-value="0"
									@update:value="update"
								/>
							</div>
							<n-input
								v-model:value="model.apiVisitUrl"
								:disabled="!model.apiVisitStatus"
								:input-props="{ type: 'url' }"
								class="mt-[10px]"
								placeholder=""
								readonly
							>
								<template #suffix>
									<NTooltip trigger="hover">
										<template #trigger>
											<img alt="" class="w-[22px] h-[22px] cursor-pointer" src="@/assets/chat/copy.png"
													 @click="copy(model.apiVisitUrl)">
										</template>
										复制
									</NTooltip>
								</template>
							</n-input>
						</div>

						<div class="flex w-full items-center justify-between mt-8 mb-4">
							<p>API密钥</p>
							<div class="">
								<n-button @click="addVisit">创建密钥</n-button>
							</div>
						</div>
						<n-data-table
							:bordered="false"
							:columns="columns"
							:data="data"
							:pagination="false"
						/>
					</div>
				</n-spin>
			</n-tab-pane>
		</n-tabs>
	</div>


</template>

<style lang="less" scoped>

</style>

