<script lang="ts" setup>
import {computed, onMounted, ref, watch, h} from 'vue'
import {
	NAlert,
	NButton,
	NCard,
	NDivider,
	NEllipsis,
	NForm,
	NFormItem,
	NInput,
	NModal,
	NPagination,
	NPopselect,
	NRadio,
	NRadioButton,
	NRadioGroup,
	useDialog,
	useMessage,
	NSpin, NSwitch,
} from 'naive-ui'
import {useRouter} from 'vue-router'
import radioCustom from './components/radioCustom.vue'
import {delModel, editShop, getAgentlist, getAgentlistId} from '@/api/workShop'
import {getlinksApi} from '@/api/tools'
import icon1 from '@/assets/applicationPage/icon1.png'
import {getpictureDictTextByCodeValue} from '@/utils/microFrontEnd'
import lssue from '@/assets/workShopPage/lssue.png'
import withdraw from '@/assets/workShopPage/withdraw.png'
import {SvgIcon} from '@/components/common'
import {debounce} from '@/utils/functions/debounce'
import {useOrchestrationStore} from '@/store'

const orchestrationStore = useOrchestrationStore()

const message = useMessage()
const router = useRouter()
const dialog = useDialog()
const page = ref(1)
const pageSize = ref(20)
const pageCount = ref(0)
const searchvalue = ref('')
const edit = ref(false)
const loadingshow = ref(false)
const options = (row: any) => [
	{
		label: '编辑',
		value: 'edit',
		disabled: row.status === 1,
	}, {
		label: '克隆',
		value: 'copy',
		disabled: true,
	}, {
		label: '使用分析',
		value: 'use',
		disabled: true,
	}, {
		label: '访问设置',
		value: 'visit',
		disabled: row.status === 0,
	}, {
		label: '删除',
		value: 'del',
	},
]
const moretagarr = ref([
	{name: '全部', ischeck: true, value: 0},
	// { name: "教研教学", ischeck: false, value: 1 },
	// { name: "自适应学习", ischeck: false, value: 2 },
	// { name: "校务服务", ischeck: false, value: 3 },
])
const applicationList = ref([])
const changmoretagfun = (index: any) => {
	moretagarr.value.forEach((item, i) => {
		item.ischeck = i === index
	})
}

function getagentlisfun() {
	loadingshow.value = true;

	getAgentlist({pageNum: page.value, pageSize: pageSize.value, name: searchvalue.value}).then((res) => {
		loadingshow.value = false;

		console.log(res, 'getagentlisfun  ------ 109')
		if (res.code == '0') {
			res.data.items
				.forEach((item: any) => {
					item.iconUrl = getpictureDictTextByCodeValue('profile_picture', item.icon)
				})
			applicationList.value = res.data.items
			pageCount.value = Number(res.data.total)
		} else {
			message.error(res.message)
		}
	}).catch(() => {
		loadingshow.value = false;
	})
}

const updatePage = (v: any) => {
	page.value = v
	getagentlisfun()
}
const updatePageSize = (v: any) => {
	pageSize.value = v
	getagentlisfun()
}
const searchTap = debounce(() => {
	page.value = 1;
	getagentlisfun()
}, 500)

const departId = ref('')

async function getLinkFun() {
	const res = await getlinksApi({linkType: 'ZSK'})
	departId.value = res.data[0].linkUrl
}

onMounted(async () => {
	await getLinkFun()
	getagentlisfun()
})

// 新建智能体
const showModal = ref(false)
const formRef = ref()
const model = ref({
	buildCategory: '0',
	scenceCategory: null,
})
const rules = {
	buildCategory: {
		required: true,
		message: '请选择构建方式',
		trigger: 'input',
	},
	name: {
		required: true,
		message: '请输入智能体名称',
		trigger: 'blur',
	},
	scenceCategory: {
		required: true,
		message: '请选择场景分类',
		trigger: 'blur',
	},
	description: {
		required: true,
		message: '请输入智能体介绍',
		trigger: 'blur',
	},
}

const songs = ref([
	{
		value: '0',
		label: '自适应学习',
	},
	{
		value: '1',
		label: '教研教学',
	},
	{
		value: '2',
		label: '校务服务',
	},
])
const radioCustomList = computed(() => [
	{
		value: '0',
		label: '简单构建',
		note: '构建简单的对话问答智能体',
		disabled: edit.value ? model.value.buildCategory !== '0' : false,
	},
	{
		value: '1',
		label: '工作流创建',
		note: '工作流编排创建强大能力智能体',
		disabled: edit.value ? model.value.buildCategory !== '1' : false,
	},
])
// const radioCustomListMsg = ref(radioCustomList())

const offModal = (flag: any) => {
	showModal.value = flag
}
const afterLeave = () => {
	model.value = {buildCategory: '0'}
	edit.value = false
}

const radioCustomChange = (val: any) => {
	model.value.buildCategory = val.value
}

const submit = async (e: any) => {
	e.preventDefault()
	formRef.value?.validate((errors: any) => {
		if (!errors) {
			console.log(model.value)
			if (model.value.buildCategory == '0') {
				router.push({
					path: '/creatIntelligencePage',
					query: {
						params: JSON.stringify(model.value),
						edit: edit.value,
					},
				})
			} else {
				orchestrationStore.updateNewFlowFrom(
					{
						name: model.value.name,
						description: model.value.description,
						scenceCategory: model.value.scenceCategory
					}
				)
				router.push({
					path: '/agentOrchestration',
					query: {
						edit: edit.value,
					},
				})
			}
		} else {
			console.log(errors)
		}
	})
}

// 操作行
const updatePopSelect = async (val: string, item: any) => {
	console.log(val, item)
	if (val === 'del') {
		dialog.warning({
			title: '删除智能体',
			content: () => h('div', {innerHTML: `${item.status === 1 ? '当前智能体已发布，删除后将下架该智能体并删除，用户将不能继续使用该智能体。' : '删除后将无法继续使用该智能体。'}<br>你确定要删除当前智能体吗？`}),
			positiveText: '删除',
			negativeText: '取消',
			draggable: true,
			onPositiveClick: async () => {
				await delModel(item.id)
				getagentlisfun()
				message.success('删除成功')
			},
		})
	} else if (val === 'edit') {
		const res = await getAgentlistId(item.id)
		if (res.data.buildCategory == '1') {
			await router.push({
				path: '/agentOrchestration',
				query: {
					id: item.id,
					name: res.data.name,
					edit: edit.value,
				},
			})
			return
		}
		edit.value = true
		const {name, buildCategory, description, scenceCategory, id} = res.data
		showModal.value = true
		model.value = {
			name, buildCategory: String(buildCategory), description, scenceCategory: String(scenceCategory), id,
		}
		console.log(res)
	} else if (val === 'visit') {
		await router.push({
			path: '/visit',
			query: {
				id: item.id,
				name: item.name,
			},
		})
	}
}

// 发布逻辑
const issueModal = ref(false)
const issueContent = ref({
	jurisdiction: '0'
})
const issueContentRules = {
	note: {
		required: true,
		message: '请输入发布说明',
		trigger: 'input',
	},
}
const issueMsg = ref(null)
const jurisdictionSongs = ref([
	{
		value: '0',
		label: '学生',
	},
	{
		value: '1',
		label: '教师',
	},
])

// 发布  1已发布  0未发布
const issueFormRef = ref()
const upLoading = ref(false)
const issueSubmit = async (e: any) => {
	e.preventDefault()
	issueFormRef.value?.validate((errors: any) => {
		if (!errors) {
			editShopTap()
		} else {
			console.log(errors)
		}
	})
}
// 取消发布
const changeLssue = (item: any) => {
	issueMsg.value = item
	if (item.status === 1) {
		dialog.warning({
			title: '确认要取消发布',
			content: '取消发布后，智能体将对平台用户不可见',
			positiveText: '确定',
			negativeText: '取消',
			draggable: true,
			onPositiveClick: () => {
				editShopTap()
			},
			onNegativeClick: () => {
			},
		})
	} else {
		issueModal.value = true
	}
}
const editShopTap = () => {
	let obj = null
	upLoading.value = true
	if (issueMsg.value.status == 1) {
		obj = {
			status: 0,
		}
	} else {
		obj = {
			status: 1,
			releaseNotes: issueContent.value.note,
			roles: issueContent.value.jurisdiction,
			agentVisitConf: {
				apiVisitStatus: issueContent.value.apiVisitStatus || 0,
				publicVisitStatus: issueContent.value.publicVisitStatus || 0
			}
		}
	}
	console.log(obj)
	editShop(obj, issueMsg.value.id).then((res) => {
		console.log(res)
		if (res.code == '0') {
			getagentlisfun()
			issueModal.value = false
			issueMsg.value = null
			message.success('操作成功')
		} else {
			message.error(res.message)
		}
		upLoading.value = false
	}).catch(err => {
		upLoading.value = false
		message.error('操作失败')
	})
}
// 弹窗消失
const afterLeaveEdit = () => {
	issueContent.value = {jurisdiction: '0'}
}
</script>

<template>
	<div class="app p-8 pr-[37px] pl-[40px] pb-[80px]">
		<n-spin :show="loadingshow">
			<header class="bothends flex justify-between items-center">
				<div class="title h-9 font-semibold text-[26px] text-[#2f3033] leading-9 flex items-center">
					<img alt="" class="w-[22px] h-[22px] mr-2" src="@/assets/toolboxPage/titicon.png"> 智能体工坊
				</div>
				<div class="w-[400px] h-12">
					<NInput
						v-model:value="searchvalue" class="modern-search-input"
						clearable
						placeholder="搜索智能体"
						round
						size="large"
						@update:value="searchTap"
					>
						<template #prefix>
							<div class="search-prefix-icon">
								<img class="w-[18px] h-[18px] opacity-60" src="../../assets/toolboxPage/SearchOutline.png">
							</div>
						</template>
						<template #suffix>
							<div class="search-suffix-btn" @click="searchTap">
								<img class="w-[18px] h-[18px]" src="../../assets/toolboxPage/SearchOutline.png">
							</div>
						</template>
					</NInput>
				</div>
			</header>

			<div class="collectbox flex flex-wrap">
				<div v-for="(item, index) in moretagarr" :key="index"
						 :class="{ 'category-tag-active': item.ischeck }"
						 class="category-tag"
						 @click="changmoretagfun(index)">
					{{ item.name }}
				</div>
			</div>
			<div class="applicationrow">
				<div class="create-card" @click="showModal = true">
					<div class="create-icon">+</div>
					<div class="create-title">创建你的专属智能体</div>
				</div>

				<div v-for="(item, index) in applicationList" :key="index" class="application-card">
					<div class="card-content">
						<div class="app-icon-wrapper">
							<img :src="icon1" class="app-icon">
						</div>
						<div class="app-info">
							<div class="app-header">
								<h3 class="app-name">{{ item.name }}</h3>
								<div class="app-tags">
									<span v-if="item.buildCategory === 1" class="app-tag">工作流</span>
									<span v-if="item.buildCategory === 0" class="app-tag">问答</span>
									<div class="app-actions">
										<NPopselect :options="options(item)" trigger="hover" @update:value="updatePopSelect($event, item)">
											<SvgIcon class="cursor-pointer" icon="material-symbols:more-vert"/>
										</NPopselect>
									</div>
								</div>
							</div>
							<div class="app-description">
								<NEllipsis :line-clamp="2">
									{{ item.description }}
									<template #tooltip>
										<div style="text-align: center;width: 250px;">
											{{ item.description }}
										</div>
									</template>
								</NEllipsis>
							</div>
						</div>
					</div>

					<div class="card-divider"></div>

					<div class="app-meta">
						<div class="knowledge-info">
							<img src="@/assets/workShopPage/books.png">
							<span>{{ item.agentKnowledgeNum }}</span>
						</div>
						<div class="publish-status">
							<div :class="{ 'status-published': item.status == 1 }" class="status-indicator">
								<span class="status-dot"></span>
							</div>
							<span class="status-text">{{ item.status == 1 ? '已发布' : '未发布' }}</span>
							<img :src="!item.status == 1 ? lssue : withdraw" class="status-action" @click="changeLssue(item)">
						</div>
					</div>
				</div>
			</div>
			<div class="fixed right-[20px] bottom-[16px]">
				<NPagination
					v-model:page="page"
					:item-count="pageCount"
					:on-update:page="updatePage"
					:on-update:page-size="updatePageSize"
					:page-size="pageSize"
					:page-sizes="[10, 20, 50, 100]"
					show-quick-jumper
					show-size-picker
				/>
			</div>
		</n-spin>
	</div>

	<!-- 新建 -->
	<NModal v-model:show="showModal" @after-leave="afterLeave">
		<NCard
			:bordered="false"
			:title="edit ? '编辑智能体' : '新建智能体'"
			aria-modal="true"
			role="dialog"
			size="huge"
			style="width: 700px"
		>
			<NForm
				ref="formRef"
				:model="model"
				:rules="rules"
				label-placement="top"
			>
				<NFormItem label="构建方式" path="buildCategory">
					<radioCustom :edit="edit" :list="radioCustomList" :value="model.buildCategory" @change="radioCustomChange"/>
				</NFormItem>
				<NFormItem label="智能体名称" path="name">
					<NInput
						v-model:value="model.name"
						placeholder="智能体名称"
					/>
				</NFormItem>
				<NFormItem label="智能体介绍" path="description">
					<NInput
						v-model:value="model.description"
						maxlength="200"
						placeholder="智能体介绍"
						show-count type="textarea"
					/>
				</NFormItem>
				<NFormItem label="场景分类" path="scenceCategory">
					<NRadioGroup v-model:value="model.scenceCategory" name="radiobuttongroup1">
						<NRadioButton
							v-for="song in songs"
							:key="song.value"
							:label="song.label"
							:value="song.value"
						/>
					</NRadioGroup>
				</NFormItem>
			</NForm>
			<template #footer>
				<div class="flex flex-row-reverse w-full">
					<NButton type="info" @click="submit">
						确认
					</NButton>
					<NButton class="!mr-5" @click="offModal(false)">
						取消
					</NButton>
				</div>
			</template>
		</NCard>
	</NModal>

	<!-- 发布 -->
	<NModal v-model:show="issueModal" @after-leave="afterLeaveEdit">
		<NCard
			:bordered="false"
			aria-modal="true"
			role="dialog"
			size="huge"
			style="width: 700px"
			title="发布智能体"
		>
			<!--			<NAlert class="mb-[20px]" type="info">-->
			<!--				智能体发布后，将对平台用户可见-->
			<!--			</NAlert>-->
			<NForm
				ref="issueFormRef"
				:model="issueContent"
				:rules="issueContentRules"
				label-placement="top"
			>
				<NFormItem label="发布说明" path="note">
					<NInput v-model:value="issueContent.note" maxlength="200" placeholder="发布说明" show-count type="textarea"/>

				</NFormItem>
				<NFormItem label="发布范围">
					<NRadioGroup v-model:value="issueContent.jurisdiction" name="jurisdictionGroup">
						<NRadio
							v-for="song in jurisdictionSongs"
							:key="song.value"
							:label="song.label"
							:value="song.value"
						/>
					</NRadioGroup>
				</NFormItem>
				<NFormItem
					label="API发布"
					label-placement="left"
				>
					<NSwitch
						v-model:value="issueContent.apiVisitStatus"
						checked-value="1"
						unchecked-value="0"
					/>
				</NFormItem>
				<NFormItem
					label="URL发布"
					label-placement="left"
				>
					<NSwitch
						v-model:value="issueContent.publicVisitStatus"
						checked-value="1"
						unchecked-value="0"
					/>
				</NFormItem>
			</NForm>
			<template #footer>
				<div class="flex flex-row-reverse w-full">
					<NButton :loading="upLoading" type="info" @click="issueSubmit">
						确认
					</NButton>
					<NButton class="!mr-5" @click="issueModal = false">
						取消
					</NButton>
				</div>
			</template>
		</NCard>
	</NModal>
</template>

<style lang="less" scoped>
.app {
	background: url("@/assets/topbg.png") no-repeat;
	background-size: 90% 220px;
	background-position-x: 5%;
}

// 现代化搜索框样式
.modern-search-input {
	:deep(.n-input-wrapper) {
		padding-right: 4px;
		padding-left: 16px;
		border: none;
		background: transparent;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		transition: all 0.3s ease;
		border-radius: 3rem;

		&:hover {
			box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
		}

		&.n-input-wrapper--focus {
			box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
		}
	}

	:deep(.n-input__input-el) {
		font-size: 15px;
		color: #2f3033;

		&::placeholder {
			color: #9ca3af;
			font-weight: 400;
		}
	}
}

.search-prefix-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 8px;
}

.search-suffix-btn {
	width: 36px;
	height: 36px;
	background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		transform: scale(1.05);
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
	}

	&:active {
		transform: scale(0.98);
	}

	img {
		filter: brightness(0) invert(1);
	}
}

// 分类标签样式
.category-tag {
	width: 128px;
	height: 40px;
	background: #ffffff;
	border: 1px solid #e5e7eb;
	border-radius: 20px;
	font-weight: 500;
	font-size: 14px;
	color: #6b7280;
	line-height: 40px;
	text-align: center;
	margin-right: 12px;
	margin-bottom: 12px;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		border-color: #125EFF;
		color: #125EFF;
		box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
	}

	&.category-tag-active {
		background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
		border-color: #125EFF;
		color: #ffffff;
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
	}
}

.collectbox {
	margin-top: 32px;
}

// 应用卡片网格布局
.applicationrow {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
	gap: 20px;
	margin-top: 32px;
}

// 创建卡片样式
.create-card {
	background: linear-gradient(135deg, #E7F9FF 0%, #F5F5FF 100%);
	border: 2px dashed #125EFF;
	border-radius: 16px;
	padding: 40px 24px;
	text-align: center;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	//min-height: 200px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	&:hover {
		background: linear-gradient(135deg, #d1e9ff 0%, #e8f0ff 100%);
		border-color: #0052cc;
		transform: translateY(-2px);
		box-shadow: 0 8px 32px rgba(18, 94, 255, 0.15);
	}
}

.create-icon {
	width: 48px;
	height: 48px;
	border-radius: 50%;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32px;
	color: #125EFF;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
}

.create-title {
	font-size: 18px;
	font-weight: 500;
	color: #125EFF;
	line-height: 24px;
}

// 现代化应用卡片设计
.application-card {
	background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
	border: 1px solid #e8ecf0;
	border-radius: 16px;
	padding: 24px;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

	&:hover {
		background: #ffffff;
		border-color: #125EFF;
		box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
		transform: translateY(-2px);
	}
}

.card-content {
	margin-bottom: 16px;
}

.app-icon-wrapper {
	position: relative;
	margin-right: 20px;
	float: left;

	// 默认状态的微妙背景
	&::before {
		content: '';
		position: absolute;
		top: -6px;
		left: -6px;
		right: -6px;
		bottom: -6px;
		background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(30, 127, 255, 0.02) 100%);
		border-radius: 18px;
		opacity: 1;
		transition: all 0.3s ease;
	}

	.application-card:hover &::before {
		background: linear-gradient(135deg, rgba(18, 94, 255, 0.15) 0%, rgba(30, 127, 255, 0.08) 100%);
		transform: scale(1.05);
	}
}

.app-icon {
	width: 64px;
	height: 64px;
	border-radius: 12px;
	position: relative;
	z-index: 1;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	.application-card:hover & {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
		transform: scale(1.02);
	}
}

.app-info {
	overflow: hidden;
}

.app-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 12px;
}

.app-name {
	font-size: 18px;
	font-weight: 600;
	color: #1f2937;
	line-height: 24px;
	margin: 0;
	flex: 1;
	transition: color 0.2s ease;

	.application-card:hover & {
		color: #125EFF;
	}
}

.app-tags {
	display: flex;
	align-items: center;
	gap: 8px;
}

.app-tag {
	font-size: 12px;
	color: #6b7280;
	background: rgba(107, 114, 128, 0.1);
	border: 1px solid rgba(107, 114, 128, 0.2);
	border-radius: 12px;
	padding: 2px 8px;
	line-height: 16px;
}

.app-actions {
	display: flex;
	align-items: center;
}

.app-description {
	font-size: 14px;
	color: #6b7280;
	line-height: 20px;
	margin: 0;
	clear: both;
}

.card-divider {
	height: 1px;
	background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);
	margin: 16px 0;
}

.app-meta {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.knowledge-info {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #6b7280;

	img {
		width: 16px;
		height: 16px;
		margin-right: 6px;
		opacity: 0.8;
	}
}

.publish-status {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #6b7280;
	gap: 8px;
}

.status-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 14px;
	height: 14px;
	border-radius: 50%;
	background: #e5e7eb;

	&.status-published {
		background: #c0f1cc;
	}
}

.status-dot {
	width: 4px;
	height: 4px;
	border-radius: 50%;
	background: #9ca3af;

	.status-published & {
		background: #30d158;
	}
}

.status-text {
	font-size: 13px;
	color: #6b7280;
}

.status-action {
	width: 16px;
	height: 16px;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		transform: scale(1.1);
	}
}

@media (max-width: 768px) {
	.app {
		padding: 16px 20px;
	}

	.bothends {
		flex-direction: column;
		gap: 20px;
		align-items: flex-start;
	}

	.applicationrow {
		grid-template-columns: 1fr;
	}

	.card-content {
		padding: 20px;
	}
}
</style>
