<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import {
  NButton,
  NCard,
  NInput,
  NModal,
  NPagination,
  NSelect,
  NSpin,
  NUpload,
  NUploadDragger,
  useMessage,
} from "naive-ui";
import { useToolsStore, usestretchoutStore, loadingPageStore } from "@/store";
import { infoStore } from "@/store/modules/info";
import signMd5Utils from "@/utils/encryption/signMd5Utils";
import { getUrl } from "@/api";
import { addTextbook, textbook } from "@/api/courseware";
import { auth } from "@/utils/auth";
import { calculateFileMD5Fast } from "@/utils/file/md5";
import word from "@/assets/toolboxPage/word.png";
import pdf from "@/assets/toolboxPage/pdf.png";
import ppt from "@/assets/toolboxPage/ppt.png";
import { getpictureDictTextByCodeValue } from "@/utils/microFrontEnd";
import mp4 from "@/assets/toolboxPage/video.png";
import { useCrypto } from "@/hooks/useSystem";

const loadingStorage = loadingPageStore();
const { sm4Decrypt, sm4Encrypt } = useCrypto();
const encryptRequestResponse = import.meta.env
  .VITE_GLOB_ENCRYPT_REQUEST_RESPONSE;
const message = useMessage();
const router = useRouter();
const info = infoStore();
const ToolsStore = useToolsStore();
const useTretchOut = usestretchoutStore();
const charts = ref(null);
const flagBtn = computed(() => {
  return !active.value?.id;
});

// 上传逻辑
const fileList = ref([]);
// const device = window.$Eucp?.auth?.getBrowserFingerprint()
const ossAction = computed(
  () => `${import.meta.env.VITE_GLOB_API_URL}${getUrl}`
); // 上传header
const getUploadHeaders = () => {
  const headers: any = reactive({
    "X-Access-Token": (window as any).$EucpAuth?.getToken(),
    Authorization: `Bearer ${(window as any).$EucpAuth?.getToken()}`,
    // 'X-Tenant-Id': tenantId ? tenantId : '0',
  });
  const timestamp = signMd5Utils.getTimestamp();
  headers["X-TIMESTAMP"] = timestamp;
  headers["X-Sign"] = signMd5Utils.getSign("/eucp_api/resource/files/upload", {
    "X-TIMESTAMP": timestamp,
  });
  return headers;
};

// 校验文件类型
function canUpload(filename: any) {
  // 去掉路径部分，只保留文件名
  const cleanName = filename.replace(/^.*[\\/]/, "");

  // 提取扩展名
  const extMatch = cleanName.match(/\.([^.]+)$/);
  if (!extMatch) return false;

  // 获取小写扩展名
  const ext = extMatch[1].toLowerCase();

  const arr = ["pdf", "docx", "ppt", "doc", "pptx"];
  // 判断扩展名是否在允许列表里
  return arr.includes(ext);
}

// 上传前回调
async function beforeUpload(info: any) {
  let loadingMessage: any = null;

  try {
    // 检查文件类型
    if (!canUpload(info.file.fullPath)) {
      message.error("只能上传PDF、WORD、PPT格式的文件，请重新上传");
      return false;
    }

    // 检查文件大小
    if (info.file.file.size > 52428800) {
      message.error("文件大小不能大于50MB");
      return false;
    }

    // // 显示计算MD5的加载提示
    // loadingMessage = message.loading("正在检查文件...", { duration: 0 });

    // // 计算文件MD5
    // const fileMD5 = await calculateFileMD5Fast(info.file.file);
    // console.log("文件MD5:", fileMD5);
    // // 检查文件是否已存在
    // const checkResult = await checkFileMD5(fileMD5);

    // // 清除加载提示
    // loadingMessage.destroy();

    // if (
    //   (checkResult as any).code === "0" &&
    //   (checkResult as any).data &&
    //   (checkResult as any).data.exists
    // ) {
    //   // 文件已存在，显示提示并阻止上传
    //   message.warning(
    //     `文件已存在！文件名: ${
    //       (checkResult as any).data.fileName || "未知"
    //     }，上传时间: ${(checkResult as any).data.uploadTime || "未知"}`
    //   );
    //   return false;
    // }

    // 文件不存在，可以继续上传
    loadingStorage.resetLoadingPageData();
    loadingStorage.updateloadingData({ size: info.file.file.size });

    router.push({
      path: "/loadingPage",
      query: {
        type: "courseware",
      },
    });

    return true;
  } catch (error) {
    // 清除加载提示（如果还存在的话）
    try {
      loadingMessage?.destroy();
    } catch (e) {
      // 忽略清除消息时的错误
    }

    console.error("文件检查失败:", error);
    message.error("文件检查失败，请重试");
    return false;
  }
}

/**
 * 解析文件名，提取文件格式和不带格式的名称
 * @param fileName 完整的文件名，例如 "xxx.docx"
 * @returns 包含名称和格式的对象
 */
function parseFileName(fileName: string): { name: string; ext: string } {
  // 处理空字符串情况
  if (!fileName) {
    return { name: "", ext: "" };
  }

  // 找到最后一个点的位置
  const lastDotIndex = fileName.lastIndexOf(".");

  // 如果没有点或者点在开头，认为没有扩展名
  if (lastDotIndex <= 0) {
    return { name: fileName, ext: "" };
  }

  // 提取名称和扩展名
  const name = fileName.substring(0, lastDotIndex);
  const ext = fileName.substring(lastDotIndex + 1).toLowerCase();

  return { name, ext };
}

async function finish(file: any) {
  console.log(file);
  if (file.file.status === "finished") {
    // message.success('上传成功')
    const currentTarget = JSON.parse(file.event.target.response);
    console.log(currentTarget);
    console.log(currentTarget.code);
    console.log(currentTarget.data);
    var name = "";
    var savePath = "";
    var fileUrl = "";
    var filePath = "";

    if (encryptRequestResponse == "true") {
      name = JSON.parse(sm4Decrypt(currentTarget.data)).name;
      savePath = JSON.parse(sm4Decrypt(currentTarget.data)).savePath;
      fileUrl = JSON.parse(sm4Decrypt(currentTarget.data)).fileUrl;
      filePath = JSON.parse(sm4Decrypt(currentTarget.data)).filePath;
    } else {
      name = currentTarget.data.name;
      savePath = currentTarget.data.filePath;
      fileUrl = currentTarget.data.fileUrl;
      filePath = currentTarget.data.filePath;
    }
    // 创建数据
    let res = await addTextbook({
      status: "0",
      url: filePath,
      size: file.file.file.size,
      type: parseFileName(name)?.ext,
      title: parseFileName(name)?.name,
      textbookType:
        parseFileName(name)?.ext === "pptx" ||
        parseFileName(name)?.ext === "ppt"
          ? "1"
          : "0",
    });
    console.log(res);
    info.setBreadcrumb([
      {
        id: res.data.id,
        title: res.data.title,
        textbookType: res.data.textbookType,
      },
    ]);
    loadingStorage.updateloadingData({
      fileId: res.data.id,
      uploadStatus: true,
      fileUrl: fileUrl,
      previewUrl: fileUrl,
    });
  } else message.error("上传失败");
}

// 选取教学材料
const ShowAddStore = ref(false);
const searchValue = ref("");
const valueSelect:any = ref('1');
const options = ref([
  {
    label: "教材",
    value: "0",
  },
  {
    label: "课件",
    value: "1",
  },
]);
const applicationList = ref([]);

const showTap = (id?: any) => {
  ShowAddStore.value = !ShowAddStore.value;
  if (ShowAddStore.value) {
    getAgentLisFun(id);
  }
};

function jumpPage(url: string) {
  console.log(url);

  if (url) {
    router.push(url);
  } else {
    router.push("/toolboxPage");
  }
}

const show = ref(false);

// 分页
const page = ref(1);
const pageSize = ref(20);
const pageCount = ref(0);
const loadingShow = ref(false);
const active: any = ref(null);

function getAgentLisFun(id?: any) {
  loadingShow.value = true;
  // console.log({
  //   pageNum: page.value,
  //   pageSize: pageSize.value,
  //   title: searchValue.value,
  //   textbookTypeText: valueSelect.value,
  //   createdBy: id || null,
  // });
  textbook({
    pageNum: page.value,
    pageSize: pageSize.value,
    title: searchValue.value,
    textbookTypeText: valueSelect.value ? valueSelect.value : "0,1",
    createdBy: id || null,
  })
    .then((res) => {
      loadingShow.value = false;
      if ((res as any).code == "0") {
        console.log(res);
        applicationList.value = (res as any).data.items;
        pageCount.value = Number((res as any).data.total);
      }
    })
    .catch((err) => {
      loadingShow.value = false;
      message.error(err);
    });
}

const updatePage = (v: any) => {
  page.value = v;
  getAgentLisFun();
};
const updatePageSize = (v: any) => {
  pageSize.value = v;
  getAgentLisFun();
};
const itemTap = (item: any) => {
  if (active.value?.id === item.id) active.value = null;
  else active.value = item;
};
const afterLeave = () => {
  active.value = null;
  valueSelect.value = '1';
  searchValue.value = "";
};
// 类型映射关系：键为type值，值为对应的图片名称
const typeMap = {
  ppt: ppt,
  pptx: ppt,
  pdf: pdf,
  doc: word,
  docx: word,
  mp4: mp4,
};

const confirm = (id?: any, title?: any, textbookType?: any) => {
  info.setBreadcrumb([{ id, title, textbookType }]);
  router.push({
    path: "/coursewareChat",
    query: { id },
  });
};

onMounted(() => {});
</script>

<template>
  <div class="w-full h-full bg-[#F7F9FF]">
    <header class="headers pt-[30px]">
      <div class="left">
        <div class="gohome" @click="jumpPage()">
          <img src="@/assets/workShopPage/leftarrow.png" />
        </div>
        {{ ToolsStore.ToolInfo.name }}
      </div>
    </header>

    <div class="w-full flex flex-col h-[85%] items-center justify-center mt-3">
      <div class="w-full kaichangbai headpad">
        <img :src="ToolsStore.ToolInfo.icon" />
        <div class="title">
          {{ ToolsStore.ToolInfo.title }}
        </div>
        <div class="des">
          {{ ToolsStore.ToolInfo.des }}
        </div>
      </div>
      <div class="max-w-[950px] h-[473px] flex gap-6 mt-[60px]">
        <div class="w-[632px]">
          <NUpload
            :action="ossAction"
            :default-file-list="fileList"
            :headers="getUploadHeaders"
            accept=".pdf,.doc,.docx,.ppt,.pptx"
            directory-dnd
            multiple
            @finish="finish"
            @before-upload="beforeUpload"
          >
            <NUploadDragger>
              <div
                class="mt-[90px] mb-[91px] w-full px-[6px] h-[242px] flex justify-between"
              >
                <img
                  alt=""
                  class="w-[274px] h-[242px]"
                  src="@/assets/toolboxPage/zskxq.png"
                />
                <div class="w-[250px] h-full text-left">
                  <p class="text-[24px] font-semibold">本地上传教学材料</p>
                  <p class="text-[14px] text-[#8B8BA0] mt-[30px]">
                    支持从本地上传 PDF、WORD、PPT 等格式的教学资源
                  </p>
                  <p class="text-[14px] text-[#8B8BA0] mt-[10px]">
                    单个文件不超过 50M
                  </p>
                  <div
                    class="flex justify-center items-center rounded-[28px] mt-[50px] border-gray-50 w-[213px] h-[50px] up-shaow"
                  >
                    <img
                      alt=""
                      class="w-[22px] h-[22px] mr-[12px]"
                      src="@/assets/toolboxPage/upload.png"
                    />
                    <p class="text-[18px] font-semibold">材料上传</p>
                  </div>
                </div>
              </div>
            </NUploadDragger>
          </NUpload>
        </div>

        <div class="w-[300px]">
          <div
            class="w-full h-[252px] up-border flex justify-center items-center relative dotBorder"
            @click="showTap()"
          >
            <div class="w-[212px] h-[165px] relative">
              <img alt="" class="w-full" src="@/assets/toolboxPage/zskxq.png" />
            </div>
            <p
              class="absolute bottom-[35px] left-[50%] translate-x-[-50%] text-[22px] font-semibold"
            >
              从知识库选取
            </p>
          </div>
          <div
            class="w-full h-[209px] mt-[12px] up-border flex justify-center items-center relative dotBorder"
            @click="showTap(auth.getUserInfo().id)"
          >
            <div class="w-[212px] h-[152px] relative">
              <img alt="" class="w-full" src="@/assets/toolboxPage/wdsc.png" />
            </div>
            <p
              class="absolute bottom-[20px] left-[50%] translate-x-[-50%] text-[22px] font-semibold"
            >
              我的上传
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <NModal v-model:show="ShowAddStore" @after-leave="afterLeave">
    <NCard
      role="dialog"
      size="huge"
      style="width: 1058px"
      title="请选择教学材料"
    >
      <div class="">
        <n-spin :show="loadingShow">
          <div class="flex mb-5 gap-4 items-center">
            <!--          <NInput v-model:value="searchvalue" placeholder="搜索智能体" size="small"> -->

            <!--          </NInput> -->
            <NSelect
              v-model:value="valueSelect"
              :options="options"
              class="!w-80"
              clearable
              placeholder="请选择教学材料类型"
            />

            <NInput
              v-model:value="searchValue"
              class="!w-80"
              clearable
              placeholder="请输入材料名称"
            />
            <NButton type="info" @click="getAgentLisFun(null)">
              <img
                alt=""
                class="w-[16px] h-[16px] mr-[8px]"
                src="@/assets/toolboxPage/searh.png"
              />
              搜索
            </NButton>
          </div>
          <div v-if="applicationList.length" class="applicationrow">
            <div
              v-for="(item, index) in applicationList"
              :key="index"
              :class="active?.id === item.id ? ' application-card-active' : ''"
              class="cursor-pointer application-card"
              @click="itemTap(item)"
            >
              <div class="card-content gap-[14px]">
                <img
                  :src="typeMap[item.type]"
                  alt=""
                  class="w-[34px] h-[38px]"
                />
                <div class="w-[234px]">
                  <p class="ellipsis">
                    {{ item.title }}
                  </p>
                  <div class="flex w-full justify-between mt-[4px]">
                    <p class="text-[12px] text-[#838383]">
                      {{ (item.size / 1024 / 1024)?.toFixed(2) }}MB
                    </p>
                    <p class="text-[12px] text-[#838383]">
                      {{ item.createdAt }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <span class="empty-svg">
              <svg
                fill="none"
                height="96"
                viewBox="0 0 96 96"
                width="96"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  fill="#eaf1ff"
                  height="48"
                  rx="16"
                  width="72"
                  x="12"
                  y="28"
                />
                <rect
                  fill="#b6d0ff"
                  height="24"
                  rx="8"
                  width="48"
                  x="24"
                  y="40"
                />
                <circle cx="36" cy="52" fill="#fff" r="4" />
                <circle cx="60" cy="52" fill="#fff" r="4" />
                <rect
                  fill="#b6d0ff"
                  height="4"
                  rx="2"
                  width="8"
                  x="44"
                  y="64"
                />
                <rect
                  fill="#b6d0ff"
                  height="12"
                  rx="6"
                  width="16"
                  x="40"
                  y="20"
                />
                <rect
                  fill="#b6d0ff"
                  height="8"
                  rx="2"
                  width="4"
                  x="46"
                  y="12"
                />
              </svg>
            </span>
            <div class="empty-text">暂无教学材料！</div>
          </div>
          <div class="w-full mt-[8px] flex flex-row-reverse">
            <NPagination
              v-model:page="page"
              :item-count="pageCount"
              :on-update:page="updatePage"
              :on-update:page-size="updatePageSize"
              :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              show-quick-jumper
              show-size-picker
            />
          </div>
        </n-spin>
      </div>
      <template #header-extra>
        <img
          alt=""
          class="w-[27px] h-[28px] cursor-pointer"
          src="@/assets/toolboxPage/close.png"
          @click="showTap"
        />
      </template>
      <template #footer>
        <div class="flex justify-center items-center w-full">
          <NButton
            :disabled="flagBtn"
            type="info"
            @click="confirm(active.id, active.title, active.textbookType)"
          >
            选中教学材料
          </NButton>
        </div>
      </template>
    </NCard>
  </NModal>
</template>

<style lang="less" scoped>
.h-calc {
  height: calc(100vh - 200px);
  //height: 80%;
}

.ellipsis {
  white-space: nowrap; /*默认normal自动换行，nowrap是强制不换行*/
  overflow: hidden; /*溢出部分隐藏*/
  text-overflow: ellipsis; /*溢出部分用省略号显示，ellipsis就是省略号的意思*/
}

.up-shaow {
  box-shadow: 0 0 28px 0 #e7efff;
}

.up-border {
  border: 1px dashed rgb(224, 224, 230);
  cursor: pointer;
  transition: 0.25s;
}

.up-border:hover {
  border: 1px dashed #0264fa;
}

.kaichangbai {
  img {
    width: 74px;
    height: 74px;
    margin: 0 auto;
  }

  .title {
    height: 25px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 18px;
    color: #323233;
    letter-spacing: 0;
    text-align: center;
    margin-top: 19px;
    margin-bottom: 10px;
  }

  .des {
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #606266;
    letter-spacing: 0;
    text-align: center;
    //margin-bottom: 30px;
  }

  .openingQuestionrow {
    max-width: 752px;
    // background: #FFFFFF;
    margin: 0 auto;

    .openingQuestionitem {
      height: 46px;
      border: 1px solid #eaedf0;
      border-radius: 12px;
      display: flex;
      justify-content: space-between;
      background: #ffffff;
      align-items: center;
      padding-right: 21.72px;
      padding-left: 16px;
      margin-bottom: 12px;

      .context {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #323233;
        letter-spacing: 0;

        .openingQuestionicon {
          margin-right: 8.72px;
          width: 19px;
          height: 12px;
        }
      }

      img {
        width: 18px;
        height: 21px;
        transform: rotate(180deg);
      }
    }

    .openingQuestionitem:hover {
      cursor: pointer;
      border: 1px solid #125eff;
    }
  }
}

.sendicon {
  width: 40px;
  height: 40px;
  background: #d6d5de;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 21px;
    height: 21px;
  }
}

.send {
  background-image: linear-gradient(135deg, #fde6fe 0%, #c3f9ff 100%);
}

.send:hover {
  cursor: pointer;
}

.headpad {
  position: relative;
  //padding-top: 56px;
  height: auto;

  header {
    position: absolute;
    top: 0;
    left: 0;
  }
}

.leftbox {
  width: 50%;
}

.rightbox {
  width: 50%;
}

.headers {
  //height: 40px;
  //display: flex;
  //justify-content: space-between;
  // margin-bottom: 90px;
  .left {
    color: #323233;
    font-size: 20px;
    font-weight: 500;
    line-height: 0;
    letter-spacing: 0;
    line-height: 40px;
    display: flex;
    align-items: center;
    margin-left: 24px;

    .gohome {
      width: 40px;
      height: 40px;
      background: #fafbff;
      border: 1px solid #e9ecf3;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 11px;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }

    span {
      color: #909399;
      margin-left: 8px;
    }
  }
}

.documentbg {
  // width: 796px;
  width: 50%;
  height: 87vh;
  background: #ffffff;
  box-shadow: 0 0 10px 0 #00000021;
  border-radius: 4px;
  margin-left: 24px;
  margin-right: 24px;
  margin-top: 24px;
  margin-bottom: 24px;
  overflow-y: auto;
}

.autoheight {
  width: 50%;
  height: 85vh;
  padding-left: 24px;
  padding-right: 32px;
  /* 允许内容溢出时滚动 */
  overflow-y: auto;
  /* 隐藏 WebKit 浏览器的滚动条 */
  scrollbar-width: none;
  /* 隐藏 Firefox 的滚动条 */
  -ms-overflow-style: none;
}

.autoheight::-webkit-scrollbar {
  display: none;
}

/* 优化后的平滑动画效果 */
.slide-fade-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

// 应用卡片网格布局
.applicationrow {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(312px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

// 现代化应用卡片设计
.application-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #e8ecf0;
  border-radius: 16px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    background: #ffffff;
    border-color: #125eff;
    box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
    transform: translateY(-2px);
  }
}

.application-card-active {
  background: #ffffff;
  border-color: #125eff;
  box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
  transform: translateY(-2px);
}

.card-content {
  display: flex;
  align-items: center;
  padding: 20px;
  height: 80px;
}

.app-icon-wrapper {
  position: relative;
  margin-right: 20px;

  // 默认状态的微妙背景
  &::before {
    content: "";
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: linear-gradient(
      135deg,
      rgba(18, 94, 255, 0.05) 0%,
      rgba(30, 127, 255, 0.02) 100%
    );
    border-radius: 18px;
    opacity: 1;
    transition: all 0.3s ease;
  }

  .application-card:hover &::before {
    background: linear-gradient(
      135deg,
      rgba(18, 94, 255, 0.15) 0%,
      rgba(30, 127, 255, 0.08) 100%
    );
    transform: scale(1.05);
  }
}

.app-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  .application-card:hover & {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: scale(1.02);
  }
}

.app-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.app-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  line-height: 24px;
  margin: 0 0 12px 0;
  transition: color 0.2s ease;

  .application-card:hover & {
    color: #125eff;
  }
}

.app-meta {
  display: flex;
  align-items: center;
  gap: 20px;
}

.heat-info,
.collect-action {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #6b7280;
  transition: color 0.2s ease;

  .meta-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    opacity: 0.8;
  }
}

.collect-action {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(18, 94, 255, 0.08);
    color: #125eff;

    .meta-icon {
      opacity: 1;
    }
  }
}

.empty-state {
  width: 100%;
  min-height: 320px;
  padding: 48px 0;
  margin: 40px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border-radius: 24px;
  box-shadow: 0 4px 16px rgba(18, 94, 255, 0.08);

  .empty-svg {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    svg {
      display: block;
      width: 96px;
      height: 96px;
      opacity: 0.85;
    }
  }

  .empty-text {
    color: #7a8ca3;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    letter-spacing: 1px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .searchbox {
    width: 350px !important;
  }
}

@media (max-width: 768px) {
  .app {
    padding: 16px 20px;
  }

  .bothends {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .searchbox {
    width: 100% !important;
  }

  .applicationrow {
    grid-template-columns: 1fr;
  }

  .card-content {
    padding: 20px;
  }

  .section-title {
    font-size: 18px;
    margin-top: 24px;
  }
}

/deep/ .n-upload-dragger {
  border: 2px dashed #87adfe;
  border-radius: 12px;
  background: #f7fbff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-color: #125eff;
    box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
    transform: translateY(-2px);
  }
}

.dotBorder {
  border: 2px dashed #87adfe;
  border-radius: 12px;
  background: #f7fbff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border:2px dashed #125eff;
    box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
    transform: translateY(-2px);
  }
}
</style>
