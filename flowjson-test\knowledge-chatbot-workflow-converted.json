{"id": "knowledge-chatbot-workflow", "name": "知识检索聊天机器人", "description": "基于Dify工作流转换的知识检索+聊天机器人流程，支持从知识库检索信息并智能回答", "nodes": [{"id": "start-node", "type": "start", "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "description": "知识检索聊天机器人的起始点", "status": "idle", "config": {"variables": [{"name": "query", "type": "string", "description": "用户查询问题", "required": true, "defaultValue": ""}], "fileUpload": {"enabled": false, "allowedTypes": ["image"], "allowedExtensions": [".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG"], "maxFileSize": 15, "maxImageSize": 10, "maxCount": 3}}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": false}, {"id": "knowledge-retrieval-node", "type": "knowledge", "position": {"x": 400, "y": 200}, "data": {"label": "知识检索", "description": "从知识库中检索与用户问题相关的文本内容", "status": "idle", "config": {"database": "default", "searchType": "semantic", "maxResults": 5, "retrievalMode": "single", "singleOutput": true, "queryVariable": "{{query}}", "datasetIds": [], "model": {"provider": "openai", "name": "gpt-3.5-turbo", "mode": "chat", "completionParams": {"temperature": 0, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}}, "variables": [{"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": true}, {"id": "llm-answer-node", "type": "llm", "position": {"x": 700, "y": 200}, "data": {"label": "LLM节点", "description": "基于检索到的上下文信息回答用户问题", "status": "idle", "config": {"model": "gpt-3.5-turbo", "provider": "openai", "temperature": 0.7, "maxTokens": 512, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0, "prompt": "You are a helpful assistant. \nUse the following context as your learned knowledge, inside <context></context> XML tags.\n<context>\n{{#context#}}\n</context>\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.", "systemPrompt": "You are a helpful assistant that answers questions based on provided context.", "memory": {"enabled": false, "windowSize": 50, "rolePrefix": {"user": "", "assistant": ""}}, "context": {"enabled": true, "variable": "{{#knowledge-retrieval-node.result#}}"}, "vision": {"enabled": false}, "variables": [{"name": "context", "type": "string", "source": "knowledge-retrieval-node.result"}, {"name": "query", "type": "string", "source": "start-node.query"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": true}, {"id": "answer-node", "type": "end", "position": {"x": 1000, "y": 200}, "data": {"label": "结束节点", "description": "输出最终的智能回答结果", "status": "idle", "config": {"outputVariables": [{"name": "answer", "type": "string", "source": "llm-answer-node.text", "description": "基于知识库的智能回答"}, {"name": "context", "type": "string", "source": "knowledge-retrieval-node.result", "description": "检索到的相关上下文"}, {"name": "query", "type": "string", "source": "start-node.query", "description": "原始用户查询"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": false}], "edges": [{"id": "start-to-knowledge", "source": "start-node", "target": "knowledge-retrieval-node", "sourceHandle": "start-node-output", "targetHandle": "knowledge-retrieval-node-input", "animated": false, "style": {"stroke": "#64748b", "strokeWidth": 2}, "data": {"label": "查询输入"}}, {"id": "knowledge-to-llm", "source": "knowledge-retrieval-node", "target": "llm-answer-node", "sourceHandle": "knowledge-retrieval-node-output", "targetHandle": "llm-answer-node-input", "animated": false, "style": {"stroke": "#2f54eb", "strokeWidth": 2}, "data": {"label": "检索结果"}}, {"id": "llm-to-answer", "source": "llm-answer-node", "target": "answer-node", "sourceHandle": "llm-answer-node-output", "targetHandle": "answer-node-input", "animated": false, "style": {"stroke": "#64748b", "strokeWidth": 2}, "data": {"label": "智能回答"}}], "createdAt": "2024-01-15T10:30:00.000Z", "updatedAt": "2024-01-15T10:30:00.000Z", "metadata": {"originalSource": "dify-workflow", "version": "1.0.0", "convertedAt": "2024-01-15T10:30:00.000Z", "description": "从Dify工作流YAML转换而来的知识检索聊天机器人流程", "tags": ["知识检索", "聊天机器人", "RAG", "问答系统"], "features": {"fileUpload": false, "speechToText": false, "textToSpeech": false, "suggestedQuestions": false}, "originalConfig": {"mode": "advanced-chat", "icon": "📑", "iconBackground": "#EFF1F5"}}}