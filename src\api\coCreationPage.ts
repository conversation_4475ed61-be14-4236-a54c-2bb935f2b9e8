import { get, post } from '@/utils/request'


export function getcoCreationlistApi<T = any>(data: any) {
  return get<T>({
    url: '/eaide/intelligent_agent/list?status=1',
    data,
  })
}

// export function addagentApi<T = any>(data: any) {
//     return post<T>({
//       url: `/eaide/intelligent_agent`,
//       data,
//       headers: {
//         'Content-Type': 'application/json',
//     },
//     })
//   }
