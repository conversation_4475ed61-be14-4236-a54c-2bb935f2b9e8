<template>
  <!-- 右侧滑出变量管理面板 -->
  <div
    class="variable-panel-overlay"
    v-show="visible"
    @click="handleOverlayClick"
  >
    <div class="variable-panel" :class="{ 'panel-open': visible }" @click.stop>
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-content">
          <div class="panel-info">
            <div class="panel-name">变量管理</div>
          </div>
          <div class="header-actions">
            <button class="close-btn" @click="handleClose" title="关闭">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 面板内容 -->
      <div class="panel-content">
        <n-scrollbar style="max-height: calc(100vh - 120px);">
          <!-- 环境变量部分 -->
          <div class="config-section">
            <div class="section-header">
              <div class="rowstit">
                <span class="rowicon"></span>
                环境变量
              </div>
            </div>

            <div class="variable-description">
              环境变量是指在代码运行过程中设定的变量值，可以在流程运行过程中被引用和修改。变量将随流程一起保存到后端。
            </div>

            <div class="add-variable-btn-container">
              <n-button @click="addEnvironmentVariable" color="#125EFF">
                + 添加环境变量
              </n-button>
            </div>

            <!-- 环境变量表格 -->
            <div class="variable-table pt-[17px]" v-if="environmentVariables.length > 0">
              <div class="flex mb-[16px]">
                <div class="flex-1 variablePrompt">变量名</div>
                <div class="flex-1 variablePrompt">变量标识</div>
                <div class="flex-1 variablePrompt">变量值</div>
                <div class="flex-1 variablePrompt">变量类型</div>
                <div class="flex-1 variablePrompt"></div>
              </div>
              <div>
                <div
                  v-for="(variable, index) in environmentVariables"
                  :key="variable.id || index"
                  class="flex mb-[16px] align-center"
                >
                  <div class="flex-1 variablerow">
                    {{ variable.name }}
                  </div>
                  <div class="flex-1 variablerow">
                    {{ variable.code }}
                  </div>
                  <div class="flex-1 variablerow">
                    {{ variable.value }}
                  </div>
                  <div class="flex-1 variablerow">
                    {{ variable.type }}
                  </div>
                  <div class="flex-1 variablerow flex justify-start items-center">
                      <img
                        @click="editVariable(variable)"
                        class="action-icon delete-icon ml-[16px] w-[14px] h-[14px]"
                        src="@/assets/agentOrchestration/editIcon.png"
                        title="删除"
                      >
                      <img
                        @click="removeEnvironmentVariable(variable)"
                        class="action-icon delete-icon ml-[16px]"
                        src="@/assets/agentOrchestration/delIcon2.png"
                        title="删除"
                      >
                  </div>
                </div>
              </div>
            </div>
          </div>


          <!-- 节点变量部分 -->
          <div class="config-section">
            <div class="section-header">
              <div class="rowstit">
                <span class="rowicon"></span>
                节点变量
              </div>
            </div>

            <!-- 节点变量展开列表 -->
            <div class="node-variables">
              <div
                v-for="(nodeVar, index) in nodeVariables"
                :key="index"
                class="node-variable-group"
              >
                <div  @click="toggleNodeExpanded(index)">
                  <div class="node-info cursor-pointer">
                    <img class="expand-icon w-[16px] h-[16px] "   :class="{ expanded: nodeVar.expanded }" src="@/assets/agentOrchestration/rightIcon.png">
                    <span class="node-name ">{{ nodeVar.nodeName }}</span>
                  </div>
                </div>

                <div v-if="nodeVar.expanded" class="node-variable-content">
                  <div class="node-table">
                    <div class="flex mb-[16px] mt-[16px]">
                      <div class="flex-1 nodevariablePrompt text-left">变量名</div>
                      <div class="flex-1 nodevariablePrompt text-left">变量标识</div>
                      <div class="flex-1 nodevariablePrompt text-left">变量类型</div>
                    </div>
                    <div>
                      <div
                        v-for="(variable, varIndex) in nodeVar.variables"
                        :key="varIndex"
                        class="flex mb-[16px] align-center"
                      >
                        <div class="flex-1 nodevariablerow">
                         {{ variable.name }}
                        </div>
                        <div class="flex-1 nodevariablerow">
                         {{ variable.value || '' }}
                        </div>
                        <div class="flex-1 nodevariablerow">
                         {{ variable.type }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </n-scrollbar>
      </div>

      <!-- 面板底部 -->
      <div class="panel-footer">
        <div class="footer-actions">
          <n-button @click="handleClose">取消</n-button>
          <n-button type="info" color="#125EFF" @click="handleSave">保存</n-button>
        </div>
      </div>
    </div>
  </div>

  <!-- 变量编辑弹窗 -->
  <NModal v-model:show="showVariableModal">
    <NCard
      style="width: 500px"
      :title="isEditMode ? '编辑变量' : '新增变量'"
      :bordered="false"
      size="huge"
    >
      <NForm
        ref="variableFormRef"
        :model="variableForm"
        :rules="variableRules"
        label-placement="left"
        label-width="80px"
      >
        <NFormItem label="变量名" path="name">
          <NInput
            v-model:value="variableForm.name"
            placeholder="请输入变量名"
          />
        </NFormItem>
        <NFormItem label="变量标识" path="code">
          <NInput
            v-model:value="variableForm.code"
            placeholder="请输入变量标识"
          />
        </NFormItem>
        <NFormItem label="变量值" path="value">
          <NInput
            v-model:value="variableForm.value"
            placeholder="请输入变量值"
          />
        </NFormItem>
        <NFormItem label="变量类型" path="type">
          <NSelect
            v-model:value="variableForm.type"
            :options="typeOptions"
            placeholder="请选择变量类型"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <div class="modal-footer">
          <NButton @click="cancelVariableEdit">取消</NButton>
          <NButton type="info" color="#125EFF" @click="saveVariable">
            {{ isEditMode ? '保存' : '新增' }}
          </NButton>
        </div>
      </template>
    </NCard>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import {
  NButton,
  NInput,
  NSelect,
  NScrollbar,
  NModal,
  NCard,
  NForm,
  NFormItem,
  NTabs,
  NTabPane,
  NSpace,
  NTag,
  NPopconfirm,
  useMessage,
} from "naive-ui";
import { useOrchestrationStore } from '@/store'
import type { FlowVariable } from '@/types/backend'

const props = defineProps<{
  show: boolean;
}>();

const emit = defineEmits<{
  "update:show": [value: boolean];
  save: [variables: FlowVariable[]];
}>();

const message = useMessage();
const orchestrationStore = useOrchestrationStore();

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

watch(visible,()=>{
  if(visible.value){
    // 获取所有节点变量
    const allNodeVariables = orchestrationStore.getVariablesByType('nodeVariable');
    
    // 按 nodeId 分组
    const groupedVariables = allNodeVariables.reduce((groups: Record<string, any>, variable) => {
      const nodeId = variable.nodeId || '';
      if (!groups[nodeId]) {
        groups[nodeId] = {
          nodeId: nodeId,
          nodeName: variable.nodeName || '未知节点',
          nodeType: variable.nodeType || 'unknown',
          expanded: false,
          variables: []
        };
      }
      
      groups[nodeId].variables.push({
        id: variable.id,
        name: variable.name,
        code: variable.code, 
        value: variable.value || '',
        type: getDataTypeLabel(variable.valueType),
        nodeId: variable.nodeId,
        nodeName: variable.nodeName,
        nodeType: variable.nodeType,
      });
      
      return groups;
    }, {});
    
    // 转换为数组格式
    nodeVariables.value = Object.values(groupedVariables);
  }
})

// 变量类型选项（保持原有格式）
const typeOptions = [
  { label: "文本", value: "文本" },
  { label: "数字", value: "数字" },
  { label: "布尔", value: "布尔" },
  { label: "数组", value: "数组" },
  { label: "对象", value: "对象" }
];

// 获取环境变量列表（保持原有表格格式）
const environmentVariables = computed(() => {
  return orchestrationStore.getVariablesByType('envVariable').map(variable => ({
    id: variable.id,
    name: variable.name,
    code: variable.code, 
    value: variable.value || '',
    type: getDataTypeLabel(variable.valueType),
    originalVariable: variable // 保存原始变量对象用于编辑
  }));
});
// 获取数据类型标签文本
const getDataTypeLabel = (valueType: string) => {
  const labelMap: Record<string, string> = {
    string: '文本',
    number: '数字',
    boolean: '布尔',
    array: '数组',
    object: '对象'
  };
  return labelMap[valueType] || valueType;
};

// 获取数据类型值
const getDataTypeValue = (label: string): 'string' | 'number' | 'boolean' | 'array' | 'object' => {
  const valueMap: Record<string, 'string' | 'number' | 'boolean' | 'array' | 'object'> = {
    '文本': 'string',
    '数字': 'number',
    '布尔': 'boolean',
    '数组': 'array',
    '对象': 'object'
  };
  return valueMap[label] || 'string';
};

// 变量编辑弹窗相关
const showVariableModal = ref(false);
const isEditMode = ref(false);
const editingVariableId = ref<string>('');
const variableFormRef = ref();

// 变量表单数据（保持原有格式）
const variableForm = ref({
  name: "",
  code: "",
  value: "",
  type: "文本"
});

// 表单验证规则
const variableRules = {
  name: {
    required: true,
    message: "请输入变量名",
    trigger: "blur"
  },
  code: {
    required: true,
    message: "请输入变量标识",
    trigger: "blur"
  },
  type: {
    required: true,
    message: "请选择变量类型",
    trigger: "change"
  }
};

// 处理遮罩层点击
const handleOverlayClick = () => {
  handleClose();
};

// 关闭面板
const handleClose = () => {
  visible.value = false;
};

// 添加环境变量
const addEnvironmentVariable = () => {
  isEditMode.value = false;
  variableForm.value = {
    name: "",
    code: "",
    value: "",
    type: "文本"
  };
  showVariableModal.value = true;
};

// 编辑变量
const editVariable = (variable: any) => {
  console.log(variable);
  isEditMode.value = true;
  editingVariableId.value = variable.id;
  variableForm.value = {
    name: variable.name,
    code: variable.code,
    value: variable.value,
    type: variable.type
  };
  showVariableModal.value = true;
};

// 删除环境变量
const removeEnvironmentVariable = (variable: any) => {
  try {
    orchestrationStore.deleteVariable(variable.id);
    message.success("变量删除成功");
  } catch (error) {
    message.error(error instanceof Error ? error.message : "删除失败");
  }
};

// 保存变量
const saveVariable = async () => {
  try {
    await variableFormRef.value?.validate();

    if (isEditMode.value) {
      // 编辑模式
      orchestrationStore.updateVariable(editingVariableId.value, {
        name: variableForm.value.name,
        code: variableForm.value.code,
        valueType: getDataTypeValue(variableForm.value.type),
        value: variableForm.value.value
      });
      message.success("变量编辑成功");
    } else {
      // 新增模式
      orchestrationStore.addVariable({
        name: variableForm.value.name,
        type: 'envVariable', // 默认为环境变量
        code: variableForm.value.code,
        valueType: getDataTypeValue(variableForm.value.type),
        value: variableForm.value.value,
        readonly: true
      });
      message.success("变量新增成功");
    }

    showVariableModal.value = false;
  } catch (error) {
    message.error(error instanceof Error ? error.message : "操作失败");
  }
};

// 取消编辑
const cancelVariableEdit = () => {
  showVariableModal.value = false;
};

// 保存变量管理面板
const handleSave = () => {
  // 这里可以添加保存逻辑
  message.success("变量管理已保存");
  handleClose();
};

// 节点变量数据（保留原有功能）
const nodeVariables = ref();

// 切换节点展开状态（保留原有功能）
const toggleNodeExpanded = (index: number) => {
  nodeVariables.value[index].expanded = !nodeVariables.value[index].expanded;
};
</script>

<script lang="ts">
export default {
  name: 'VariableManagementPanel'
}
</script>

<style scoped lang="less">
.variable-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.variable-panel {
  position: fixed;
  top: 0;
  right: -520px;
  width: 520px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.variable-panel.panel-open {
  right: 0;
}

.panel-header {
  height: 56px;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.panel-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.panel-name {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #1a202c;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  padding: 24px;
}

.config-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.variable-description {
  font-size: 13px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

.add-variable-btn-container {
  width: 130px;
height: 33px;
border-radius: 7px;
  margin-bottom: 16px;

  :deep(.n-button) {
    width: 100%;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
  }
}

.variable-table {
  overflow: hidden;
  background: #F9F9F9;
  border-radius: 8px;
  padding-left: 14px;
  .variablePrompt{
    height: 14px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #C7C7C7;
    line-height: 14px;
    text-align: left;
  }
  .variablerow{
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #565756;
    letter-spacing: 0;
    text-align: left;
    line-height: 22px;
  }
}
.nodevariablePrompt{
    height: 14px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #C7C7C7;
    line-height: 14px;
    text-align: left;
  }
  .nodevariablerow{
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #565756;
    letter-spacing: 0;
    text-align: left;
    line-height: 22px;
  }
.table-header {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.header-cell {
  flex: 1;
  padding: 12px 16px;
  font-size: 13px;
  font-weight: 400;
  color: #c7c7c7;
  border-right: 1px solid #e2e8f0;
}

.header-cell:last-child {
  border-right: none;
}

.action-header {
  flex: 0 0 80px;
  text-align: center;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #f0f0f0;
  font-size: 14px;
  color: #565756;
  min-height: 48px;
}

.table-cell:last-child {
  border-right: none;
}

.action-cell {
  flex: 0 0 80px;
  justify-content: center;
}

.cell-text {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #565756;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-input {
  width: 100%;

  :deep(.n-input__input-el) {
    border: 1px solid #e2e8f0;
    background: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
  }

  :deep(.n-input__border) {
    display: none;
  }

  :deep(.n-input__state-border) {
    display: none;
  }
}

.table-select {
  width: 100%;

  :deep(.n-base-selection) {
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 4px;
    min-height: 28px;
  }

  :deep(.n-base-selection-label) {
    padding: 4px 8px;
    font-size: 14px;
  }
}

.delete-icon,
.action-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
}

.delete-icon:hover,
.action-icon:hover {
  opacity: 0.7;
}

.edit-icon {
  color: #1890ff;
}

.save-icon {
  color: #52c41a;
}

.edit-icon:hover {
  color: #40a9ff;
}

.save-icon:hover {
  color: #73d13d;
}

.node-variables {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-variable-group {
  // border: 1px solid #e2e8f0;
  // border-radius: 8px;
  overflow: hidden;
  background: white;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  cursor: pointer;
  transition: background 0.2s ease;
  border-bottom: 1px solid #e2e8f0;
}

.node-header:hover {
  background: #f1f5f9;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 22px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #000000;
  letter-spacing: 0;
}

.node-icon {
  width: 18px;
  height: 18px;
}

.expand-control {
  display: flex;
  align-items: center;
}

.expand-icon {
  transition: transform 0.2s ease;
  color: #666;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.node-name {
  font-weight: 500;
  color: #1a202c;
}

.node-variable-content {
  padding: 0;
}

.node-table {
  border: none;
  border-radius: 0;
  margin-top: 0;
}

.node-table .table-header {
  background: #fafbfc;
}

.node-table .header-cell {
  color: #c7c7c7;
  font-size: 13px;
}

.panel-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background: white;
  flex-shrink: 0;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  :deep(.n-button) {
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    min-height: 36px;
    min-width: 88px;

    &:not([type]) {
      border: 1px solid #e0e0e0;
      background: #ffffff;
      color: #666666;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #125EFF;
        color: #125EFF;
        box-shadow: 0 2px 6px rgba(18, 94, 255, 0.15);
      }
    }

    &[type="info"] {
      box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
      }
    }
  }
}

.divider {
  width: 100%;
  height: 0.92px;
  background: #0000000f;
  margin-bottom: 20px;
}

.rowstit {
  display: flex;
  align-items: center;
  height: 22px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #000000e0;
  
  .rowicon {
    width: 5px;
    height: 16px;
    background: #abc6ff;
    background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
    border-radius: 3px;
    margin-right: 9px;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.modal-footer :deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  min-height: 36px;
  min-width: 88px;

  &:not([type]) {
    border: 1px solid #e0e0e0;
    background: #ffffff;
    color: #666666;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &:hover {
      border-color: #125EFF;
      color: #125EFF;
      box-shadow: 0 2px 6px rgba(18, 94, 255, 0.15);
    }
  }

  &[type="info"] {
    box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
    }
  }
}
</style>
