<template>
	<div
		v-if="visible"
		:style="{ left: position.x + 'px', top: position.y + 'px' }"
		class="pdf-context-menu fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-48"
		@click.stop
	>
		<!--		<div class="px-3 py-2 text-sm text-gray-500 border-b border-gray-100">-->
		<!--			已选择文本:-->
		<!--			{{-->
		<!--				selectedText.length > 20-->
		<!--					? selectedText.substring(0, 20) + "..."-->
		<!--					: selectedText-->
		<!--			}}-->
		<!--		</div>-->

		<div class="flex px-[10px] items-center">
			<div class="flex items-center cursor-pointer btn-hover" @click="handleAddNote">
				<div class="relative w-[12px] h-[12px] mr-1">
					<img alt="" class="w-[12px] h-[12px]  active" src="@/assets/toolboxPage/bja.png">
					<img alt="" class="w-[12px] h-[12px]  defaults" src="@/assets/toolboxPage/bjd.png">
				</div>
				<p>标记疑难点</p>
			</div>
			<div class="w-[1px] h-[17px] bg-[#E3E3E3] mx-[10px]"></div>
			<div class="flex items-center cursor-pointer btn-hover" @click="handleAiExplain">
				<div class="relative w-[12px] h-[12px] mr-1">
					<img alt="" class="w-[12px] h-[12px]  active" src="@/assets/toolboxPage/aia.png">
					<img alt="" class="w-[12px] h-[12px]  defaults" src="@/assets/toolboxPage/ai.png">
				</div>
				<p>向AI提问</p>
			</div>
		</div>


		<!--		<div class="menu-item-group">-->
		<!--			<button-->
		<!--				class="menu-item flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"-->
		<!--				@click="handleHighlight"-->
		<!--			>-->
		<!--				<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">-->
		<!--					<path-->
		<!--						d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"-->
		<!--					/>-->
		<!--				</svg>-->
		<!--				高亮标注-->
		<!--			</button>-->

		<!--			<button-->
		<!--				class="menu-item flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"-->
		<!--				@click="handleAddNote"-->
		<!--			>-->
		<!--				<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">-->
		<!--					<path-->
		<!--						d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"-->
		<!--					/>-->
		<!--					<path d="M6 8h8M6 10h8M6 12h4"/>-->
		<!--				</svg>-->
		<!--				添加注释-->
		<!--			</button>-->

		<!--			<button-->
		<!--				class="menu-item flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"-->
		<!--				@click="handleAiExplain"-->
		<!--			>-->
		<!--				<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">-->
		<!--					<path-->
		<!--						d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"-->
		<!--					/>-->
		<!--				</svg>-->
		<!--				AI解释-->
		<!--			</button>-->

		<!--			<div class="border-t border-gray-100 my-1"/>-->

		<!--			<button-->
		<!--				class="menu-item flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"-->
		<!--				@click="handleCopy"-->
		<!--			>-->
		<!--				<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">-->
		<!--					<path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>-->
		<!--					<path-->
		<!--						d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"-->
		<!--					/>-->
		<!--				</svg>-->
		<!--				复制文本-->
		<!--			</button>-->

		<!--			<button-->
		<!--				class="menu-item flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"-->
		<!--				@click="handleSearch"-->
		<!--			>-->
		<!--				<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">-->
		<!--					<path-->
		<!--						clip-rule="evenodd"-->
		<!--						d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"-->
		<!--						fill-rule="evenodd"-->
		<!--					/>-->
		<!--				</svg>-->
		<!--				搜索相关内容-->
		<!--			</button>-->
		<!--		</div>-->
	</div>
</template>

<script lang="ts" setup>
import {ref, computed} from "vue";

interface Position {
	x: number;
	y: number;
}

interface Props {
	visible: boolean;
	position: Position;
	selectedText: string;
	selectionData?: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
	(e: "highlight", data: { text: string; color: string; position: any }): void;
	(e: "addNote", data: { text: string; note: string; position: any }): void;
	(e: "aiExplain", data: { text: string; note: string; position: any }): void;
	(e: "copy", text: string): void;
	(e: "search", text: string): void;
	(e: "close"): void;
}>();

// 高亮颜色选项
const highlightColors = [
	{name: "黄色", color: "#FDE047"},
	{name: "绿色", color: "#4ADE80"},
	{name: "蓝色", color: "#60A5FA"},
	{name: "红色", color: "#F87171"},
	{name: "紫色", color: "#A78BFA"}
];

const handleHighlight = () => {
	// 默认使用黄色高亮，后续可以扩展颜色选择
	emit("highlight", {
		text: props.selectedText,
		color: highlightColors[0].color,
		position: props.selectionData
	});
	emit("close");
};

const handleAddNote = () => {
	// 直接发射事件，让父组件处理注释输入
	emit("addNote", {
		text: props.selectedText,
		note: "", // 空字符串，父组件会显示输入模态框
		position: props.selectionData
	});
	emit("close");
};

const handleAiExplain = () => {
	emit("aiExplain", {
		text: props.selectedText,
		note: "", // 空字符串，父组件会显示输入模态框
		position: props.selectionData
	});
	emit("close");
};

const handleCopy = async () => {
	try {
		await navigator.clipboard.writeText(props.selectedText);
		// 可以添加复制成功的提示
		console.log("文本已复制到剪贴板");
	} catch (err) {
		console.error("复制失败:", err);
		// 降级方案
		const textArea = document.createElement("textarea");
		textArea.value = props.selectedText;
		document.body.appendChild(textArea);
		textArea.select();
		document.execCommand("copy");
		document.body.removeChild(textArea);
	}
	emit("copy", props.selectedText);
	emit("close");
};

const handleSearch = () => {
	emit("search", props.selectedText);
	emit("close");
};
</script>

<style scoped>
.btn-hover:hover p {
	color: #125EFF;
}

.btn-hover:hover .active {
	//display: block;
	opacity: 1;
}

.btn-hover:hover .defaults {
	//display: none;
	opacity: 0;
}

.active {
	opacity: 0;
}

.btn-hover p {
	transition: 0.25s;
}

.btn-hover img {
	transition: 0.25s;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.pdf-context-menu {
	user-select: none;
	backdrop-filter: blur(8px);
	background-color: rgba(255, 255, 255, 0.95);
}

.menu-item {
	transition: background-color 0.15s ease-in-out;
}

.menu-item:hover {
	background-color: #f3f4f6;
}

.menu-item:active {
	background-color: #e5e7eb;
}

.menu-item svg {
	flex-shrink: 0;
}
</style>
