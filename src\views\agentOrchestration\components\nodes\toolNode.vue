<template>
  <div class="tool-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />

    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/toolIcon.png" alt="工具">
        <div class="node-title">{{ data.label || '工具' }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>

      <!-- 工具配置 -->
      <div class="tool-config">
        <div class="config-item">
          <span class="config-label">工具:</span>
          <span class="config-value">{{ data.config?.toolName || '工具名称' }}</span>
        </div>
        <div class="config-item" v-if="data.config?.action">
          <span class="config-label">操作:</span>
          <span class="config-value">{{ data.config.action }}</span>
        </div>
      </div>
    </div>

    <!-- 输出连接点 -->
    <Handle
      type="source"
      :position="Position.Right"
      :id="`${id}-output`"
      class="output-handle"
    />

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'

interface ToolNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      toolName?: string
      action?: string
      parameters?: any
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<ToolNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})

</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.tool-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .tool-config {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .config-item {
        .node-list-item-style();

        .config-label {
          font-size: 11px;
          color: #6b7280;
          font-weight: 500;
        }

        .config-value {
          font-size: 11px;
          color: #9ca3af;
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
