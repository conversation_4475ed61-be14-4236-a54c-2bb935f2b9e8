<script lang="ts" setup>
import {
	computed,
	defineExpose,
	nextTick,
	onMounted,
	onUnmounted,
	ref,
	watch,
	withDefaults,
} from "vue";
import * as pdfjsLib from "pdfjs-dist";
import PdfContextMenu from "./pdfMark/PdfContextMenu.vue";
import AiExplanationModal from "./pdfMark/AiExplanationModal.vue";
import AnnotationTooltip from "./pdfMark/AnnotationTooltip.vue";
import NoteInputModal from "./pdfMark/NoteInputModal.vue";
import {addTextbookAnnotations} from "@/api/courseware";
import { NButton, NInputNumber } from 'naive-ui'
import { SvgIcon } from '@/components/common'

const props = withDefaults(
	defineProps<{
		pdfUrl?: string;
		main?: any;
		enableLog?: boolean;
		enableHighlight?: boolean;
		showContextMenu?: boolean;
		forbid?: boolean;
		keywords?: { text: string; color: string }[];
		externalAnnotations?: ExternalAnnotation[]; // 外部标注数据
	}>(),
	{
		pdfUrl:
			"https://cimg.930703.xyz/file/1755253195057_AI%20Engineering%20Building%20Applications%20with%20Foundation%20Models%20(Chip%20Huyen)%20(Z-Library).pdf?from=admin",
		enableLog: false,
		enableHighlight: false,
		showContextMenu: true,
		keywords: () => [
			{text: "审核", color: "#FDE047"},
			{text: "添加测试账号", color: "#4ADE80"},
			{text: "app", color: "#FF5733"},
		],
		externalAnnotations: () => [],
	}
);

const emit = defineEmits<{
	(e: "pageChange", page: number): void;
	(e: "keywordClick", keyword: string): void;
	(e: "textSelected", text: string): void;
	(e: "menuAction", action: string, data?: any): void;
	(
		e: "highlightAdded",
		data: { text: string; color: string; position: any; id: string }
	): void;
	(
		e: "noteAdded",
		data: {
			content: string;
			note: string;
			position: any;
			id?: string;
			page: any;
			color: string;
		}
	): void;
	(e: "aiExplainRequested", annotation: any): void;
	(e: "textCopied", text: string): void;
	(e: "searchRequested", text: string): void;
	(e: "delete", id: string): void;
	(e: "edit", data: any): void;
	(e: "maxPageChange", data?: any): void;
}>();

// 配置PDF.js worker
import workerUrl from "./pdf.worker.min.js?url";

// 设置worker配置
try {
	pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrl;
	console.log('PDF.js worker配置成功:', workerUrl);
} catch (error) {
	// 降级到CDN worker
	console.warn('Worker配置失败，使用CDN worker:', error);
	pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cimg.930703.xyz/file/1744109010421_pdf.worker.min.mjs';
}

interface ExternalAnnotation {
	id: string;
	text: string;
	note: string;
	color: string;
	page: number;
	position: {
		startX: number;
		startY: number;
		endX: number;
		endY: number;
	};
	createdAt: Date;
	updatedAt: Date;
	readonly?: boolean; // 只读标注，不可编辑删除
}

const isLoading = ref(true); // 添加加载状态
const loadingProgress = ref(0);
const loadingStageText = ref("正在初始化..."); // 添加加载阶段描述

// 渲染队列管理（借鉴官方view.js）
const pageRendering = ref(false);
const pageNumPending = ref<number | null>(null);

// IndexedDB缓存管理
const DB_NAME = 'PDFViewerCache';
const DB_VERSION = 1;
const CACHE_STORE = 'pdfCache';
const RENDER_STORE = 'renderCache';
let db: IDBDatabase | null = null;

// 页面预加载管理
const preloadedPages = new Map<number, any>();
const renderCache = new Map<string, ImageData>();
const maxCacheSize = 10; // 最大缓存页面数

// 真实加载进度跟踪
const realLoadingProgress = ref(0);
const loadingStageText_real = ref("正在初始化...");

// 性能监控
const performanceMetrics = ref({
  loadStartTime: 0,
  loadEndTime: 0,
  renderStartTime: 0,
  renderEndTime: 0,
  cacheHits: 0,
  cacheMisses: 0,
  preloadedPagesCount: 0,
  totalRenderTime: 0
});

// 缓存管理
const cacheManager = {
  // 获取缓存统计
  getStats: () => ({
    preloadedPages: preloadedPages.size,
    renderCache: renderCache.size,
    cacheHits: performanceMetrics.value.cacheHits,
    cacheMisses: performanceMetrics.value.cacheMisses
  }),

  // 清除所有缓存
  clearAll: () => {
    preloadedPages.clear();
    renderCache.clear();
    performanceMetrics.value.cacheHits = 0;
    performanceMetrics.value.cacheMisses = 0;
    log('所有缓存已清除');
  },

  // 清除过期缓存
  clearExpired: () => {
    // 清理预加载页面缓存（保留最近使用的）
    if (preloadedPages.size > maxCacheSize) {
      const keysToDelete = Array.from(preloadedPages.keys()).slice(0, preloadedPages.size - maxCacheSize);
      keysToDelete.forEach(key => preloadedPages.delete(key));
    }

    // 清理渲染缓存（保留最近使用的）
    if (renderCache.size > maxCacheSize) {
      const keysToDelete = Array.from(renderCache.keys()).slice(0, renderCache.size - maxCacheSize);
      keysToDelete.forEach(key => renderCache.delete(key));
    }

    log('过期缓存清理完成');
  }
};

// 加载阶段描述信息数组（保留原有的，用于兼容）
const loadingStages = [
  { progress: 0, text: "正在初始化..." },
  { progress: 10, text: "正在连接服务器..." },
  { progress: 25, text: "正在下载PDF文件..." },
  { progress: 40, text: "正在解析文件结构..." },
  { progress: 60, text: "正在处理页面内容..." },
  { progress: 80, text: "正在优化显示效果..." },
  { progress: 95, text: "即将完成加载..." },
  { progress: 100, text: "加载完成！" }
];
let progressInterval: number | null = null;
const pdfCanvas = ref<HTMLCanvasElement | null>(null);
const textLayer = ref<HTMLDivElement | null>(null);
const currentPage = ref(1);
const currentPageInput = ref(1);
const totalPages = ref(0);
let pdfDoc: pdfjsLib.PDFDocumentProxy | null = null;
// 添加渲染任务跟踪
let currentRenderTask: any = null;

// 文本选择相关变量
const selectedText = ref("");
const selectionData = ref<any>(null);
const isTextSelected = ref(false);

// 上下文菜单相关变量
const contextMenuVisible = ref(false);
const contextMenuPosition = ref({x: 0, y: 0});

// AI解释模态框相关变量
const aiModalVisible = ref(false);
const aiSelectedText = ref("");

// 注释tooltip相关变量
const annotationTooltipVisible = ref(false);
const selectedAnnotation = ref<any>(null);
const annotationTooltipPosition = ref({x: 0, y: 0});

// 注释输入相关变量
const noteInputVisible = ref(false);
const noteSelectedText = ref("");
const pendingNoteData = ref<any>(null);

// 选中文本高亮效果
const selectedTextHighlight = ref({
	visible: false,
	style: {},
});

// 当前页面的文本内容缓存
let currentPageTextContent: any = null;

// 添加高亮区域记录
const highlightedAreas = ref<
	{
		x: number;
		y: number;
		width: number;
		height: number;
		keyword: string;
	}[]
>([]);

// 用户标注记录
const userAnnotations = ref<
	{
		id?: string;
		text: string;
		note?: string;
		color?: string;
		page: number;
		position: {
			startX: number;
			startY: number;
			endX: number;
			endY: number;
		};
		createdAt?: Date;
		updatedAt?: Date;
	}[]
>([]);
// 默认关键词
const defaultKeywords = [
	{text: "审核", color: "#FDE047"},
	{text: "添加测试账号", color: "#4ADE80"},
	{text: "app", color: "#FF5733"},
];
// 关键词
const keywords = ref<{ text: string; color: string }[]>(
	props.keywords || defaultKeywords
);

// 添加日志工具函数
const log = (message: string, ...args: any[]) => {
	if (props.enableLog) console.log(message, ...args);
};

// 文本选择状态
const textSelectionRange = ref<Range | null>(null);

// 初始化IndexedDB
const initIndexedDB = async (): Promise<void> => {
	return new Promise((resolve, reject) => {
		const request = indexedDB.open(DB_NAME, DB_VERSION);

		request.onerror = () => reject(request.error);
		request.onsuccess = () => {
			db = request.result;
			resolve();
		};

		request.onupgradeneeded = (event) => {
			const database = (event.target as IDBOpenDBRequest).result;

			// 创建PDF缓存存储
			if (!database.objectStoreNames.contains(CACHE_STORE)) {
				database.createObjectStore(CACHE_STORE, { keyPath: 'url' });
			}

			// 创建渲染缓存存储
			if (!database.objectStoreNames.contains(RENDER_STORE)) {
				database.createObjectStore(RENDER_STORE, { keyPath: 'key' });
			}
		};
	});
};

// 从IndexedDB获取缓存的PDF数据
const getCachedPDF = async (url: string): Promise<ArrayBuffer | null> => {
	if (!db) return null;

	return new Promise((resolve) => {
		const transaction = db!.transaction([CACHE_STORE], 'readonly');
		const store = transaction.objectStore(CACHE_STORE);
		const request = store.get(url);

		request.onsuccess = () => {
			resolve(request.result?.data || null);
		};
		request.onerror = () => resolve(null);
	});
};

// 缓存PDF数据到IndexedDB
const cachePDF = async (url: string, data: ArrayBuffer): Promise<void> => {
	if (!db) return;

	return new Promise((resolve, reject) => {
		try {
			const transaction = db!.transaction([CACHE_STORE], 'readwrite');
			const store = transaction.objectStore(CACHE_STORE);
			const request = store.put({ url, data, timestamp: Date.now() });

			request.onsuccess = () => resolve();
			request.onerror = () => reject(request.error);
		} catch (error) {
			console.warn('Failed to cache PDF:', error);
			reject(error);
		}
	});
};

const init = async (url: any) => {
	// 定义pdfUrl在函数顶部，确保在catch块中可以访问
	const pdfUrl = url || props.pdfUrl || "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf";

	try {
		// 开始性能监控
		performanceMetrics.value.loadStartTime = performance.now();

		isLoading.value = true;
		loadingProgress.value = 0;
		realLoadingProgress.value = 0;
		loadingStageText.value = "正在初始化...";

		// 清除之前的定时器
		if (progressInterval) clearInterval(progressInterval);

		// 异步初始化IndexedDB（不阻塞主流程）
		initIndexedDB().catch(error => {
			console.warn('IndexedDB初始化失败，将跳过缓存功能:', error);
		});

		log(`开始加载PDF: ${pdfUrl}`);

		// 验证PDF URL
		try {
			const testResponse = await fetch(pdfUrl, { method: 'HEAD' });
			log(`PDF URL响应状态: ${testResponse.status}`);
			log(`PDF URL内容类型: ${testResponse.headers.get('content-type')}`);

			if (!testResponse.ok) {
				throw new Error(`HTTP ${testResponse.status}: ${testResponse.statusText}`);
			}

			const contentType = testResponse.headers.get('content-type');
			if (contentType && !contentType.includes('pdf') && !contentType.includes('application/octet-stream')) {
				console.warn(`警告: 内容类型可能不是PDF: ${contentType}`);
			}
		} catch (error) {
			console.warn('PDF URL预检查失败:', error);
			log(`PDF URL预检查失败: ${error}`);
		}

		// 尝试从缓存获取PDF数据（如果IndexedDB可用）
		let cachedData = null;
		try {
			if (db) {
				loadingStageText.value = "检查本地缓存...";
				cachedData = await getCachedPDF(pdfUrl);
			}
		} catch (error) {
			console.warn('缓存检查失败，将从网络加载:', error);
		}

		let loadingTask;
		if (cachedData) {
			loadingStageText.value = "从缓存加载PDF...";
			performanceMetrics.value.cacheHits++;
			log('PDF从缓存加载');

			// 验证缓存数据
			if (cachedData.byteLength < 100) {
				throw new Error('缓存的PDF数据太小，可能已损坏');
			}

			loadingTask = pdfjsLib.getDocument({
				data: cachedData,
				// 启用流式加载优化
				disableAutoFetch: false,
				disableStream: false,
			});
		} else {
			loadingStageText.value = "正在下载PDF文件...";
			performanceMetrics.value.cacheMisses++;
			log('PDF从网络加载');

			loadingTask = pdfjsLib.getDocument({
				url: pdfUrl,
				// 启用流式加载优化
				disableAutoFetch: false,
				disableStream: false,
				// 添加更多配置选项
				httpHeaders: {
					'Accept': 'application/pdf,*/*',
				},
				withCredentials: false,
			});
		}

		// 监听真实加载进度
		loadingTask.onProgress = (progressData: any) => {
			try {
				if (progressData && progressData.total > 0) {
					const progress = Math.round((progressData.loaded / progressData.total) * 100);
					realLoadingProgress.value = progress;
					loadingProgress.value = progress;

					if (progress < 30) {
						loadingStageText.value = "正在下载PDF文件...";
					} else if (progress < 60) {
						loadingStageText.value = "正在解析文件结构...";
					} else if (progress < 90) {
						loadingStageText.value = "正在处理页面内容...";
					} else {
						loadingStageText.value = "即将完成加载...";
					}

					log(`加载进度: ${progress}% (${progressData.loaded}/${progressData.total})`);
				}
			} catch (error) {
				console.warn('进度监听出错:', error);
			}
		};

		pdfDoc = await loadingTask.promise;
		log('PDF文档解析完成');

		// 异步缓存PDF数据（不阻塞主流程）
		if (!cachedData && db) {
			// 简化缓存逻辑，避免重复下载
			setTimeout(async () => {
				try {
					const response = await fetch(pdfUrl);
					if (response.ok) {
						const arrayBuffer = await response.arrayBuffer();
						await cachePDF(pdfUrl, arrayBuffer);
						log('PDF数据已缓存到本地');
					}
				} catch (error) {
					console.warn('缓存PDF数据失败:', error);
				}
			}, 1000); // 延迟缓存，不影响用户体验
		}

		totalPages.value = pdfDoc.numPages;
		loadingStageText.value = "正在渲染页面...";
		log(`PDF文档包含 ${totalPages.value} 页`);

		// 使用渲染队列渲染第一页
		await queueRenderPage(currentPage.value);

		loadingProgress.value = 100;
		loadingStageText.value = "加载完成！";

		// 结束性能监控
		performanceMetrics.value.loadEndTime = performance.now();
		const loadTime = performanceMetrics.value.loadEndTime - performanceMetrics.value.loadStartTime;
		log(`PDF加载完成，总耗时: ${Math.round(loadTime)}ms`);

		setTimeout(() => {
			isLoading.value = false;
			loadingProgress.value = 0;
		}, 300);

		// 异步预加载相邻页面（不阻塞主流程）
		setTimeout(() => {
			preloadAdjacentPages(currentPage.value);
		}, 500);

		// 取巧方案：如果是第一页且有多页，自动切换到第二页再回来
		// 这样可以激活第一页的文本选择功能
		if (currentPage.value === 1 && totalPages.value > 1) {
			// 显示初始化提示，让用户以为还在加载中
			loadingStageText.value = "正在初始化文本选择功能...";

			setTimeout(async () => {
				log('执行优化的取巧方案：快速切换页面以激活第一页文本选择功能');

				try {
					// 保存当前页面状态
					const originalPage = currentPage.value;

					// 快速切换到第二页
					log('快速切换到第二页...');
					await queueRenderPage(2);

					// 极短延迟，减少用户感知
					await new Promise(resolve => setTimeout(resolve, 200));

					// 快速切换回第一页
					log('快速切换回第一页...');
					await queueRenderPage(originalPage);

					log('优化取巧方案执行完成，第一页文本选择功能已激活');
				} catch (error) {
					log(`取巧方案执行失败: ${error}`);
				} finally {
					// 清除初始化提示
					setTimeout(() => {
						loadingStageText.value = "";
					}, 100);
				}
			}, 200); // 大幅缩短延迟时间
		}

	} catch (error) {
		console.error("PDF loading failed:", error);
		const errorMessage = error instanceof Error ? error.message : String(error);

		// 详细的错误分析
		if (errorMessage.includes('Invalid PDF structure')) {
			log(`PDF结构无效，可能原因：`);
			log(`1. URL返回的不是PDF文件`);
			log(`2. PDF文件已损坏`);
			log(`3. 网络请求被拦截或重定向`);
			log(`4. CORS问题`);
			log(`当前URL: ${pdfUrl}`);

			// 尝试直接获取数据进行分析
			try {
				const response = await fetch(pdfUrl);
				const text = await response.text();
				const first100Chars = text.substring(0, 100);
				log(`响应前100个字符: ${first100Chars}`);

				if (text.includes('<html') || text.includes('<!DOCTYPE')) {
					log('错误：URL返回的是HTML页面，不是PDF文件');
				} else if (!text.startsWith('%PDF')) {
					log('错误：文件不是有效的PDF格式（应该以%PDF开头）');
				}
			} catch (fetchError) {
				log(`无法获取URL内容进行分析: ${fetchError}`);
			}
		}

		log(`PDF加载失败: ${errorMessage}`);
		isLoading.value = false;
		loadingStageText.value = `加载失败: ${errorMessage}`;

		// 记录加载失败
		performanceMetrics.value.loadEndTime = performance.now();
	}
};

// 渲染队列管理函数（借鉴官方view.js）
const queueRenderPage = async (num: number): Promise<void> => {
	if (pageRendering.value) {
		pageNumPending.value = num;
		return;
	} else {
		return await renderPageWithQueue(num);
	}
};

// 预加载相邻页面
const preloadAdjacentPages = (currentPageNum: number) => {
	const pagesToPreload = [];

	// 预加载前一页
	if (currentPageNum > 1) {
		pagesToPreload.push(currentPageNum - 1);
	}

	// 预加载后一页
	if (currentPageNum < totalPages.value) {
		pagesToPreload.push(currentPageNum + 1);
	}

	// 预加载后两页（如果存在）
	if (currentPageNum + 1 < totalPages.value) {
		pagesToPreload.push(currentPageNum + 2);
	}

	// 异步预加载页面
	pagesToPreload.forEach(pageNum => {
		if (!preloadedPages.has(pageNum)) {
			preloadPage(pageNum);
		}
	});
};

// 预加载单个页面
const preloadPage = async (pageNum: number) => {
	if (!pdfDoc || preloadedPages.has(pageNum)) return;

	try {
		const page = await pdfDoc.getPage(pageNum);
		preloadedPages.set(pageNum, page);

		// 限制缓存大小
		if (preloadedPages.size > maxCacheSize) {
			const firstKey = preloadedPages.keys().next().value;
			preloadedPages.delete(firstKey);
		}

		log(`预加载页面 ${pageNum} 完成`);
		performanceMetrics.value.preloadedPagesCount++;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.warn(`预加载页面 ${pageNum} 失败:`, errorMessage);
	}
};

// 带队列管理的页面渲染
const renderPageWithQueue = async (num: number): Promise<void> => {
	pageRendering.value = true;

	try {
		await renderPage(num);
	} finally {
		pageRendering.value = false;

		// 处理待渲染的页面
		if (pageNumPending.value !== null) {
			const pendingNum = pageNumPending.value;
			pageNumPending.value = null;
			await queueRenderPage(pendingNum);
		}
	}
};







// 初始化事件监听器
const initEventListeners = () => {
	// 添加canvas点击事件监听
	if (pdfCanvas.value)
		pdfCanvas.value.addEventListener("click", handleCanvasClick);

	// 添加全局点击事件来关闭上下文菜单
	document.addEventListener("click", (event) => {
		if (contextMenuVisible.value && isTextSelected.value && !selectedText.value) {
			const target = event.target as HTMLElement;
			if (!target.closest(".pdf-context-menu")) closeContextMenu();
			console.log('走清空了')
		}
	});

	// 添加文本选择事件监听
	document.addEventListener("selectionchange", handleSelectionChange);

	// 屏蔽整个组件的默认右键菜单
	const preventDefaultContextMenu = (event: Event) => {
		event.preventDefault();
	};

	let pdfViewerElement: Element | null = null;

	// 在组件挂载时添加事件监听
	nextTick(() => {
		// 获取PDF查看器的根元素
		pdfViewerElement = document.querySelector(".pdf-viewer");
		if (pdfViewerElement) {
			pdfViewerElement.addEventListener(
				"contextmenu",
				preventDefaultContextMenu
			);
		}
	});

	// 在组件卸载时清理事件监听
	onUnmounted(() => {
		if (pdfViewerElement) {
			pdfViewerElement.removeEventListener(
				"contextmenu",
				preventDefaultContextMenu
			);
		}

	});
};

onMounted(async () => {
	// 初始化事件监听器
	initEventListeners();
});

// 处理文本选择变化
const handleSelectionChange = () => {
	try {
		const selection = window.getSelection();
		if (!selection || selection.rangeCount === 0) {
			selectedText.value = "";
			isTextSelected.value = false;
			contextMenuVisible.value = false;
			return;
		}

		const range = selection.getRangeAt(0);
		const text = range.toString().trim();

		if (text && textLayer.value?.contains(range.commonAncestorContainer)) {
			selectedText.value = text;
			isTextSelected.value = true;
			textSelectionRange.value = range;

			// 获取选择区域的位置信息用于菜单定位
			const rect = range.getBoundingClientRect();
			selectionData.value = {
				text: text,
				rect: rect,
				page: currentPage.value,
			};

			log(`选择了文本: "${text}"`);
			emit("textSelected", text);
		} else {
			selectedText.value = "";
			isTextSelected.value = false;
			contextMenuVisible.value = false;
		}
	} catch (error) {
		console.error('文本选择处理出错:', error);
		selectedText.value = "";
		isTextSelected.value = false;
		contextMenuVisible.value = false;
	}
};

// 处理鼠标移动事件（检查是否悬停在标注区域）
const handleMouseMove = (event: MouseEvent) => {
	// 检查鼠标是否悬停在有注释的标注区域上
	if (!textLayer.value) return;

	const rect = textLayer.value.getBoundingClientRect();
	const {scaleX, scaleY} = getCanvasScale();

	// 转换为原始 Canvas 坐标
	const x = (event.clientX - rect.left) * scaleX;
	const y = (event.clientY - rect.top) * scaleY;

	const currentPageUserAnnotations = userAnnotations.value.filter(
		(annotation) => annotation.page === currentPage.value
	);
	const currentPageExternalAnnotations = (
		props.externalAnnotations || []
	).filter((annotation) => annotation.page === currentPage.value);
	const allCurrentPageAnnotations = [
		...currentPageExternalAnnotations,
		...currentPageUserAnnotations,
	];

	let isOverAnnotation = false;
	for (const annotation of allCurrentPageAnnotations) {
		if (annotation.note && annotation.note.trim() !== "") {
			const {startX, startY, endX, endY} = annotation.position;
			if (x >= startX && x <= endX && y >= startY && y <= endY) {
				isOverAnnotation = true;
				break;
			}
		}
	}

	// 动态改变光标样式
	if (textLayer.value)
		textLayer.value.style.cursor = isOverAnnotation ? "pointer" : "text";
};

// 处理文本层点击事件
const handleTextLayerClick = (event: MouseEvent) => {
	// 只处理左键点击
	if (event.button !== 0) return;

	// 调用canvas点击处理函数
	handleCanvasClick(event);
};

// 处理右键点击显示上下文菜单
const handleContextMenu = (event: MouseEvent) => {
	event.preventDefault();

	if (props.forbid) return;

	if (isTextSelected.value && selectedText.value.trim()) {
		// 只有在启用上下文菜单时才显示
		if (props.showContextMenu) {
			contextMenuPosition.value = {
				x: event.clientX,
				y: event.clientY,
			};
			contextMenuVisible.value = true;
		}
	}
};

// 处理canvas点击事件
const handleCanvasClick = (event: MouseEvent) => {
	if (!pdfCanvas.value) return;
	if (selectedText.value) return;

	// 获取点击位置相对于canvas的坐标
	const rect = pdfCanvas.value.getBoundingClientRect();
	const {scaleX, scaleY} = getCanvasScale();

	// 转换为原始 Canvas 坐标
	const x = (event.clientX - rect.left) * scaleX;
	const y = (event.clientY - rect.top) * scaleY;

	log(`Canvas点击: x=${x}, y=${y}`);

	// 检查是否点击了标注区域（包括用户标注和外部标注）
	const currentPageUserAnnotations = userAnnotations.value.filter(
		(annotation) => annotation.page === currentPage.value
	);
	const currentPageExternalAnnotations = (
		props.externalAnnotations || []
	).filter((annotation) => annotation.page === currentPage.value);
	const allCurrentPageAnnotations = [
		...currentPageExternalAnnotations,
		...currentPageUserAnnotations,
	];

	log(
		`当前页面有 ${allCurrentPageAnnotations.length} 个标注 (用户: ${currentPageUserAnnotations.length}, 外部: ${currentPageExternalAnnotations.length})`
	);
	log("所有标注列表:", allCurrentPageAnnotations);

	for (const annotation of allCurrentPageAnnotations) {
		const {startX, startY, endX, endY} = annotation.position;
		log(
			`检查标注区域: (${startX}, ${startY}) - (${endX}, ${endY}), 点击位置: (${x}, ${y})`
		);

		if (x >= startX && x <= endX && y >= startY && y <= endY) {
			// 点击了标注区域
			log(`点击了标注区域: ${annotation.text}, 有注释: ${!!annotation.note}`);

			if (annotation.note && annotation.note.trim() !== "") {
				// 只有有注释的标注才显示tooltip
				showAnnotationTooltip(annotation, event);
				log(`显示注释tooltip: ${annotation.text}`);
				return;
			} else {
				log("标注没有注释内容，不显示tooltip");
			}
		}
	}

	// 检查点击是否在任何关键词高亮区域内
	for (const area of highlightedAreas.value) {
		if (
			x >= area.x &&
			x <= area.x + area.width &&
			y >= area.y &&
			y <= area.y + area.height
		) {
			log(`点击了关键字: ${area.keyword}`);
			emit("keywordClick", area.keyword);
			return;
		}
	}

	// 如果点击了其他区域，关闭注释tooltip
	closeAnnotationTooltip();
};

// 直接打开对应弹窗
const openAnnotationById = (annotationId: string) => {
	// 查找注释
	const annotation = props.externalAnnotations.find(
		(a) => a.id === annotationId
	);
	console.log(annotation, annotationId);
	// 如果找到注释
	if (annotation) {
		// 如果提供了页码，跳转到该页
		if (annotation?.page) {
			currentPage.value = annotation?.page;
			currentPageInput.value = currentPage.value;
			debouncedRenderPage(annotation?.page);
		}

		// 显示注释弹窗
		showAnnotationTooltip(annotation, {
			clientX: annotation.position.endX,
			clientY: annotation.position.endY,
		} as MouseEvent); // 使用默认位置
	} else {
		console.log("Annotation not found");
	}
};

// 清除文本选择
const clearTextSelection = () => {
	const selection = window.getSelection();
	if (selection) {
		selection.removeAllRanges();
	}
	selectedText.value = "";
	isTextSelected.value = false;
	textSelectionRange.value = null;
	contextMenuVisible.value = false;
};

// 关闭上下文菜单
const closeContextMenu = () => {
	contextMenuVisible.value = false;
	clearTextSelection();
	// 隐藏选中文本高亮效果
	selectedTextHighlight.value.visible = false;
};

// 显示注释tooltip
const showAnnotationTooltip = (annotation: any, event: MouseEvent) => {
	selectedAnnotation.value = annotation;
	annotationTooltipPosition.value = {
		x: event.clientX,
		y: event.clientY,
	};
	annotationTooltipVisible.value = true;

	// 关闭其他弹出层
	contextMenuVisible.value = false;
	aiModalVisible.value = false;
};

// 关闭注释tooltip
const closeAnnotationTooltip = () => {
	annotationTooltipVisible.value = false;
	selectedAnnotation.value = null;
};

// 在canvas上绘制用户高亮
const drawUserHighlight = (annotation: any) => {
	if (!pdfCanvas.value) return;

	const canvas = pdfCanvas.value;
	const context = canvas.getContext("2d")!;
	const {startX, startY, endX, endY} = annotation.position;
	const hasNote = annotation.note && annotation.note.trim() !== "";

	// 保存当前绘图状态
	context.save();

	// 设置高亮样式
	context.fillStyle = annotation.color;
	context.globalAlpha = 0.4;

	// 绘制高亮矩形
	context.fillRect(startX, startY, endX - startX, endY - startY);

	// 如果有注释，添加特殊边框和图标提示
	if (hasNote) {
		context.globalAlpha = 1;
		context.strokeStyle = "#3B82F6"; // 蓝色边框
		context.lineWidth = 2;
		context.setLineDash([4, 2]); // 虚线边框
		context.strokeRect(startX, startY, endX - startX, endY - startY);

		// 绘制注释图标
		const iconSize = 12;
		const iconX = endX - iconSize - 2;
		const iconY = startY + 2;

		// 图标背景
		context.fillStyle = "#3B82F6";
		context.fillRect(iconX, iconY, iconSize, iconSize);

		// 图标文字 "N"
		context.fillStyle = "white";
		context.font = "8px Arial";
		context.textAlign = "center";
		context.fillText("N", iconX + iconSize / 2, iconY + iconSize / 2 + 2);

		context.setLineDash([]); // 重置虚线
	}

	// 恢复绘图状态
	context.restore();

	log(
		`绘制用户高亮: ${annotation.text} (${annotation.color}) ${
			hasNote ? "含注释" : ""
		}`
	);
};

// 只绘制用户标注（不重新渲染页面）
const drawCurrentPageAnnotations = () => {
	if (!pdfCanvas.value) return;

	// 获取当前页面的用户标注
	const currentPageUserAnnotations = userAnnotations.value.filter(
		(annotation) => annotation.page === currentPage.value
	);

	// 获取当前页面的外部标注
	const currentPageExternalAnnotations = (
		props.externalAnnotations || []
	).filter((annotation) => annotation.page === currentPage.value);

	// 绘制所有当前页面的标注
	[...currentPageExternalAnnotations, ...currentPageUserAnnotations].forEach(
		(annotation) => {
			drawUserHighlight(annotation);
		}
	);

	const totalCount =
		currentPageUserAnnotations.length + currentPageExternalAnnotations.length;
	log(
		`绘制了 ${totalCount} 个标注 (用户: ${currentPageUserAnnotations.length}, 外部: ${currentPageExternalAnnotations.length})`
	);
};

// 重新绘制所有用户标注（重新渲染页面）
const redrawUserAnnotations = () => {
	if (!pdfCanvas.value || !pdfDoc) return;

	// 重新渲染当前页面以清除所有绘制内容
	renderPage(currentPage.value);
};

// 上下文菜单事件处理
const handleHighlight = (data: {
	text: string;
	color: string;
	position: any;
}) => {
	log("高亮文本:", data);

	if (!textSelectionRange.value || !selectionData.value) {
		log("没有有效的文本选择");
		return;
	}

	// 生成唯一ID
	const annotationId = `highlight_${Date.now()}_${Math.random()
		.toString(36)
		.substring(2, 11)}`;

	// 从选择范围获取位置信息
	const rect = textSelectionRange.value.getBoundingClientRect();
	const canvasRect = pdfCanvas.value?.getBoundingClientRect();
	if (!canvasRect) return;

	const {scaleX, scaleY} = getCanvasScale();
	const startX = (rect.left - canvasRect.left) * scaleX;
	const startY = (rect.top - canvasRect.top) * scaleY;
	const endX = (rect.right - canvasRect.left) * scaleX;
	const endY = (rect.bottom - canvasRect.top) * scaleY;

	// 创建标注记录
	const annotation = {
		id: annotationId,
		text: data.text,
		note: "",
		color: data.color,
		page: currentPage.value,
		position: {startX, startY, endX, endY},
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	// 添加到用户标注列表
	userAnnotations.value.push(annotation);

	// 在canvas上绘制高亮
	drawUserHighlight(annotation);

	// 发射事件
	emit("highlightAdded", {...data, id: annotationId});
	emit("menuAction", "highlight", data);
};

const handleAddNote = (data: { text: string; note: string; position: any }) => {
	log("添加注释:", data);

	if (!textSelectionRange.value) {
		log("没有有效的文本选择");
		return;
	}

	// 从选择范围获取位置信息
	const rect = textSelectionRange.value.getBoundingClientRect();
	const canvasRect = pdfCanvas.value?.getBoundingClientRect();
	if (!canvasRect) return;

	const {scaleX, scaleY} = getCanvasScale();
	const startX = (rect.left - canvasRect.left) * scaleX;
	const startY = (rect.top - canvasRect.top) * scaleY;
	const endX = (rect.right - canvasRect.left) * scaleX;
	const endY = (rect.bottom - canvasRect.top) * scaleY;

	// 保存待处理的数据，等待用户输入注释
	pendingNoteData.value = {
		text: data.text,
		position: {startX, startY, endX, endY},
	};

	// 显示注释输入模态框
	noteSelectedText.value = data.text;
	noteInputVisible.value = true;

	// 关闭上下文菜单
	closeContextMenu();
};

const handleAiExplain = (data: any) => {
	log("AI解释:", data);
	// aiSelectedText.value = data.text
	// aiModalVisible.value = true

	if (!textSelectionRange.value) {
		log("没有有效的文本选择");
		return;
	}

	// 从选择范围获取位置信息
	const rect = textSelectionRange.value.getBoundingClientRect();
	const canvasRect = pdfCanvas.value?.getBoundingClientRect();
	if (!canvasRect) return;

	const {scaleX, scaleY} = getCanvasScale();
	const startX = (rect.left - canvasRect.left) * scaleX;
	const startY = (rect.top - canvasRect.top) * scaleY;
	const endX = (rect.right - canvasRect.left) * scaleX;
	const endY = (rect.bottom - canvasRect.top) * scaleY;

	// 生成唯一ID
	const annotationId = `note_${Date.now()}_${Math.random()
		.toString(36)
		.substring(2, 11)}`;

	// 创建注释记录
	const annotation = {
		id: annotationId,
		text: data.text,
		note: data.note,
		color: "#FF9ECB",
		page: currentPage.value,
		position: {startX, startY, endX, endY},
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	// 添加到用户标注列表
	// userAnnotations.value.push(annotation)

	// 在canvas上绘制高亮
	// drawUserHighlight(annotation)

	// 发射事件
	emit("aiExplainRequested", annotation);
	emit("menuAction", "aiExplain", {text: data.text});
};

const handleCopy = (text: string) => {
	log("复制文本:", text);
	emit("textCopied", text);
	emit("menuAction", "copy", {text});
};

const handleSearch = (text: string) => {
	log("搜索文本:", text);
	emit("searchRequested", text);
	emit("menuAction", "search", {text});
};

// AI模态框事件处理
const closeAiModal = () => {
	aiModalVisible.value = false;
	aiSelectedText.value = "";
};

const handleAiCopy = (text: string) => {
	log("复制AI解释:", text);
	emit("menuAction", "copyAiExplanation", {text});
};

// 注释tooltip事件处理
const updateAnnotation = (updatedAnnotation: any) => {
	console.log(updatedAnnotation);
	emit("edit", updatedAnnotation);
	closeAnnotationTooltip();
	return;
	// const index = userAnnotations.value.findIndex(
	// 	a => a.id === updatedAnnotation.id,
	// )
	// if (index !== -1) {
	// 	userAnnotations.value[index] = updatedAnnotation
	//
	// 	// 重新绘制当前页面的标注
	// 	redrawUserAnnotations()
	// 	log('更新注释:', updatedAnnotation.text)
	// }
	// closeAnnotationTooltip()
};

const deleteAnnotationById = (id: string) => {
	const index = userAnnotations.value.findIndex((a) => a.id === id);
	console.log(userAnnotations.value, index);
	if (index !== -1) {
		const annotation = userAnnotations.value[index];
		userAnnotations.value.splice(index, 1);
		// 重新绘制当前页面的标注
		// redrawUserAnnotations()
		log("删除注释:", annotation.text);
	}
	redrawUserAnnotations();
	emit("delete", id);
	closeAnnotationTooltip();
};

// 注释输入模态框事件处理
const closeNoteInput = () => {
	noteInputVisible.value = false;
	noteSelectedText.value = "";
	pendingNoteData.value = null;
};

const saveNote = async (data: { note: string; color: string }) => {
	if (!pendingNoteData.value) return;

	// 生成唯一ID
	// const annotationId = `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

	// 创建注释记录
	const annotation = {
		id: undefined,
		text: pendingNoteData.value.text,
		note: data.note,
		color: "#FF9ECB",
		page: currentPage.value,
		position: pendingNoteData.value.position,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	// 发射事件
	let res = await addTextbookAnnotations({
		content: annotation.text,
		note: annotation.note,
		position: JSON.stringify(annotation.position),
		page: annotation.page,
		color: annotation.color,
		textbookId: props.main.id,
	});
	annotation.id = res.data.id;
	// console.log(res)
	// 添加到用户标注列表
	userAnnotations.value.push(annotation);

	// 在canvas上绘制高亮
	drawUserHighlight(annotation);
	emit("noteAdded", {
		content: annotation.text,
		note: annotation.note,
		position: JSON.stringify(annotation.position),
		page: annotation.page,
		color: annotation.color,
		// id: annotationId,
	});

	emit("menuAction", "addNote", {
		text: annotation.text,
		note: annotation.note,
	});

	log("保存注释:", annotation);

	// 关闭模态框
	closeNoteInput();
};

// 渲染PDF文本层以支持文本选择
const renderPdfTextLayer = async (textContent: any, viewport: any) => {
	if (!textLayer.value || !pdfCanvas.value) return;

	// 清空现有文本层内容
	textLayer.value.innerHTML = "";

	// 获取canvas的实际显示尺寸和内部尺寸
	const canvasRect = pdfCanvas.value.getBoundingClientRect();
	const canvasDisplayWidth = canvasRect.width;
	const canvasDisplayHeight = canvasRect.height;
	const canvasInternalWidth = pdfCanvas.value.width;
	const canvasInternalHeight = pdfCanvas.value.height;

	// 计算显示缩放比例
	const displayScaleX = canvasDisplayWidth / canvasInternalWidth;
	const displayScaleY = canvasDisplayHeight / canvasInternalHeight;

	log(`Canvas显示尺寸: ${canvasDisplayWidth}x${canvasDisplayHeight}`);
	log(`Canvas内部尺寸: ${canvasInternalWidth}x${canvasInternalHeight}`);
	log(`显示缩放比例: ${displayScaleX}x${displayScaleY}`);

	// 设置文本层尺寸为canvas的显示尺寸
	textLayer.value.style.left = "0px";
	textLayer.value.style.top = "0px";
	textLayer.value.style.width = `${canvasDisplayWidth}px`;
	textLayer.value.style.height = `${canvasDisplayHeight}px`;

	const textDivs: HTMLElement[] = [];

	textContent.items.forEach((item: any) => {
		if (!item.str || item.str.trim() === "") return;

		const div = document.createElement("div");
		div.textContent = item.str;
		div.style.position = "absolute";

		// 使用PDF.js的标准坐标转换得到viewport坐标
		const [viewportX, viewportY] = viewport.convertToViewportPoint(
			item.transform[4],
			item.transform[5]
		);
		const viewportFontSize = Math.abs(item.transform[3]) * viewport.scale;

		// 将viewport坐标转换为实际显示坐标
		const displayX = viewportX * displayScaleX;
		const displayY = viewportY * displayScaleY;
		const displayFontSize = viewportFontSize * displayScaleY;

		// 调整Y坐标，因为PDF文本基线与DOM元素顶部的差异
		const adjustedY = displayY - displayFontSize * 0.8;

		// 设置样式
		div.style.left = `${displayX}px`;
		div.style.top = `${adjustedY}px`;
		div.style.fontSize = `${displayFontSize}px`;
		div.style.fontFamily = item.fontName || "sans-serif";
		div.style.color = "transparent";
		div.style.userSelect = "text";
		div.style.pointerEvents = "auto";
		div.style.whiteSpace = "nowrap";
		div.style.margin = "0";
		div.style.padding = "0";
		div.style.border = "none";
		div.style.lineHeight = "1";
		div.style.zIndex = "10";

		// 处理水平缩放
		if (Math.abs(item.transform[0] - item.transform[3]) > 0.01) {
			div.style.transform = `scaleX(${item.transform[0] / item.transform[3]})`;
			div.style.transformOrigin = "left bottom";
		}

		textLayer.value!.appendChild(div);
		textDivs.push(div);
	});

	log(`渲染了 ${textDivs.length} 个文本元素到文本层`);
};

const renderPage = async (pageNum: number) => {
	if (!pdfDoc || !pdfCanvas.value) return;

	// 开始渲染性能监控
	performanceMetrics.value.renderStartTime = performance.now();

	// 取消当前渲染任务（如果有）
	if (currentRenderTask) {
		try {
			await currentRenderTask.cancel();
		} catch (error) {
			log("取消渲染任务时出错:", error);
		}
		currentRenderTask = null;
	}

	// 清空高亮区域记录
	highlightedAreas.value = [];

	// 清除选中文本高亮效果
	selectedTextHighlight.value.visible = false;

	// 尝试从预加载缓存获取页面
	let page;
	if (preloadedPages.has(pageNum)) {
		page = preloadedPages.get(pageNum);
		performanceMetrics.value.cacheHits++;
		log(`使用预加载的页面 ${pageNum}`);
	} else {
		page = await pdfDoc.getPage(pageNum);
		performanceMetrics.value.cacheMisses++;
	}

	const scale = 1.5; // 定义 scale
	const viewport = page.getViewport({scale});
	const context = pdfCanvas.value.getContext("2d")!;

	pdfCanvas.value.height = viewport.height;
	pdfCanvas.value.width = viewport.width;

	// 检查渲染缓存
	const cacheKey = `${pageNum}_${scale}_${viewport.width}_${viewport.height}`;
	if (renderCache.has(cacheKey)) {
		const cachedImageData = renderCache.get(cacheKey);
		if (cachedImageData) {
			context.putImageData(cachedImageData, 0, 0);
			performanceMetrics.value.cacheHits++;
			log(`使用缓存渲染页面 ${pageNum}`);

			// 仍需要处理文本层和标注
			const textContent = await page.getTextContent();
			currentPageTextContent = textContent;
			highlightKeywords(context, textContent, viewport, scale, keywords.value);
			await renderPdfTextLayer(textContent, viewport);
			drawCurrentPageAnnotations();

			// 结束渲染性能监控
			performanceMetrics.value.renderEndTime = performance.now();
			const renderTime = performanceMetrics.value.renderEndTime - performanceMetrics.value.renderStartTime;
			performanceMetrics.value.totalRenderTime += renderTime;

			return;
		}
	}

	// 清除canvas
	context.clearRect(0, 0, pdfCanvas.value.width, pdfCanvas.value.height);

	// 渲染 PDF 到 canvas
	const renderContext = {
		canvasContext: context,
		viewport,
	};

	// 保存渲染任务引用
	currentRenderTask = page.render(renderContext);

	try {
		await currentRenderTask.promise;

		// 缓存渲染结果
		const imageData = context.getImageData(0, 0, pdfCanvas.value.width, pdfCanvas.value.height);
		renderCache.set(cacheKey, imageData);

		// 限制缓存大小
		if (renderCache.size > maxCacheSize) {
			const firstKey = renderCache.keys().next().value;
			renderCache.delete(firstKey);
		}

		// 获取文本内容并高亮
		const textContent = await page.getTextContent();
		currentPageTextContent = textContent; // 缓存文本内容用于文本选择
		highlightKeywords(context, textContent, viewport, scale, keywords.value);

		// 渲染文本层以支持文本选择
		await renderPdfTextLayer(textContent, viewport);

		// 绘制用户标注
		drawCurrentPageAnnotations();

		// 结束渲染性能监控
		performanceMetrics.value.renderEndTime = performance.now();
		const renderTime = performanceMetrics.value.renderEndTime - performanceMetrics.value.renderStartTime;
		performanceMetrics.value.totalRenderTime += renderTime;

		log(`页面 ${pageNum} 渲染完成并已缓存，耗时: ${Math.round(renderTime)}ms`);
	} catch (error) {
		if (error instanceof Error && error.message !== "Rendering cancelled")
			log("渲染PDF时出错:", error);
	} finally {
		currentRenderTask = null;
	}
};
// 高亮关键词
const highlightKeywords = (
	context: CanvasRenderingContext2D,
	textContent: any,
	viewport: any,
	scale: number,
	keywords: { text: string; color: string }[]
) => {
	// 如果未启用高亮功能，则直接返回
	if (!props.enableHighlight) return;

	// 创建一个映射来存储每个项目的文本和位置信息
	const textItems: {
		text: string;
		x: number;
		y: number;
		width: number;
		height: number;
		item: any;
	}[] = [];

	// 首先收集所有文本项
	textContent.items.forEach((item: any) => {
		textItems.push({
			text: item.str,
			x: item.transform[4],
			y: item.transform[5],
			width: item.width,
			height: item.height || 14,
			item,
		});
	});

	// 为调试目的，输出完整的文本内容
	log("PDF文本内容:", textItems.map((item) => item.text).join("|"));

	// 输出每个文本项的详细内容，帮助调试
	textItems.forEach((item, index) => {
		log(`文本项[${index}]: "${item.text}" (${item.text.length}字符)`);
		// 输出每个字符的编码，帮助检测特殊字符
		if (item.text.includes("审") || item.text.includes("核")) {
			log("包含审核相关字符:", item.text);
			for (let i = 0; i < item.text.length; i++)
				log(`字符[${i}]: "${item.text[i]}" 编码: ${item.text.charCodeAt(i)}`);
		}
	});

	// 对每个关键词进行处理
	keywords.forEach((kw) => {
		const keyword = kw.text;
		log(`尝试匹配关键词: "${keyword}"`);

		// 创建不区分大小写的正则表达式
		const regex = new RegExp(keyword, "gi");

		// 检查每个文本项
		textItems.forEach((textItem) => {
			const text = textItem.text;
			let match: RegExpExecArray | null;

			// 使用正则表达式查找所有匹配项
			while ((match = regex.exec(text)) !== null) {
				const startIndex = match.index;
				const textLength = match[0].length;

				log(`在文本 "${text}" 中找到匹配: "${match[0]}", 位置: ${startIndex}`);

				// 计算关键字的相对位置和宽度
				const charWidth = textItem.width / text.length; // 每个字符的平均宽度
				const highlightX = textItem.x + startIndex * charWidth;
				const highlightWidth = textLength * charWidth;

				// 将 PDF 坐标转换为 canvas 坐标
				const [canvasX, canvasY] = viewport.convertToViewportPoint(
					highlightX,
					textItem.y
				);
				const [canvasXEnd] = viewport.convertToViewportPoint(
					highlightX + highlightWidth,
					textItem.y
				);
				const canvasWidth = canvasXEnd - canvasX;
				const canvasHeight = textItem.height * scale;

				// 绘制高亮矩形
				context.fillStyle = kw.color;
				context.globalAlpha = 0.4;
				context.fillRect(
					canvasX,
					canvasY - canvasHeight,
					canvasWidth,
					canvasHeight
				);

				// 记录高亮区域
				highlightedAreas.value.push({
					x: canvasX,
					y: canvasY - canvasHeight,
					width: canvasWidth,
					height: canvasHeight,
					keyword: match[0],
				});
			}
		});

		// 增强跨文本项匹配逻辑
		// 这对于处理可能被分割的中文字符很有用
		if (keyword.length > 1) {
			// 检查相邻的文本项组合
			for (let i = 0; i < textItems.length - 1; i++) {
				// 尝试将当前项与下一项组合
				const combinedText = textItems[i].text + textItems[i + 1].text;
				const startIndex = combinedText.indexOf(keyword);

				if (startIndex !== -1) {
					log(
						`在组合文本 "${combinedText}" 中找到跨项匹配: "${keyword}", 位置: ${startIndex}`
					);

					// 处理跨项匹配的高亮逻辑
					const firstItem = textItems[i];
					const secondItem = textItems[i + 1];

					// 判断关键词是否跨越两个文本项
					if (startIndex + keyword.length > firstItem.text.length) {
						// 关键词跨越两个文本项
						// 高亮第一个文本项的部分
						if (startIndex < firstItem.text.length) {
							const partialLength = firstItem.text.length - startIndex;
							const charWidth = firstItem.width / firstItem.text.length;
							const highlightX = firstItem.x + startIndex * charWidth;
							const highlightWidth = partialLength * charWidth;

							// 转换为canvas坐标
							const [canvasX, canvasY] = viewport.convertToViewportPoint(
								highlightX,
								firstItem.y
							);
							const [canvasXEnd] = viewport.convertToViewportPoint(
								highlightX + highlightWidth,
								firstItem.y
							);
							const canvasWidth = canvasXEnd - canvasX;
							const canvasHeight = firstItem.height * scale;

							// 绘制高亮矩形
							context.fillStyle = kw.color;
							context.globalAlpha = 0.4;
							context.fillRect(
								canvasX,
								canvasY - canvasHeight,
								canvasWidth,
								canvasHeight
							);

							// 记录高亮区域（第一部分）
							highlightedAreas.value.push({
								x: canvasX,
								y: canvasY - canvasHeight,
								width: canvasWidth,
								height: canvasHeight,
								keyword,
							});
						}

						// 高亮第二个文本项的部分
						const secondPartLength =
							keyword.length - (firstItem.text.length - startIndex);
						if (secondPartLength > 0) {
							const charWidth = secondItem.width / secondItem.text.length;
							const highlightWidth = secondPartLength * charWidth;

							// 转换为canvas坐标
							const [canvasX, canvasY] = viewport.convertToViewportPoint(
								secondItem.x,
								secondItem.y
							);
							const [canvasXEnd] = viewport.convertToViewportPoint(
								secondItem.x + highlightWidth,
								secondItem.y
							);
							const canvasWidth = canvasXEnd - canvasX;
							const canvasHeight = secondItem.height * scale;

							// 绘制高亮矩形
							context.fillStyle = kw.color;
							context.globalAlpha = 0.4;
							context.fillRect(
								canvasX,
								canvasY - canvasHeight,
								canvasWidth,
								canvasHeight
							);

							// 记录高亮区域（第二部分）
							highlightedAreas.value.push({
								x: canvasX,
								y: canvasY - canvasHeight,
								width: canvasWidth,
								height: canvasHeight,
								keyword,
							});
						}
					}
				}
			}

			// 检查更多相邻文本项的组合（处理可能被分割成多个部分的中文关键词）
			for (let i = 0; i < textItems.length - 2; i++) {
				const combinedText =
					textItems[i].text + textItems[i + 1].text + textItems[i + 2].text;
				if (combinedText.includes(keyword))
					log(`在三项组合文本 "${combinedText}" 中找到跨项匹配: "${keyword}"`);
				// 这里可以添加更复杂的高亮逻辑，处理跨越三个文本项的情况
			}
		}

		// 特殊处理中文关键词（假设中文字符可能被单独分割）
		if (/[\u4E00-\u9FA5]/.test(keyword)) {
			// 检测是否包含中文字符
			log("处理中文关键词:", keyword);

			// 查找连续的文本项，它们的字符可能组成关键词
			for (let i = 0; i < textItems.length - keyword.length + 1; i++) {
				let potentialMatch = "";
				const matchItems = [];

				// 尝试组合连续的文本项
				for (let j = 0; j < keyword.length && i + j < textItems.length; j++) {
					if (textItems[i + j].text.length === 1) {
						// 假设单个字符的文本项可能是分割的中文字符
						potentialMatch += textItems[i + j].text;
						matchItems.push(textItems[i + j]);

						// 如果组合起来匹配关键词
						if (potentialMatch === keyword) {
							log(`找到分散的中文关键词匹配: "${potentialMatch}"`);

							// 高亮所有匹配的文本项
							matchItems.forEach((item) => {
								const [canvasX, canvasY] = viewport.convertToViewportPoint(
									item.x,
									item.y
								);
								const [canvasXEnd] = viewport.convertToViewportPoint(
									item.x + item.width,
									item.y
								);
								const canvasWidth = canvasXEnd - canvasX;
								const canvasHeight = item.height * scale;

								// 绘制高亮矩形
								context.fillStyle = kw.color;
								context.globalAlpha = 0.4;
								context.fillRect(
									canvasX,
									canvasY - canvasHeight,
									canvasWidth,
									canvasHeight
								);

								// 记录高亮区域
								highlightedAreas.value.push({
									x: canvasX,
									y: canvasY - canvasHeight,
									width: canvasWidth,
									height: canvasHeight,
									keyword,
								});
							});

							break;
						}
					} else {
						break; // 如果不是单个字符的文本项，则中断尝试
					}
				}
			}
		}
	});
};

// 添加防抖功能，避免频繁切换页面导致的渲染冲突
const debounce = (fn: Function, delay: number) => {
	let timer: number | null = null;
	return (...args: any[]) => {
		if (timer) clearTimeout(timer);
		timer = setTimeout(() => {
			fn(...args);
			timer = null;
		}, delay) as unknown as number;
	};
};

// 使用防抖包装页面切换函数（保留兼容性）
const debouncedRenderPage = debounce((pageNum: number) => {
	queueRenderPage(pageNum);
}, 300);

// 翻页逻辑
const prevPage = () => {
	if (currentPage.value > 1) {
		currentPage.value--;
		currentPageInput.value = currentPage.value;
		queueRenderPage(currentPage.value);
		emit("pageChange", currentPage.value);
		// 预加载相邻页面
		preloadAdjacentPages(currentPage.value);
	}
};

// 下一页
const nextPage = () => {
	if (currentPage.value < totalPages.value) {
		currentPage.value++;
		currentPageInput.value = currentPage.value;
		queueRenderPage(currentPage.value);
		emit("pageChange", currentPage.value);
		// 预加载相邻页面
		preloadAdjacentPages(currentPage.value);
	}
	if (currentPage.value === totalPages.value) {
		emit("maxPageChange");
	}
};

// 跳转到指定页
const goToPage = () => {
	if (
		currentPageInput.value >= 1 &&
		currentPageInput.value <= totalPages.value
	) {
		currentPage.value = currentPageInput.value;
		queueRenderPage(currentPage.value);
		emit("pageChange", currentPage.value);
		// 预加载相邻页面
		preloadAdjacentPages(currentPage.value);
	} else {
		currentPageInput.value = currentPage.value;
	}

	if (currentPage.value === totalPages.value) {
		emit("maxPageChange");
	}
};

const goPage = (page: any) => {
	// console.log(page)
	currentPage.value = page;
	currentPageInput.value = page;
	queueRenderPage(currentPage.value);
	emit("pageChange", page);
	// 预加载相邻页面
	preloadAdjacentPages(currentPage.value);

	if (currentPage.value === totalPages.value) {
		emit("maxPageChange");
	}
};
// 新增：获取 Canvas 显示缩放比例的工具函数
const getCanvasScale = () => {
	if (!pdfCanvas.value) return {scaleX: 1, scaleY: 1};
	const canvas = pdfCanvas.value;
	const rect = canvas.getBoundingClientRect(); // 显示尺寸
	const scaleX = canvas.width / rect.width; // 原始宽度 / 显示宽度
	const scaleY = canvas.height / rect.height; // 原始高度 / 显示高度
	return {scaleX, scaleY};
};

// 修改watch，使用渲染队列
watch(currentPage, (newPage) => {
	queueRenderPage(newPage);
	// 预加载相邻页面
	preloadAdjacentPages(newPage);
});

// 暴露组件的属性和方法 ，可以通过ref获取 方便外部调用
/*
  renderPage,
  currentPage,
  totalPages,
  highlightedAreas,
  keywords,
  prevPage,
  nextPage,
  goToPage
*/
// 获取当前页文字内容
const getCurrentPageTextContent = () => {
	if (!currentPageTextContent) {
		return '';
	}

	// 提取文字内容
	let textContent = '';
	if (currentPageTextContent.items) {
		textContent = currentPageTextContent.items
			.map((item: any) => item.str)
			.join(' ');
	}

	return textContent;
};

defineExpose({
	// 原有功能（保持兼容性）
	renderPage,
	currentPage,
	totalPages,
	highlightedAreas,
	keywords,
	userAnnotations,
	prevPage,
	nextPage,
	goToPage,
	redrawUserAnnotations,
	drawCurrentPageAnnotations,
	init,
	openAnnotationById,
	goPage,
	getCurrentPageTextContent,

	// 新增的优化功能
	queueRenderPage,
	preloadAdjacentPages,
	performanceMetrics: () => performanceMetrics.value,
	cacheManager,

	// 缓存统计
	getCacheStats: () => ({
		preloadedPages: preloadedPages.size,
		renderCache: renderCache.size,
		cacheHits: performanceMetrics.value.cacheHits,
		cacheMisses: performanceMetrics.value.cacheMisses,
		totalRenderTime: performanceMetrics.value.totalRenderTime
	}),

	// 清除缓存
	clearCache: () => {
		cacheManager.clearAll();
	},


});
</script>

<template>
	<div class="pdf-viewer flex flex-col items-center p-4">
		<div class="pdf-container bg-gray-100 rounded-lg w-full relative">
			<div v-show="isLoading" class="loading-container">
				<div class="progress-wrapper">
					<div class="progress-icon">
						<svg class="spinner" viewBox="0 0 50 50">
							<circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
						</svg>
					</div>
					<div class="progress-text">{{ loadingStageText }} {{ Math.round(realLoadingProgress || loadingProgress)>100?'100':Math.round(realLoadingProgress || loadingProgress) }}%</div>
					<div class="progress-bar-container">
						<div class="progress-bar" :style="{ width: (realLoadingProgress || loadingProgress) + '%' }"></div>
					</div>
				</div>
			</div>
			<canvas v-show="!isLoading" ref="pdfCanvas" class="w-full"/>
			<!-- 文本选择层 -->
			<div
				v-show="!isLoading"
				ref="textLayer"
				class="text-layer absolute inset-0 pointer-events-auto w-full"
				@click="handleTextLayerClick"
				@mousemove="handleMouseMove"
				@mouseup.left="handleContextMenu"
			>
				<!-- 右键显示弹窗-->
				<!-- @contextmenu="handleContextMenu"-->
				<!-- 已选择文本的高亮效果 -->
				<div
					v-if="selectedTextHighlight.visible"
					:style="selectedTextHighlight.style"
					class="selected-text-highlight absolute bg-blue-400 bg-opacity-40 border border-blue-500 pointer-events-none animate-pulse"
				/>
			</div>

			<!-- 上下文菜单 -->
			<PdfContextMenu
				v-if="props.showContextMenu"
				:position="contextMenuPosition"
				:selected-text="selectedText"
				:selection-data="selectionData"
				:visible="contextMenuVisible"
				@close="closeContextMenu"
				@copy="handleCopy"
				@highlight="handleHighlight"
				@search="handleSearch"
				@add-note="handleAddNote"
				@ai-explain="handleAiExplain"
			/>
		</div>

		<!-- AI解释模态框 -->
		<AiExplanationModal
			:selected-text="aiSelectedText"
			:visible="aiModalVisible"
			@close="closeAiModal"
			@copy="handleAiCopy"
		/>

		<!-- 注释tooltip -->
		<AnnotationTooltip
			:annotation="selectedAnnotation"
			:position="annotationTooltipPosition"
			:visible="annotationTooltipVisible"
			@close="closeAnnotationTooltip"
			@delete="deleteAnnotationById"
			@update="updateAnnotation"
		/>

		<!-- 注释输入模态框 -->
		<NoteInputModal
			:selected-text="noteSelectedText"
			:visible="noteInputVisible"
			@close="closeNoteInput"
			@save="saveNote"
		/>

		<div class="pagination mt-4 flex items-center space-x-2">
			<!--			<button-->
			<!--				:disabled="currentPage <= 1"-->
			<!--				class="bg-gray-600 text-white px-3 py-1 rounded hover:bg-blue-600 disabled:bg-gray-400"-->
			<!--				@click="prevPage"-->
			<!--			>-->
			<!--				&lt;-->
			<!--			</button>-->
			<n-button
				:disabled="currentPage <= 1"
				@click="prevPage"
				circle
				size="small"
			>
				<SvgIcon icon="ri:arrow-left-s-line" />
			</n-button>
			<n-input-number
				v-model:value="currentPageInput"
				:max="totalPages"
				:min="1"
				:show-button="false"
				class="w-16"
				@keyup.enter="goToPage"
			/>
			<span>/ {{ totalPages }}</span>
			<n-button
				:disabled="currentPage >= totalPages"
				@click="nextPage"
				circle
				size="small"
			>
				<SvgIcon icon="ri:arrow-right-s-line" />
			</n-button>
			<!--			<button-->
			<!--				:disabled="currentPage >= totalPages"-->
			<!--				class="bg-gray-600 text-white px-3 py-1 rounded hover:bg-blue-600 disabled:bg-gray-400"-->
			<!--				@click="nextPage"-->
			<!--			>-->
			<!--				&gt;-->
			<!--			</button>-->
		</div>
	</div>
</template>

<style scoped>
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 20;
  backdrop-filter: blur(5px);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.progress-wrapper {
  width: 70%;
  max-width: 400px;
  text-align: center;
  padding: 30px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(2, 100, 250, 0.15);
}

.progress-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.spinner {
  animation: rotate 2s linear infinite;
  width: 50px;
  height: 50px;
}

.path {
  stroke: #0264FA;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.progress-text {
  color: #0264FA;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
  font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  text-shadow: 0 1px 2px rgba(2, 100, 250, 0.1);
}

.progress-bar-container {
  width: 100%;
  height: 12px;
  background-color: #e6f0ff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #0264FA 0%, #3a86ff 100%);
  border-radius: 10px;
  transition: width 0.4s cubic-bezier(0.22, 0.61, 0.36, 1);
  box-shadow: 0 2px 8px rgba(2, 100, 250, 0.3);
}
.pdf-viewer {
	position: relative;
	/* height: 100vh; */
}

.pdf-container {
	position: relative;
	overflow: hidden;
	min-height: 50vh; /* 为加载动画提供最小高度 */
}

.text-layer {
	cursor: text;
	user-select: text;
	-webkit-user-select: text;
	-moz-user-select: text;
	-ms-user-select: text;
}

/* 为有注释的区域添加特殊光标样式 */
.text-layer.has-annotations {
	cursor: pointer;
}

.selected-text-highlight {
	pointer-events: none;
	z-index: 15;
	border-radius: 2px;
	box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
	animation: selectedTextPulse 2s ease-in-out;
}

@keyframes selectedTextPulse {
	0% {
		opacity: 0;
		transform: scale(0.95);
	}
	50% {
		opacity: 1;
		transform: scale(1.02);
	}
	100% {
		opacity: 0.8;
		transform: scale(1);
	}
}

/* 允许文本选择 */
.text-layer * {
	user-select: text;
	-webkit-user-select: text;
	-moz-user-select: text;
	-ms-user-select: text;
}

/* 简化版分页控件样式 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 0;
  margin-top: 24px !important;
}

/* 分页按钮样式 - 极简设计 */
:deep(.n-button) {
  background: transparent !important;
  border: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: none !important;
  width: 36px;
  height: 36px;
  min-width: 36px;
  padding: 0;
  border-radius: 50% !important;
}

:deep(.n-button:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
  transform: none !important;
}

:deep(.n-button:active) {
  transform: scale(0.92) !important;
  background: rgba(59, 130, 246, 0.15) !important;
}

:deep(.n-button:disabled) {
  background: transparent !important;
  opacity: 0.4 !important;
  box-shadow: none !important;
  transform: none !important;
}

:deep(.n-button .n-button__icon) {
  color: #4b5563 !important;
  font-size: 18px !important;
  transition: all 0.3s ease !important;
}

:deep(.n-button:hover .n-button__icon) {
  color: #3b82f6 !important;
}

/* 页码输入框样式 - 极简设计 */
:deep(.n-input-number) {
  width: 60px !important;
  border-radius: 8px !important;
  border: 1px solid #e5e7eb !important;
  transition: all 0.3s ease !important;
  box-shadow: none !important;
  background: transparent !important;
}

:deep(.n-input-number:hover) {
  border-color: #d1d5db !important;
}

:deep(.n-input-number:focus-within) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

:deep(.n-input-number .n-input) {
  border: none !important;
  background: transparent !important;
  text-align: center !important;
  font-weight: 500 !important;
  color: #374151 !important;
  font-size: 14px !important;
  padding: 0 !important;
}

:deep(.n-input-number .n-input:focus) {
  box-shadow: none !important;
}

/* 页码文本样式 */
.pagination span {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin: 0 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination {
    gap: 4px;
  }

  :deep(.n-button) {
    width: 32px;
    height: 32px;
    min-width: 32px;
  }

  :deep(.n-input-number) {
    width: 50px !important;
  }

  .pagination span {
    font-size: 13px;
  }
}
</style>
