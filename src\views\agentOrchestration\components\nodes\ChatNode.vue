<template>
  <div class="chat-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />

    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/textGenerationIcon.png" alt="对话节点">
        <div class="node-title">{{ data.label || '对话节点' }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>

      <!-- 模型配置 -->
      <div class="chat-config">
        <div class="config-item">
          <span class="config-label">模型:</span>
          <span class="config-value">{{ data.config?.model || 'DeepSeek-R1' }}</span>
        </div>
      </div>
    </div>

    <!-- 输出连接点 -->
    <Handle
      type="source"
      :position="Position.Right"
      :id="`${id}-output`"
      class="output-handle"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'

interface ChatNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      model?: string
      temperature?: number
      maxTokens?: number
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<ChatNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.chat-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .chat-config {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .config-item {
        .node-list-item-style();

        .config-label {
          font-size: 11px;
          color: #6b7280;
          font-weight: 500;
        }

        .config-value {
          font-size: 11px;
          color: #9ca3af;
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
