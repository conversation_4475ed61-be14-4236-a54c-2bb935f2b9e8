import { ss } from '@/utils/storage'

const LOCAL_NAME = 'stretchoutStorage'

export interface stretchoutStorageInfos {
  documentisshow: boolean
  lessonShow?: boolean
  conversationContentId: string
	lessonText?: string
}

export interface stretchoutState {
  stretchoutStorageInfo: stretchoutStorageInfos
}

export function defaultSetting(): stretchoutState {
  return {
    stretchoutStorageInfo: {
      documentisshow: false,
      conversationContentId: '',
    },
  }
}

export function getLocalState(): stretchoutState {
  const localSetting: stretchoutState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalState(setting: stretchoutState): void {
  ss.set(LOCAL_NAME, setting)
}
