{"id": "sentiment-analysis-workflow", "name": "情感分析工作流", "description": "基于Dify工作流转换的情感分析流程，支持单情感和多情感分析", "nodes": [{"id": "start-node", "type": "start", "position": {"x": 100, "y": 200}, "data": {"label": "开始节点", "description": "情感分析工作流的起始点", "status": "idle", "config": {"variables": [{"name": "input_text", "type": "string", "description": "待分析的文本内容", "required": true, "defaultValue": ""}, {"name": "Multisentiment", "type": "string", "description": "是否进行多情感分析", "required": true, "defaultValue": "False", "options": ["True", "False"]}, {"name": "Categories", "type": "string", "description": "情感分类类别", "required": false, "defaultValue": "正面,负面,中性"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": false}, {"id": "condition-node", "type": "condition", "position": {"x": 400, "y": 200}, "data": {"label": "条件判断", "description": "判断是否进行多情感分析", "status": "idle", "config": {"condition": "{{Multisentiment}} == \"True\"", "conditionDesc": "判断Multisentiment参数是否为True", "variable": "Multisentiment", "operator": "equals", "value": "True", "logicalOperator": "and", "conditions": [{"variable": "Multisentiment", "operator": "equals", "value": "True"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": true}, {"id": "llm-single-sentiment", "type": "llm", "position": {"x": 700, "y": 100}, "data": {"label": "LLM节点", "description": "执行单情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本的情感倾向，并给出一个主要的情感标签。\n\n文本：{{input_text}}\n\n请从以下类别中选择最符合的情感：{{Categories}}\n\n请直接回答情感标签，不需要解释。", "systemPrompt": "你是一个专业的情感分析助手，能够准确识别文本的情感倾向。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": true}, {"id": "llm-multi-sentiment", "type": "llm", "position": {"x": 700, "y": 300}, "data": {"label": "LLM节点", "description": "执行多情感分析的LLM节点", "status": "idle", "config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "prompt": "你是一个情感分析专家。请分析以下文本可能包含的多种情感，并给出每种情感的强度评分（0-1之间）。\n\n文本：{{input_text}}\n\n请从以下类别中分析：{{Categories}}\n\n请以JSON格式返回结果，例如：\n{\n  \"正面\": 0.8,\n  \"负面\": 0.2,\n  \"中性\": 0.1\n}", "systemPrompt": "你是一个专业的情感分析助手，能够识别文本中的多种情感并给出强度评分。", "variables": [{"name": "input_text", "type": "string", "source": "start-node.input_text"}, {"name": "Categories", "type": "string", "source": "start-node.Categories"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": true}, {"id": "end-single", "type": "end", "position": {"x": 1000, "y": 100}, "data": {"label": "结束节点", "description": "输出单情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_result", "type": "string", "source": "llm-single-sentiment.output", "description": "单情感分析结果"}, {"name": "analysis_type", "type": "string", "value": "single", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": false}, {"id": "end-multi", "type": "end", "position": {"x": 1000, "y": 300}, "data": {"label": "结束节点", "description": "输出多情感分析结果", "status": "idle", "config": {"outputVariables": [{"name": "sentiment_scores", "type": "object", "source": "llm-multi-sentiment.output", "description": "多情感分析评分结果"}, {"name": "analysis_type", "type": "string", "value": "multi", "description": "分析类型标识"}]}, "executionLogs": [], "lastExecution": null, "totalExecutions": 0}, "draggable": true, "selectable": true, "deletable": false}], "edges": [{"id": "start-to-condition", "source": "start-node", "target": "condition-node", "sourceHandle": "start-node-output", "targetHandle": "condition-node-input", "animated": false, "style": {"stroke": "#64748b", "strokeWidth": 2}, "data": {"label": "开始流程"}}, {"id": "condition-to-single", "source": "condition-node", "target": "llm-single-sentiment", "sourceHandle": "condition-node-false", "targetHandle": "llm-single-sentiment-input", "animated": false, "style": {"stroke": "#ef4444", "strokeWidth": 2}, "data": {"label": "单情感分析", "condition": "false"}}, {"id": "condition-to-multi", "source": "condition-node", "target": "llm-multi-sentiment", "sourceHandle": "condition-node-true", "targetHandle": "llm-multi-sentiment-input", "animated": false, "style": {"stroke": "#22c55e", "strokeWidth": 2}, "data": {"label": "多情感分析", "condition": "true"}}, {"id": "single-to-end", "source": "llm-single-sentiment", "target": "end-single", "sourceHandle": "llm-single-sentiment-output", "targetHandle": "end-single-input", "animated": false, "style": {"stroke": "#64748b", "strokeWidth": 2}, "data": {"label": "输出结果"}}, {"id": "multi-to-end", "source": "llm-multi-sentiment", "target": "end-multi", "sourceHandle": "llm-multi-sentiment-output", "targetHandle": "end-multi-input", "animated": false, "style": {"stroke": "#64748b", "strokeWidth": 2}, "data": {"label": "输出结果"}}], "createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "metadata": {"originalSource": "dify-workflow", "version": "1.0.0", "convertedAt": "2024-01-15T10:00:00.000Z", "description": "从Dify工作流YAML转换而来的情感分析流程", "tags": ["情感分析", "条件分支", "LLM", "多路输出"]}}