const fs = require('fs');
const path = require('path');
const { Client } = require('ssh2');

// 获取命令行参数
const configFilePath = process.argv[2];
console.log(`config_file_path: ${configFilePath}`);

// 读取并解析配置文件
const config = JSON.parse(fs.readFileSync(configFilePath, 'utf8'));
console.log(config['hostname']);

const conn = new Client();

conn
  .on('ready', async () => {
    console.log('Client :: ready => 服务器连接成功');

    // 执行 beforeScript 命令
    if (config.cmdTask.beforeScript.length > 0) {
      for (let i = 0; i < config.cmdTask.beforeScript.length; i++) {
        console.log(`开始执行beforeScript命令==> ${config.cmdTask.beforeScript[i]}`);
        await execRemoteCommand(conn, config.cmdTask.beforeScript[i]);
      }
    }

    // 上传文件或文件夹
    await uploadFileOrFolder(conn, config);

    // 执行 afterScript 命令
    if (config.cmdTask.afterScript.length > 0) {
      for (let i = 0; i < config.cmdTask.afterScript.length; i++) {
        console.log(`开始执行afterScript命令==> ${config.cmdTask.afterScript[i]}`);
        await execRemoteCommand(conn, config.cmdTask.afterScript[i]);
      }
    }
    // 退出连接
    conn.end();
  })
  .connect({
    host: config.hostname,
    port: config.port,
    username: config.username,
    password: config.password,
  });

conn.on('close', () => {
  console.log('Connection :: close');
});

conn.on('end', () => {
  console.log('Connection :: end');
});

conn.on('error', (err) => {
  console.error(`Connection :: error :: ${err}`);
});

// 实现sftp 上传文件或文件夹到服务器，参数 clicent localPath remotePath 返回 Promise
const uploadFileOrFolder = (client, config) => {
  return new Promise((resolve) => {
    conn.sftp((err, sftp) => {
      if (err) throw err;

      const uploadRecursive = (localPath, remotePath) => {
        if (fs.lstatSync(localPath).isDirectory()) {
          sftp.mkdir(remotePath, { mode: 0755 }, (err) => {
            if (err && err.code !== 4) {
              // Ignore the error if the directory already exists
              console.error(err);
              return;
            }
            fs.readdirSync(localPath).forEach((fileName) => {
              uploadRecursive(path.join(localPath, fileName), path.join(remotePath, fileName));
            });
          });
        } else {
          console.log(`开始上传... ${localPath} 到 ${remotePath}...`);
          sftp.fastPut(localPath, remotePath, (err) => {
            if (err) {
              console.error(err);
            } else {
              console.log(`上传完成: ${localPath} to ${remotePath}`);
              resolve();
            }
          });
        }
      };

      uploadRecursive(config.uploadTask.localPath, config.uploadTask.remotePath);
    });
  });
};

// 实现执行远程命令函数 参数 client, command 返回 Promise
const execRemoteCommand = (client, command) => {
  return new Promise((resolve, reject) => {
    client.exec(command, (err, stream) => {
      if (err) {
        reject(err);
      }
      stream
        .on('close', (code, signal) => {
          console.log(`Stream :: close :: code: ${code}, signal: ${signal}`);
          if (code == 0) {
            console.log(`命令执行成功: ${command}`);
          } else {
            console.log(`命令执行失败: ${command}`);
          }
          resolve();
        })
        .on('data', (data) => {
          console.log(`STDOUT: ${data}`);
        })
        .stderr.on('data', (data) => {
          console.error(`STDERR: ${data}`);
        });
    });
  });
};
