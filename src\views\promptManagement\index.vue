<template>
  <div class="app pt-[32px] pr-[48px] pl-[48px] pb-[80px]">
    <header class="bothends flex justify-between items-center">
      <div
        class="title h-9 font-semibold text-[26px] text-[#2f3033] leading-9 flex items-center"
      >
        <img
          alt=""
          class="w-[22px] h-[22px] mr-2"
          src="@/assets/toolboxPage/titicon.png"
        />
        提示词管理
      </div>
    </header>

    <n-tabs
      v-model:value="activeTab"
      type="line"
      size="large"
      class="flex-1 mt-[20px]"
    >
      <n-tab-pane name="my" tab="我的提示词">
        <MyPrompts />
      </n-tab-pane>
      <n-tab-pane name="platform" tab="平台提示词">
        <PlatformPrompts />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { NTabs, NTabPane } from "naive-ui";
import MyPrompts from "./components/MyPrompts.vue";
import PlatformPrompts from "./components/PlatformPrompts.vue";

const activeTab = ref("my");
</script>

<style lang="less" scoped>
.n-tabs {
  height: 100%;
}

/deep/ .n-tabs-tab {
  font-family: PingFangSC-Medium !important;
  font-weight: 500 !important;
  font-size: 18.62px !important;
  color: #000000 !important;
  line-height: 29.26px !important;
}
/deep/ .n-tabs-tab--active {
  color: #125eff !important;
}
/deep/ .n-tabs.n-tabs--top .n-tab-pane {
  padding-top: 20px;
}
</style>
