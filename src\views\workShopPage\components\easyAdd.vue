<script lang="ts" setup>
import { computed, onMounted, reactive, ref, useTemplateRef } from "vue";
import {
  NButton,
  NCard,
  NDataTable,
  NDynamicInput,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NModal,
  NRadio,
  NRadioGroup,
  NSelect,
  NSwitch,
  useMessage,
} from "naive-ui";
import type { DataTableRowKey, FormInst } from "naive-ui";
import { useRoute, useRouter } from "vue-router";
import { QuillEditor } from "@vueup/vue-quill";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
import { SvgIcon } from "@/components/common";
import ChatPreview from "./ChatPreview.vue";
import {
  addConversation,
  addShop,
  agentKnowledge,
  agentParam,
  editShop,
  getAgentKnowledge,
  getAgentlistId,
  museModels,
  optimize,
} from "@/api/workShop";

const router = useRouter();
const route = useRoute();

const formRef = ref<FormInst | null>(null);
const message = useMessage();
const size = ref("medium");
const params = ref(JSON.parse(route.query.params as string));
const edit = route.query.edit;
const nameEdit = ref(false);
console.log(edit, params);
const radioCustomList = ref([
  {
    value: "0",
    label: "默认",
    note: "每次对话后自动生成问题建议",
  },
  {
    value: "1",
    label: "自定义",
    note: "自定义",
    disabled: true,
  },
]);
const showModal = ref(false);
// 表单数据
const model = ref({
  knowledgeReferenceFlag: "0",
  multiTurnFlag: "0",
  fileUploadFlag: "0",
  webSearchFlag: "0",
  nextQuestionsSuggestion: "0",
});
// 模型弹窗数据
const modelDetails = ref({});
// 知识库弹窗数据
const storeDetails = ref({});

const songs = ref([
  {
    value: "0",
    label: "创意",
  },
  {
    value: "1",
    label: "平衡",
  },
  {
    value: "2",
    label: "严肃",
  },
]);

const rules = {
  modelId: {
    required: true,
    message: "请选择模型",
    trigger: "input",
  },
  promptTemplate: {
    required: true,
    message: "请输入提示词",
    trigger: "input",
  },
};

const options = ref([
  {
    label: "Everybody's Got Something to Hide Except Me and My Monkey",
    value: "song0",
    disabled: true,
  },
  {
    label: "Drive My Car",
    value: "song1",
  },
]);

function jumpPage() {
  router.go(-1);
}
const updateRadio = (val: any) => {
  model.value.modelTemp = Number(val);
};

// 添加知识库弹窗
const data = ref([]);
const table = ref(null);
const loading = ref(true);
const ShowAddStore = ref(false);
const searchvalue = ref();
// 状态管理
const checkedRowKeys = ref<DataTableRowKey[]>([]);
const checkedRows = ref<any[]>([]);
// 创建列配置
const createColumns = () => [
  {
    type: "selection",
    // disabled: (row: any) => row.name === 'Edward King 3',
  },
  {
    title: "智库名称",
    key: "storeName",
  },
  {
    title: "文件数量",
    key: "fileNum",
  },
];

// 表格配置
const columns = createColumns();
const rowKey = (row: any) => row.id;
const pagination = reactive({
  page: 1,
  pageCount: 1,
  pageSize: 7,
  // prefix({ itemCount }) {
  //   return `Total is ${itemCount}.`
  // },
});

// 选中数据
const handleCheck = (rowKeys: DataTableRowKey[], row: any) => {
  checkedRowKeys.value = rowKeys;
  checkedRows.value = row;
  // console.log(checkedRowKeys.value)
};
// 知识库列表
const getList = async () => {
  loading.value = true;
  try {
    const agentKnowledgeList = await getAgentKnowledge({
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      storeName: searchvalue.value,
      departId: "1900435731371311106",
    });
    data.value = agentKnowledgeList.data.items;
    pagination.itemCount = agentKnowledgeList.data.total;
    pagination.pageCount = agentKnowledgeList.data.pageNum;
    console.log(agentKnowledgeList);
  } catch (error) {
    console.error("Error fetching table data:", error);
  } finally {
    loading.value = false;
  }
};
// 点击新增按钮
const addShowAddStore = async () => {
  ShowAddStore.value = true;
  await getList();
};
// 点击分页
const handlePageChange = async (currentPage: number) => {
  if (!loading.value) {
    pagination.page = currentPage;
    await getList();
  }
};
// 点击保存
const storeSave = () => {
  ShowAddStore.value = false;
  console.log(checkedRowKeys.value, checkedRows.value);
};
// 删除选中数据
const delStore = (row: any) => {
  checkedRowKeys.value = checkedRowKeys.value.filter((item) => {
    return item !== row.id;
  });
  checkedRows.value = checkedRows.value.filter((item) => {
    return item.id !== row.id;
  });
};
// 新建知识库
const addModalTap = () => {
  router.push("/knowledgeFactoryPage");
};

// 知识库数据弹窗
const showStore = ref(false);
const storeSongs = ref([
  {
    value: "0",
    label: "强制调用",
  },
  {
    value: "1",
    label: "按需调用",
  },
]);
const searchCategorySongs = ref([
  {
    value: "0",
    label: "混合检索",
  },
  {
    value: "1",
    label: "简易检索",
  },
  {
    value: "2",
    label: "图谱检索",
  },
]);

// 一键优化
const optimizingLogding = ref(false);
const optimizing = async () => {
  optimizingLogding.value = true;
  try {
    const res = await optimize({
      modelTemp: model.value.modelTemp,
      promptTemplate: model.value.promptTemplate,
      modelId: model.value.modelId,
      maxLength: model.value.maxLength,
    });
    model.value.promptTemplate = res.data;
    console.log(res);
  } catch (e) {
    message.error("优化失败");
  } finally {
    optimizingLogding.value = false;
  }
};

onMounted(async () => {
  await agentKnowledge();
  const models = await museModels();
  options.value = models.data;
  const res = await agentParam();
  songs.value = res.data.model_style;
  // 给模型参数赋默认值
  model.value.topP = Number(res.data.model_topp);
  model.value.maxLength = Number(res.data.model_max_length);
  model.value.style = res.data.model_style[0].dictKey;
  model.value.modelTemp = Number(res.data.model_style[0].dictKey);
  if (edit === "true") {
    const detail = await getAgentlistId(params.value.id);
    checkedRows.value = detail.data.agentKnowledgeList;
    model.value = detail.data;
    model.value.style = String(detail.data.modelTemp);
    model.value.preQuestions = JSON.parse(model.value.preQuestions);
    checkedRowKeys.value = detail.data.agentKnowledgeList?.map(
      (item) => item.id
    );
    if (detail.data?.agentKnowledgeBaseDesc) {
      const { id, kid, searchCategory, callCategory, topK, minMatch } =
        detail.data?.agentKnowledgeBaseDesc;
      storeDetails.value = {
        id,
        callCategory,
        searchCategory,
        kid,
        topK,
        minMatch,
      };
      storeDetails.value.callCategory = String(storeDetails.value.callCategory);
      storeDetails.value.searchCategory = String(
        storeDetails.value.searchCategory
      );
    }
  }
});

// 提交
const submitLoading = ref(false);
// 提交需要的数据
const getMsg = computed(() => {
  return {
    ...{
      ...model.value,
      preQuestions: JSON.stringify(model.value.preQuestions),
    },
    agentKnowledgeList: checkedRows.value?.map((item) => ({ id: item.id })),
    agentKnowledgeBaseDesc: storeDetails.value,
    // ...modelDetails.value,
    ...params.value,
  };
});
const handleValidateButtonClick = async (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (!params.value.name) {
        message.warning("请填写智能体名称");
        return;
      }
      submitLoading.value = true;
      const obj = getMsg.value;
      console.log(obj);
      try {
        if (edit === "true") await editShop(obj, obj.id);
        else await addShop(obj);
        message.success("提交成功");
        submitLoading.value = false;
        await router.push("/workShopPage");
      } catch (e) {
        message.error(e);
      }
    }
  });
};

// 测试对话前处理
const useChat = ref();
const showChat = ref(false);
const agentId: any = ref(null);
const conversationId: any = ref(null);
const charRef = useTemplateRef("charRef");
const textConverse = async (question: any) => {
  const obj = getMsg.value;
  showChat.value = true;
  if (agentId.value && conversationId.value) {
    await editShop(obj, agentId.value);
    useChat.value = {
      question: question?.message,
      conversationId: conversationId.value,
      category: "0",
      modelId: model.value.modelId,
      modelTemp: model.value.modelTemp,
      maxLength: model.value.maxLength,
      promptTemplate: model.value.promptTemplate,
    };
    showChat.value = false;
    await charRef.value.textConversation(useChat.value, agentId.value);
  } else {
    // 创建智能体
    const res = await addShop({ ...obj, status: "2" });
    agentId.value = res.data.id;
    // 创建对话
    const data = await addConversation({
      agentId: res.data.id,
      title: question?.message,
      category: "0",
    });
    conversationId.value = data.data.id;
    useChat.value = {
      question: question?.message,
      conversationId: data.data.id,
      category: "0",
      modelId: model.value.modelId,
      modelTemp: model.value.modelTemp,
      maxLength: model.value.maxLength,
      promptTemplate: model.value.promptTemplate,
    };
    showChat.value = false;
    await charRef.value.textConversation(useChat.value, res.data.id);
  }
};

const submitFlag = computed(() => {
  return model.value.modelId;
});
</script>

<template>
  <div class="p-6">
    <div class="flex items-center justify-between mb-[30px]">
      <div class="flex items-center">
        <img
          class="w-[36px] cursor-pointer"
          src="@/assets/workShopPage/rest.png"
          alt=""
          @click="jumpPage"
        />
        <div v-if="!nameEdit" class="flex items-center">
          <p class="text-[20px] ml-4">
            {{ params.name }}
          </p>
          <SvgIcon
            class="cursor-pointer ml-[14px] text-[26px] text-[#999]"
            icon="line-md:edit"
            @click="nameEdit = true"
          />
        </div>
        <div v-else class="flex items-center ml-[8px]">
          <NInput v-model:value="params.name" />
          <SvgIcon
            class="cursor-pointer ml-[4px] text-[32px] text-[#999]"
            icon="line-md:confirm"
            @click="nameEdit = false"
          />
        </div>
      </div>
      <NButton
        round
        type="primary"
        :loading="submitLoading"
        @click="handleValidateButtonClick"
      >
        提交
      </NButton>
    </div>
    <div class="flex gap-6">
      <div class="w-[70%]">
        <NForm
          ref="formRef"
          :model="model"
          :rules="rules"
          :size="size"
          label-placement="top"
        >
          <div class="flex h-full">
            <div class="w-1/2">
              <div
                class="mb-[18px] text-[12px] text-[#666] indent-[4px] font-semibold border-l-[3px] border-[#666] h-[17px]"
              >
                模型设置
              </div>
              <NFormItem label="模型" path="modelId">
                <NSelect
                  v-model:value="model.modelId"
                  :options="options"
                  value-field="id"
                  label-field="name"
                />
                <SvgIcon
                  class="cursor-pointer ml-[14px] text-[26px] text-[#666]"
                  icon="material-symbols:edit-note-outline"
                  @click="showModal = true"
                />
              </NFormItem>
              <NFormItem label="提示词" path="promptTemplate" class="prompt">
                <template #label>
                  <div class="">提示词</div>
                  <div class="absolute right-0 bottom-1">
                    <NButton
                      :loading="optimizingLogding"
                      :disabled="
                        !model.promptTemplate?.length || !model.modelId?.length
                      "
                      color="#125EFF"
                      text
                      @click="optimizing"
                    >
                      一键优化
                    </NButton>
                  </div>
                </template>
                <NInput
                  v-model:value="model.promptTemplate"
                  placeholder="提示词"
                  type="textarea"
                  :autosize="{
                    minRows: 6,
                    maxRows: 6,
                  }"
                />
              </NFormItem>
              <div
                class="mb-[18px] text-[12px] text-[#666] indent-[4px] font-semibold border-l-[3px] border-[#666] h-[17px]"
              >
                知识
              </div>
              <NFormItem class="store" path="inputValue">
                <template #label>
                  <div class="flex justify-between items-center">
                    <div class="">知识库</div>
                    <div class="flex items-center">
                      <SvgIcon
                        class="cursor-pointer text-[22px] text-[#666]"
                        icon="material-symbols:add-2"
                        @click="addShowAddStore"
                      />
                      <SvgIcon
                        class="cursor-pointer ml-[14px] text-[26px] text-[#666]"
                        icon="material-symbols:edit-note-outline"
                        @click="showStore = true"
                      />
                    </div>
                  </div>
                </template>
                <div class="w-full gap-1 flex flex-col">
                  <NCard
                    v-for="item in checkedRows"
                    :key="item.id"
                    size="small"
                  >
                    <div class="flex w-full justify-between items-center">
                      <div class="">
                        {{ item.storeName }}
                      </div>
                      <SvgIcon
                        icon="material-symbols:delete-outline"
                        class="text-[20px] text-[#666] cursor-pointer"
                        @click="delStore(item)"
                      />
                    </div>
                  </NCard>
                </div>
              </NFormItem>
              <NFormItem
                label-placement="left"
                label="知识引用"
                path="knowledgeReferenceFlag"
              >
                <NSwitch
                  v-model:value="model.knowledgeReferenceFlag"
                  checked-value="1"
                  unchecked-value="0"
                />
              </NFormItem>
            </div>
            <div class="w-[1px] bg-[#E0E0E0] mx-[20px]" />
            <div class="w-1/2">
              <div
                class="mb-[18px] text-[12px] text-[#666] indent-[4px] font-semibold border-l-[3px] border-[#666] h-[17px]"
              >
                对话设置
              </div>
              <h5 class="ml-[2px] text-[16px] mb-[14px] font-semibold">
                开场白
              </h5>
              <NFormItem label="开场白文案" path="openingWords">
                <div class="quill-editor-container">
                  <QuillEditor
                    v-model:content="model.openingWords"
                    content-type="html"
                    :toolbar="[
                      [{ header: [1, 2, 3, false] }],
                      ['bold', 'italic', 'underline', 'strike'],
                      [{ color: [] }, { background: [] }],
                      [{ align: [] }],
                      ['clean'],
                    ]"
                    theme="snow"
                    placeholder="请输入开场白内容..."
                  />
                </div>
              </NFormItem>
              <NFormItem label="预置问题" path="preQuestions">
                <NDynamicInput
                  v-model:value="model.preQuestions"
                  show-sort-button
                  placeholder="请输入"
                  :min="0"
                  :max="5"
                />
              </NFormItem>
              <NFormItem
                label-placement="left"
                label="多轮对话"
                path="multiTurnFlag"
              >
                <NSwitch
                  v-model:value="model.multiTurnFlag"
                  checked-value="1"
                  unchecked-value="0"
                />
              </NFormItem>
              <NFormItem
                label-placement="left"
                label="文件上传"
                path="fileUploadFlag"
              >
                <NSwitch
                  v-model:value="model.fileUploadFlag"
                  checked-value="1"
                  unchecked-value="0"
                />
              </NFormItem>
              <NFormItem
                label-placement="left"
                label="联网搜索"
                path="webSearchFlag"
              >
                <NSwitch
                  v-model:value="model.webSearchFlag"
                  checked-value="1"
                  unchecked-value="0"
                />
              </NFormItem>
              <!--          <NFormItem label-placement="left" label="下一步问题建议" path="nextQuestionsSuggestion"> -->
              <!--            <NSwitch v-model:value="model.nextQuestionsSuggestion" checked-value="1" unchecked-value="0" /> -->
              <!--          </NFormItem> -->
              <!--          <NFormItem label-placement="left" label=""> -->
              <!--            <radioCustom :list="radioCustomList" value="0" @change="radioCustomChange" /> -->
              <!--          </NFormItem> -->
            </div>
          </div>
        </NForm>
      </div>
      <div class="w-[30%]">
        <div class="border w-full h-[80vh]">
          <ChatPreview
            :title="params.name"
            :description="params.description"
            :opening-words="model.openingWords"
            :pre-questions="model.preQuestions"
            :loading="false"
            :agent-id="params.id || 'preview'"
            :model-id="model.modelId"
            :model-temp="model.modelTemp"
            :max-length="model.maxLength"
            :prompt-template="model.promptTemplate"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- 模型参数 -->
  <NModal v-model:show="showModal">
    <NCard
      style="width: 500px"
      title="模型参数"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div class="">
        <NForm
          ref="modelRef"
          label-width="auto"
          require-mark-placement="right-hanging"
          label-placement="left"
        >
          <NFormItem label="模型风格">
            <NRadioGroup
              v-model:value="model.style"
              name="radioGroup1"
              @update:value="updateRadio"
            >
              <NRadio
                v-for="song in songs"
                :key="song.dictKey"
                :value="song.dictKey"
                :label="song.dictValue"
              />
            </NRadioGroup>
          </NFormItem>
          <NFormItem label-placement="left" label="模型温度">
            <NInputNumber
              v-model:value="model.modelTemp"
              :readonly="true"
              :show-button="false"
            />
          </NFormItem>
          <NFormItem label-placement="left" label="Top P">
            <NInputNumber
              v-model:value="model.topP"
              :readonly="true"
              :show-button="false"
            />
          </NFormItem>
          <NFormItem label-placement="left" label="最大输出长度">
            <NInputNumber
              v-model:value="model.maxLength"
              :readonly="true"
              :show-button="false"
            />
          </NFormItem>
        </NForm>
      </div>
      <template #footer>
        <div class="flex flex-row-reverse w-full">
          <NButton type="info" @click="showModal = false"> 保存 </NButton>
          <NButton class="!mr-5" @click="showModal = false"> 取消 </NButton>
        </div>
      </template>
    </NCard>
  </NModal>

  <!-- 添加知识库 -->
  <NModal v-model:show="ShowAddStore">
    <NCard
      style="width: 800px"
      title="添加知识库"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div class="">
        <div class="flex mb-5 justify-between">
          <!--          <NInput v-model:value="searchvalue" placeholder="搜索智能体" size="small"> -->

          <!--          </NInput> -->
          <NInput
            v-model:value="searchvalue"
            placeholder="搜索知识库"
            class="!w-80"
          >
            <template #suffix>
              <div
                class="bg-[#EBF3FF] rounded-full flex items-center justify-center"
              >
                <img
                  class="w-[20px] cursor-pointer"
                  src="@/assets/toolboxPage/SearchOutline.png"
                  @click="getList"
                />
              </div>
            </template>
          </NInput>
          <NButton v-show="!data?.length" type="info" @click="addModalTap">
            新建知识库
          </NButton>
        </div>
        <NDataTable
          ref="table"
          v-model:checked-row-keys="checkedRowKeys"
          remote
          :columns="columns"
          :data="data"
          :loading="loading"
          :pagination="pagination"
          :row-key="rowKey"
          @update:page="handlePageChange"
          @update:checked-row-keys="handleCheck"
        />
      </div>
      <template #footer>
        <div class="flex flex-row-reverse w-full">
          <NButton type="info" @click="storeSave"> 保存 </NButton>
          <NButton class="!mr-5" @click="ShowAddStore = false"> 取消 </NButton>
        </div>
      </template>
    </NCard>
  </NModal>

  <!-- 知识库参数 -->
  <NModal v-model:show="showStore">
    <NCard
      style="width: 600px"
      title="知识库参数"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <div class="">
        <NForm
          ref="modelRef"
          :model="storeDetails"
          label-width="auto"
          require-mark-placement="right-hanging"
          label-placement="left"
        >
          <NFormItem label="调用方式">
            <NRadioGroup
              v-model:value="storeDetails.callCategory"
              name="radioGroup2"
            >
              <NRadio
                v-for="song in storeSongs"
                :key="song.value"
                :value="song.value"
                :label="song.label"
              />
            </NRadioGroup>
          </NFormItem>
          <NFormItem label="检索模式">
            <NRadioGroup
              v-model:value="storeDetails.searchCategory"
              name="radioGroup3"
            >
              <NRadio
                v-for="song in searchCategorySongs"
                :key="song.value"
                :value="song.value"
                :label="song.label"
              />
            </NRadioGroup>
          </NFormItem>
          <NFormItem label-placement="left" label="返回结果数量 （Top K）">
            <NInputNumber
              v-model:value="storeDetails.topK"
              :min="0"
              :show-button="false"
            />
          </NFormItem>
          <NFormItem label-placement="left" label="最小匹配度 （阈值）">
            <NInputNumber
              v-model:value="storeDetails.minMatch"
              :min="0"
              :show-button="false"
            />
          </NFormItem>
        </NForm>
      </div>
      <template #footer>
        <div class="flex flex-row-reverse w-full">
          <NButton type="info" @click="showStore = false"> 保存 </NButton>
          <NButton class="!mr-5" @click="showStore = false"> 取消 </NButton>
        </div>
      </template>
    </NCard>
  </NModal>
</template>

<style scoped lang="less">
.store {
  :deep(.n-form-item-label__text) {
    width: 100%;
  }
}
.prompt {
  :deep(.n-form-item-label) {
    position: relative;
  }
}
// QuillEditor 样式调整
.quill-editor-container {
  :deep(.ql-toolbar) {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
  }

  :deep(.ql-container) {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 6px 6px;
    min-height: 120px;
  }

  :deep(.ql-editor) {
    min-height: 100px;
    font-size: 14px;
    line-height: 1.5;
    padding: 12px 15px;
  }

  :deep(.ql-editor.ql-blank::before) {
    font-style: normal;
    color: #999;
  }
}
</style>
