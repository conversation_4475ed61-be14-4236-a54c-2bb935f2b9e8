import { dataURLtoBlob, urlToBase64 } from './base64Conver';

/**
 * Download online pictures
 * @param url
 * @param filename
 * @param mime
 * @param bom
 */
export function downloadByOnlineUrl(url: string, filename: string, mime?: string, bom?: BlobPart) {
  urlToBase64(url).then((base64) => {
    downloadByBase64(base64, filename, mime, bom);
  });
}

/**
 * Download pictures based on base64
 * @param buf
 * @param filename
 * @param mime
 * @param bom
 */
export function downloadByBase64(buf: string, filename: string, mime?: string, bom?: BlobPart) {
  const base64Buf = dataURLtoBlob(buf);
  downloadByData(base64Buf, filename, mime, bom);
}

/**
 * Download according to the background interface file stream
 * @param {*} data
 * @param {*} filename
 * @param {*} mime
 * @param {*} bom
 */
export function downloadByData(data:any, fileName:string) {
  const fileExtensionMap = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.txt': 'text/plain',
    '.bin': 'application/octet-stream'
};

const fileExtension = fileName.slice((fileName.lastIndexOf('.') - 1 >>> 0) + 1).toLowerCase();
const fileType = fileExtensionMap[`.${fileExtension}`] || 'application/octet-stream';

// 创建一个 Blob 对象
const blob = new Blob([data], { type: fileType });

// 创建一个 URL 对象
const url = URL.createObjectURL(blob);

// 创建一个 <a> 元素
const a = document.createElement('a');
a.href = url;
a.download = fileName;

// 模拟点击 <a> 元素来触发下载
a.click();

// 释放 URL 对象
URL.revokeObjectURL(url);
}
