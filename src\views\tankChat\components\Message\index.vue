<script lang='ts' setup>
import {computed, ref, watch} from "vue";
import {useMessage} from "naive-ui";
import AvatarComponent from "./Avatar.vue";
import TextComponent from "./Text.vue";
import {useIconRender} from "@/hooks/useIconRender";
import {t} from "@/locales";
import {useBasicLayout} from "@/hooks/useBasicLayout";
import {copyToClip} from "@/utils/copy";
import {getAudio} from "@/api/index";
import {base64ToInt16Array, pcmPlay} from "@/utils/audioPlayer";

interface Props {
	dateTime?: string;
	text?: string;
	answerList: any;
	endstatus: any;
	inversion?: boolean;
	error?: boolean;
	loading?: boolean;
	conversationId?: string;
	category?: string;
	conversationContentId: string;
	questionArr?: any;
	annotation?: any;
	btnFlag?: boolean;
	problem?: boolean;
	questionState?: number;
}

interface Emit {
	(ev: "regenerate", flag: boolean, annotation: any): void;

	(ev: "handleAnswer", msg: string): void;

	(ev: "delete"): void;
}

const props = defineProps<Props>();

const emit = defineEmits<Emit>();

// pcm播放实例
let pcmAudio: any;
const isPlay = ref(true);
const isspeeking = ref(false);

const {isMobile} = useBasicLayout();

const {iconRender} = useIconRender();

const message = useMessage();

const textRef = ref<HTMLElement>();

const asRawText = ref(props.inversion);

const messageRef = ref<HTMLElement>();

const options = computed(() => {
	const common = [
		{
			label: t("chat.copy"),
			key: "copyText",
			icon: iconRender({icon: "ri:file-copy-2-line"}),
		},
		{
			label: t("common.delete"),
			key: "delete",
			icon: iconRender({icon: "ri:delete-bin-line"}),
		},
	];

	if (!props.inversion) {
		common.unshift({
			label: asRawText.value ? t("chat.preview") : t("chat.showRawText"),
			key: "toggleRenderType",
			icon: iconRender({
				icon: asRawText.value ? "ic:outline-code-off" : "ic:outline-code",
			}),
		});
	}

	return common;
});

function handleSelect(key: "copyText" | "delete" | "toggleRenderType") {
	switch (key) {
		case "copyText":
			handleCopy();
			return;
		case "toggleRenderType":
			asRawText.value = !asRawText.value;
			return;
		case "delete":
			emit("delete");
	}
}

function handleRegenerate(flag: boolean) {
	messageRef.value?.scrollIntoView();
	emit("regenerate", flag, props.annotation);
}

async function handleCopy() {
	try {
		await copyToClip(props.text || "");
		message.success(t("chat.copied"));
	} catch {
		message.error(t("chat.copyFailed"));
	}
}

// const str = ''
const iscanVoice = ref(false);
const resdata = ref([]);

// 文字转语音
async function handleSound() {
	isspeeking.value = true;
	iscanVoice.value = false;
	const res: any = await getAudio({
		msg: props.text,
		conversationId: props.conversationId,
		onDownloadProgress: ({event}) => {
			const xhr = event.target;
			const {responseText} = xhr;
			// Always process the final line
			// console.log(responseText)
			const result = responseText.split("\n");
			const dataresult = result.filter((item: any) => item.includes("data"));
			resdata.value = dataresult.map((ele: any) => {
				const returndata = JSON.parse(ele.split("data:")[1]);
				if (returndata.status === "1") iscanVoice.value = true;
				return returndata;
			});
			console.log(resdata.value);
			try {
			} catch (error) {
			}
		},
	});
}

watch(
	() => iscanVoice.value,
	() => {
		if (iscanVoice.value) {
			resdata.value.forEach((ele: any) => {
				if (ele.answer) {
					const audioChunk = atob(ele.answer);
					if (!pcmAudio) {
						// 实时播放
						pcmAudio = pcmPlay(
							16000,
							() => {
								isPlay.value = false;
								isspeeking.value = false;
							},
							{
								channels: 1,
								flushTime: 2000,
							}
						);
					}
					pcmAudio.feed(base64ToInt16Array(audioChunk));
				}
			});
		}
	}
);

// 暂停播放
function handleStopSound() {
	isspeeking.value = false;
	isPlay.value = false;
	pcmAudio.pause();
	pcmAudio = undefined;
}
</script>

<template>
	<div
		ref="messageRef"
		:class="[{ 'flex-row-reverse': inversion }]"
		class="flex w-full mb-6 overflow-hidden"
	>
		<div
			v-show="!inversion"
			:class="[inversion ? 'ml-2' : 'mr-2']"
			class="flex items-center justify-center flex-shrink-0 h-14 overflow-hidden rounded-full basis-8"
		>
			<AvatarComponent :image="inversion"/>
		</div>
		<div
			:class="[inversion ? 'items-end' : 'items-start']"
			class="overflow-hidden text-sm"
		>
			<!--      <p class="text-xs text-[#b4bbc4]" :class="[inversion ? 'text-right' : 'text-left']"> -->
			<!--        {{ dateTime }} -->
			<!--      </p> -->
			<div
				:class="[inversion ? 'flex-row-reverse' : 'flex-row']"
				class="flex items-end gap-1 mt-2"
			>
				<TextComponent
					ref="textRef"
					:annotation="annotation"
					:answer-list="answerList"
					:as-raw-text="asRawText"
					:btnFlag="btnFlag"
					:category="category"
					:conversationContentId="conversationContentId"
					:endstatus="endstatus"
					:error="error"
					:inversion="inversion"
					:is-play="isspeeking"
					:loading="loading"
					:problem="problem"
					:questionArr="questionArr"
					:questionState="questionState"
					:text="text"
					@mark="$emit('mark',annotation)"
					@handle-answer="(msg:any) => $emit('handleAnswer', msg)"
					@handle-copy="handleCopy"
					@handle-sound="handleSound"
					@handle-stop-sound="handleStopSound"
					@handle-regenerate="handleRegenerate"
				/>
				<div class="flex flex-col">
					<!--          <button -->
					<!--            v-if="!inversion" -->
					<!--            class="mb-2 transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-300" -->
					<!--            @click="handleRegenerate" -->
					<!--          > -->
					<!--            <SvgIcon icon="ri:restart-line" /> -->
					<!--          </button> -->
					<!--          <NDropdown -->
					<!--            :trigger="isMobile ? 'click' : 'hover'" -->
					<!--            :placement="!inversion ? 'right' : 'left'" -->
					<!--            :options="options" -->
					<!--            @select="handleSelect" -->
					<!--          > -->
					<!--            <button class="transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-200"> -->
					<!--              <SvgIcon icon="ri:more-2-fill" /> -->
					<!--            </button> -->
					<!--          </NDropdown> -->
				</div>
			</div>
		</div>
	</div>
</template>
