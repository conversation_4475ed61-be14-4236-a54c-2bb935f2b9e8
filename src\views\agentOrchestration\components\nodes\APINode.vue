<template>
  <div class="api-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />
    
    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass">
        <div class="status-dot"></div>
      </div>
      
      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/toolIcon.png" alt="API调用">
        <div class="node-title">{{ data.label || 'API调用' }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>

      <!-- API配置信息 -->
      <div class="api-config">
        <div class="config-item" v-if="data.config?.method">
          <span class="config-label">方法:</span>
          <span class="config-value method-badge" :class="methodClass">{{ data.config.method }}</span>
        </div>
        <div class="config-item" v-if="data.config?.url">
          <span class="config-label">URL:</span>
          <span class="config-value">{{ truncatedUrl }}</span>
        </div>
      </div>
      
      <!-- 请求状态动画 -->
      <div v-if="isRunning" class="requesting-animation">
        <div class="wave-container">
          <div class="wave"></div>
          <div class="wave"></div>
          <div class="wave"></div>
        </div>
      </div>
    </div>
    
    <!-- 输出连接点 -->
    <Handle
      type="source"
      :position="Position.Right"
      :id="`${id}-output`"
      class="output-handle"
    />

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'

interface APINodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      method?: string
      url?: string
      headers?: string
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<APINodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})

const methodClass = computed(() => {
  const method = props.data.config?.method?.toUpperCase()
  switch (method) {
    case 'GET':
      return 'method-get'
    case 'POST':
      return 'method-post'
    case 'PUT':
      return 'method-put'
    case 'DELETE':
      return 'method-delete'
    default:
      return 'method-default'
  }
})

const hasConfig = computed(() => {
  return props.data.config && (
    props.data.config.method || 
    props.data.config.url
  )
})

const hasHeaders = computed(() => {
  return props.data.config?.headers && props.data.config.headers.trim() !== ''
})

const truncatedUrl = computed(() => {
  const url = props.data.config?.url || ''
  return url.length > 40 ? url.substring(0, 40) + '...' : url
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.api-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .api-config {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .config-item {
        .node-list-item-style();

        .config-label {
          font-size: 11px;
          color: #6b7280;
          font-weight: 500;
        }

        .config-value {
          font-size: 11px;
          color: #9ca3af;

          &.method-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;

            &.method-get {
              background: #e6f7ff;
              color: #1890ff;
            }

            &.method-post {
              background: #f6ffed;
              color: #52c41a;
            }

            &.method-put {
              background: #fff7e6;
              color: #fa8c16;
            }

            &.method-delete {
              background: #fff2f0;
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
