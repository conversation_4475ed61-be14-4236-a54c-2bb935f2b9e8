import { get, post } from '@/utils/request'
import request from '@/utils/request/axios'

export function getknowledgelistApi<T = any>(data: any) {
  return post<T>({
    url: '/emind/knowledge_base/list',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
export function synchronousKnowledgeApi<T = any>(data: any) {
  return get<T>({
    url: '/emind/agentKnowledge/sync',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function addknowledgeApi<T = any>(data: any) {
  return post<T>({
    url: '/emind/knowledge_base',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function updateknowledgeApi<T = any>(data: any) {
  return post<T>({
    url: '/emind/knowledge_base/update',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function delknowledgeApi<T = any>(data: any) {
  return get<T>({
    url: '/emind/knowledge_base/delete',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function knowledgeuploadApi<T = any>(data: any) {
  return post<T>({
    url: '/emind/knowledge_base/upload',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function savefilesApi<T = any>(data: any) {
  return post<T>({
    url: '/emind/knowledge_base/save_files',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function downloadfileApi<T = any>(data: any) {
  return request({
    method: 'GET',
    url: '/emind/knowledge_base/download',
    params: data,
    responseType: 'blob'
  })
}

export function getfilelistApi<T = any>(data: any) {
  return post<T>({
    url: '/emind/knowledge_base/file_list',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function deletefileApi<T = any>(data: any) {
  return get<T>({
    url: '/emind/knowledge_base/file_delete',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function parsefileApi<T = any>(data: any) {
  return get<T>({
    url: '/emind/knowledge_base/parse_file',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function graphbuildingApi<T = any>(data: any) {
  return get<T>({
    url: '/emind/knowledge_base/graph_building',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}


