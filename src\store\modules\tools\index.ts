import { defineStore } from 'pinia'
import type { ToolInfos, ToolLod, ToolsState } from './helper'
import { getLocalState, setLocalState } from './helper'

export const useToolsStore = defineStore('tool-store', {
  state: (): ToolsState => getLocalState(),
  actions: {
    updateToolInfo(userInfo: Partial<ToolInfos>) {
      this.ToolInfo = { ...this.ToolInfo, ...userInfo }
      this.recordState()
    },
    recordState() {
      setLocalState(this.$state)
    },
  },
})
