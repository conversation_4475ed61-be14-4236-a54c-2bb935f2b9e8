import type {App} from 'vue'
import type {RouteRecordRaw} from 'vue-router'
import {createRouter, createWebHashHistory} from 'vue-router'
import {setupPageGuard} from './permission'
import {ChatLayout} from '@/views/chat/layout'

const routes: RouteRecordRaw[] = [
	{
		path: '/',
		name: 'Root',
		component: ChatLayout,
		redirect: '/toolboxPage',
		children: [
			{
				path: '/chat/:uuid?',
				name: 'Chat',
				component: () => import('@/views/chat/index.vue'),
			},
			{
				path: '/teachPlanContentGen',
				name: 'teachPlanContentGen',
				component: () => import('@/views/teachPlanContentGen/index.vue'),
			},
			{
				path: '/tankChat',
				name: 'tankChat',
				component: () => import('@/views/tankChat/index.vue'),
			},
			{
				path: '/classroom',
				name: 'classroom',
				component: () => import('@/views/classroom/index.vue'),
			},
			{
				path: '/toolboxPage',
				name: 'toolboxPage',
				component: () => import('@/views/toolboxPage/index.vue'),
			},
			{
				path: '/applicationPage',
				name: 'applicationPage',
				component: () => import('@/views/applicationPage/index.vue'),
			},
			{
				path: '/coCreationPage',
				name: 'coCreationPage',
				component: () => import('@/views/coCreationPage/index.vue'),
			},
			{
				path: '/workShopPage',
				name: 'workShopPage',
				component: () => import('@/views/workShopPage/index.vue'),
			},
			{
				path: '/visit',
				name: 'visit',
				component: () => import('@/views/workShopPage/components/visit.vue'),
			},
			{
				path: '/creatIntelligencePage',
				name: 'creatIntelligencePage',
				component: () => import('@/views/workShopPage/components/easyAdd.vue'),
			},
			{
				path: '/knowledgeFactoryPage',
				name: 'knowledgeFactoryPage',
				component: () => import('@/views/knowledgeFactoryPage/index.vue'),
			},
			{
				path: '/promptManagement',
				name: 'promptManagement',
				component: () => import('@/views/promptManagement/index.vue'),
			},
			{
				path: '/promptEdit',
				name: 'promptEdit',
				component: () => import('@/views/promptManagement/components/PromptEdit.vue'),
			},
			{
				path: '/creatknowledgePage',
				name: 'creatknowledgePage',
				component: () => import('@/views/knowledgeFactoryPage/creatknowledgePage.vue'),
			},
			{
				path: '/fileUploadPage',
				name: 'fileUploadPage',
				component: () => import('@/views/knowledgeFactoryPage/fileUploadPage.vue'),
			},
			{
				path: '/fileManagementPage',
				name: 'fileManagementPage',
				component: () => import('@/views/knowledgeFactoryPage/fileManagementPage.vue'),
			},
			{
				path: '/iframePage',
				name: 'iframePage',
				component: () => import('@/views/iframePage/index.vue'),
			},
			{
				path: '/historyPage',
				name: 'historyPage',
				component: () => import('@/views/historyPage/index.vue'),
			},
			{
				path: '/courseware',
				name: 'courseware',
				component: () => import('@/views/courseware/index.vue'),
			},
			{
				path: '/coursewareChat',
				name: 'coursewareChat',
				component: () => import('@/views/courseware/chat.vue'),
			},
			{
				path: '/loadingPage',
				name: 'loadingPage',
				component: () => import('@/views/loadingPage/index.vue'),
			},
			{
				path: '/videoStudyHelper',
				name: 'videoStudyHelper',
				component: () => import('@/views/videoStudyHelper/index.vue'),
			},
			{
				path: '/videoStudyHelperChat',
				name: 'videoStudyHelperChat',
				component: () => import('@/views/videoStudyHelper/chat.vue'),
			},
		],
	},
	{
		path: '/previewPage',
		name: 'previewPage',
		component: () => import('@/views/previewPage/index.vue'),
	},

	{
		path: '/404',
		name: '404',
		component: () => import('@/views/exception/404/index.vue'),
	},

	{
		path: '/500',
		name: '500',
		component: () => import('@/views/exception/500/index.vue'),
	},
	{
		path: '/unauthorized',
		name: 'unauthorized',
		component: () => import('@/views/exception/unauthorized/index.vue'),
	},
	{
		path: '/:pathMatch(.*)*',
		name: 'notFound',
		redirect: '/404',
	},
	{
		path: '/agentOrchestration',
		name: 'agentOrchestration',
		component: () => import('@/views/agentOrchestration/index.vue'),
	},

]

export const router = createRouter({
	history: createWebHashHistory(import.meta.env.VITE_PUBLIC_PATH),
	routes,
	scrollBehavior: () => ({left: 0, top: 0}),
})

setupPageGuard(router)

export async function setupRouter(app: App) {
	app.use(router)
	await router.isReady()
}
