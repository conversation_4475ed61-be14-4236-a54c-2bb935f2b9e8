<template>
  <div class="llm-node-config">
    <!-- 模型选择 -->
    <n-form-item path="config.modelConfig.model" class="setrowbottom">
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 模型选择
        </div>
      </template>
      <n-select
        v-model:value="formData.config.modelConfig.model"
        :options="modelOptions"
        placeholder="请选择模型"
        filterable
        @update:value="handleModelChange"
      />
      <img
        @click="showModelParameterDialog"
        class="w-[20px] h-[20px] ml-[10px] cursor-pointer"
        src="@/assets/agentOrchestration/yitupeizhi.png"
      />
    </n-form-item>

    <!-- 提示词配置 -->
    <n-form-item
      label-placement="left"
      class="setHeight mb-[9px] mt-[21px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 提示词
        </div>
      </template>
      <div class="flex justify-end w-full">
        <div class="text-[#125EFF] cursor-pointer" @click="optimizePrompt">
          一键优化
        </div>
      </div>
    </n-form-item>

    <!-- 可编辑提示词输入框 -->
    <n-form-item path="config.tishici" class="seteditable">
      <div class="relative w-full">
        <div
          ref="editableDiv"
          contenteditable="true"
          class="editable-div"
          @keydown="handleKeydown"
          @input="handleInput"
        ></div>
        <button
          class="add-variable-btn"
          @click="showVariableSelector = true"
          type="button"
        >
          + 添加变量
        </button>
      </div>
    </n-form-item>

    <!-- 输出配置 -->
    <n-form-item
      label-placement="left"
      class="setHeight mt-[24px] mb-[10px]"
    >
      <template #label>
        <div class="rowstit"><span class="rowicon"></span>输出</div>
      </template>
    </n-form-item>

    <n-form-item label-placement="left">
      <div class="w-full flex text-[#C7C7C7]">
        <div class="w-[50%]">名称</div>
        <div>类型</div>
      </div>
    </n-form-item>

    <n-form-item
      path="config.wenbenshuchu"
      label-placement="left"
      class="outputrow"
    >
      <div class="w-full flex text-[#565756] items-center">
        <div class="w-[50%]">
          <div class="w-[234px] h-[38px]">
            <n-input
              v-model:value="formData.config.wenbenshuchu"
              type="text"
              placeholder="请输入输出名称"
            />
          </div>
        </div>
        <div>文本</div>
      </div>
    </n-form-item>

    <!-- 变量选择器弹窗 -->
    <n-modal v-model:show="showVariableSelector">
      <n-card
        style="width: 600px"
        title="选择变量"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <VariableSelector
          v-model="selectedVariableForInsert"
          :options="aggregationOptions"
          @select="handleVariableSelect"
          @confirm="insertVariableToPrompt"
          @cancel="showVariableSelector = false"
        />
      </n-card>
    </n-modal>

    <!-- 模型参数配置弹窗 -->
    <n-modal v-model:show="modelParameterShow">
      <n-card
        class="modelParametercard"
        style="width: 644px"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div class="flex items-center justify-between h-[70px] text-[17px] font-medium text-[#1F1F1F] pl-[26px] pr-[23px] border-b-[1px] border-[#E5E5E5]">
          <span>模型参数</span>
          <span @click="modelParameterShow = false" class="cursor-pointer">×</span>
        </div>
        
        <n-form ref="modelParameterformRef" :model="modelParameterformData">
          <!-- 模型风格 -->
          <n-form-item path="style">
            <template #label>
              <div class="h-[21px] text-[#1F1F1F] text-[15px] mt-[28px] mb-[7px] pl-[26px] pr-[23px] font-medium">
                模型风格
              </div>
            </template>
            <n-radio-group
              v-model:value="modelParameterformData.style"
              name="style"
              @update:value="updateModelParameter"
            >
              <div class="w-[592px] h-[38px] bg-[#F9FAFC] mx-auto rounded-lg pl-[18px] pr-[18px] flex items-center justify-between">
                <div v-for="(item, index) in modelParameterOptions" :key="index">
                  <n-radio :key="item.value" :value="item.value">
                    {{ item.label }}
                  </n-radio>
                </div>
              </div>
            </n-radio-group>
          </n-form-item>

          <!-- 模型温度 -->
          <n-form-item path="temperature" label-placement="left" class="mt-[26px]">
            <template #label>
              <div class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center">
                模型温度
              </div>
            </template>
            <div class="w-full flex justify-end pr-[23px]">
              <div class="w-[112px] h-[38px]">
                <n-input v-model:value="modelParameterformData.temperature" />
              </div>
            </div>
          </n-form-item>

          <!-- Top P -->
          <n-form-item path="topP" label-placement="left" class="mt-[24px]">
            <template #label>
              <div class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center">
                Top P
              </div>
            </template>
            <div class="w-full flex justify-end pr-[23px]">
              <div class="w-[112px] h-[38px]">
                <n-input v-model:value="modelParameterformData.topP" />
              </div>
            </div>
          </n-form-item>

          <!-- 最大输出长度 -->
          <n-form-item path="maxTokens" label-placement="left" class="mt-[24px]">
            <template #label>
              <div class="h-[34px] text-[#1F1F1F] text-[15px] pl-[26px] font-medium flex items-center">
                最大输出长度
              </div>
            </template>
            <div class="w-full flex justify-end pr-[23px]">
              <div class="w-[112px] h-[38px]">
                <n-input v-model:value="modelParameterformData.maxTokens" />
              </div>
            </div>
          </n-form-item>
        </n-form>

        <template #footer>
          <div class="flex w-full justify-end pl-[26px] pr-[23px] pb-[27px] mt-[47px]">
            <div class="btnparent w-[80px] h-[36px]">
              <n-button @click="modelParameterShow = false">取消</n-button>
            </div>
            <div class="btnparent w-[80px] h-[36px] ml-[16px]">
              <n-button @click="saveModelParameter" type="info" color="#125EFF">
                保存
              </n-button>
            </div>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { watch, nextTick, onMounted } from 'vue';
import {
  NFormItem,
  NSelect,
  NInput,
  NModal,
  NCard,
  NForm,
  NRadioGroup,
  NRadio,
  NButton
} from 'naive-ui';
import VariableSelector from '../VariableSelector.vue';
import { useNodeConfig } from './composables/useNodeConfig';
import { useVariableSelector } from './composables/useVariableSelector';
import { useModelConfig } from './composables/useModelConfig';
import type { NodeConfigProps, NodeConfigEvents } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const { aggregationOptions, updateAggregationOptions, orchestrationStore } = useNodeConfig(props);

const {
  showVariableSelector,
  selectedVariableForInsert,
  selectedVariableObj,
  editableDiv,
  handleVariableSelect,
  insertVariableToPrompt,
  handleKeydown,
  handleInput,
  renderEditableDiv,
} = useVariableSelector();

const {
  modelOptions,
  modelParameterOptions,
  modelParameterShow,
  modelParameterformData,
  modelParameterformRef,
  handleModelChange: baseHandleModelChange,
  changemodelParameterShow,
  changemodelParameterfun,
  updatemodelParameterfun,
  initializeModelConfig,
  optimizing,
  initModelData,
} = useModelConfig();

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'llm') {
      const config = newNode.data.config;

      // 初始化基本配置
      config.tishici = config.tishici || "";
      config.wenbenshuchu = config.wenbenshuchu || "文本输出";

      // 初始化模型配置
      initializeModelConfig(config);

      // 渲染提示词内容
      if (config.tishici) {
        nextTick(() => {
          renderEditableDiv(config.tishici);
        });
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听显示状态，更新聚合选项
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.node) {
      updateAggregationOptions();
    }
  },
  { immediate: true }
);

// 处理模型变更
const handleModelChange = (value: string) => {
  baseHandleModelChange(value, props.formData);
};

// 显示模型参数对话框
const showModelParameterDialog = () => {
  changemodelParameterShow(true, props.formData);
};

// 保存模型参数
const saveModelParameter = async () => {
  await changemodelParameterfun(props.formData);
};

// 更新模型参数
const updateModelParameter = (value: string) => {
  updatemodelParameterfun(value);
};

// 优化提示词
const optimizePrompt = async () => {
  try {
    const optimizedPrompt = await optimizing(props.formData);
    if (optimizedPrompt) {
      props.formData.config.tishici = optimizedPrompt;
      nextTick(() => {
        renderEditableDiv(optimizedPrompt);
      });
    }
  } catch (error) {
    console.error("优化失败:", error);
  }
};

// 组件挂载时初始化
onMounted(() => {
  initModelData();
  if (props.visible && props.node) {
    updateAggregationOptions();
  }
});
</script>

<style scoped lang="less">
.llm-node-config {
  .rowstit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
    width: 100%;

    .rowicon {
      width: 5px;
      height: 16px;
      background: #abc6ff;
      background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
      border-radius: 3px;
      margin-right: 9px;
    }
  }

  .setrowbottom {
    :deep(.n-form-item-label) {
      margin-bottom: 9px;
    }
  }

  .setHeight {
    :deep(.n-form-item-label) {
      height: 22px !important;
      min-height: 22px;
    }

    :deep(.n-form-item-blank) {
      height: 22px;
      min-height: 22px;
    }
  }

  .seteditable {
    grid-template-rows: 1fr 130px;

    :deep(.n-form-item-blank) {
      height: 130px;
    }
  }

  .outputrow {
    .n-input {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      :deep(.n-input__border) {
        display: none;
      }
    }

    .n-input__input-el {
      height: 100%;
      border: 0;
    }
  }

  .editable-div {
    min-height: 130px;
    width: 100%;
    background: #f5f5f6;
    border-radius: 8px;
    padding: 12px;
    outline: none;
    position: relative;
  }

  .add-variable-btn {
    position: absolute;
    right: 16px;
    bottom: 16px;
    z-index: 2;
    background: #125eff;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 4px 12px;
    cursor: pointer;
    font-size: 14px;
  }

  .modelParametercard {
    :deep(.n-card__content) {
      padding: 0px;
    }

    :deep(.n-card__footer) {
      padding: 0px;
    }
  }

  .btnparent {
    :deep(.n-button) {
      width: 100%;
    }
  }
}
</style>
