<script lang='ts' setup>
import { onMounted, ref } from "vue";
import { NButton, NInput, NSpin } from "naive-ui";
import Message from "@/views/tankChat/components/Message/index.vue";
import testTit from "@/assets/workShopPage/test-tit.png";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { t } from "@/locales";
import { useScroll } from "@/views/tankChat/hooks/useScroll";
import {
  agentById,
  answersStop,
  fetchChatAPIProcess,
  generateSummarizeProblem,
  getGenerateProblem,
} from "@/api/courseware";
import { useToolsStore } from "@/store";
import icon1 from "@/assets/toolboxPage/kj-logo.png";
import { SvgIcon } from "@/components/common";

defineOptions({
  name: "Chat",
});

const props = defineProps<Props>();

const emit = defineEmits(["textConverse"]);

const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll();

interface Props {
  submitFlag?: boolean;
  show?: boolean;
  dialogue: any;
  main?: any;
  questionState?: number;
  isVideoCompleted?: boolean;
}

const ToolsStore = useToolsStore();
const { isMobile } = useBasicLayout();
let controller: any = new AbortController();
const loading = ref(false);
// 状态2的总结问题生成loading状态
const summaryLoading = ref(false);
// 状态1的预设问题生成loading状态
const questionLoading = ref(false);
// 状态2图标开关状态
const xiuFlag = ref(false);

const prompt = ref();

const uuid: any = ref(null);

// 停止响应
const stopId = ref<any>({});

const dataSources: any = ref([]);
const addChat = (uuid: any, chat: any) => {
  // console.log(uuid, chat)
  // const index = dataSources.value.findIndex(item => item.uuid === uuid)
  // if (index !== -1) {
  //   dataSources.value[index] = chat
  // }
  // else {
  dataSources.value.push({
    uuid,
    ...chat,
  });
  // }
};
const updateChat = (uuid: any, index: any, chat: any) => {
  if (!uuid || uuid === 0) {
    if (dataSources.value.length) dataSources.value[0].data[index] = chat;
    return;
  }

  // const chatIndex = dataSources.value.findIndex(item => item.uuid === uuid)
  // // console.log(uuid, index, chatIndex)
  // if (chatIndex !== -1)
  dataSources.value[index] = chat;
};

const updateChatSome = (uuid: any, index: any, chat: any) => {
  dataSources.value[index] = {
    ...dataSources.value[index],
    ...chat,
  };
};

const onConversation = async (msg?: string) => {
  // 做对话前处理  后父组件调用 textConversation
  // emit("textConverse", {message: prompt.value});
  console.log(prompt.value, "prompt.value");

  await textConversation({
    question: msg ? msg : prompt.value,
    conversationId: props.dialogue.id,
    category: "1",
    agentId: "1950834085600997399",
    prompt: !xiuFlag.value
      ? "videoAssistantProblemEvaluationPrompt"
      : "videoAssistantPrompt",
    // 状态2时根据开关状态添加额外参数
    xiuStatus: props.questionState === 2 ? xiuFlag.value : undefined,
  });
};

const textConversation = async (obj: any, Id?: string) => {
  console.log(obj);
  uuid.value = obj?.conversationId;
  loading.value = true;

  const message = obj?.question;
  addChat(uuid, {
    dateTime: new Date().toLocaleString(),
    text: message,
    answerList: [],
    endstatus: 1,
    inversion: true,
    error: false,
    conversationOptions: null,
    requestOptions: { prompt: message, options: null },
    btnFlag: false, // 用户消息不显示按钮
    // 状态2且开关开启时显示xiu图标
    showXiuIcon: props.questionState === 2 && xiuFlag.value,
  });
  prompt.value = "";
  controller = new AbortController();
  addChat(uuid.value, {
    dateTime: new Date().toLocaleString(),
    text: t("chat.thinking"),
    loading: true,
    answerList: [],
    endstatus: 1,
    inversion: false,
    error: false,
    conversationOptions: null,
    btnFlag: false, // 思考中消息不显示按钮
  });
  await scrollToBottom();

  try {
    let lastText = "";
    const fetchChatAPIOnce = async () => {
      await fetchChatAPIProcess<Chat.ConversationResponse>({
        signal: controller.signal,
        ...obj,
        onDownloadProgress: ({ event }) => {
          const xhr = event.target;
          const { responseText } = xhr;
          // if (inputFlag.netFlag) {
          // 深度思考
          // 按行分割响应文本
          const lines = responseText
            .split("\n")
            .filter((line: string) => line.trim() !== "");

          // 重置文本,避免重复累加
          lastText = "";
          // 处理每一行数据
          for (const line of lines) {
            const trimmedLine = line.replace(/^data: /, "").trim();
            // info.setloading(false)
            try {
              // const data = JSON.parse(trimmedLine)
              const data = JSON.parse(trimmedLine?.substring(5));
              console.log(data);
              // 停止回答用
              stopId.value.id = data.id;
              // currectemitterId.value = data.emitterId

              // 直接使用当前响应文本,不进行累加
              const deltaContent = data.choices[0].message.content || "";

              lastText += deltaContent;

              updateChat(uuid.value, dataSources.value.length - 1, {
                dateTime: new Date().toLocaleString(),
                text: lastText, // 使用完整的lastText,不再和之前的文本拼接
                // answerList: data.answerList,
                inversion: false,
                error: false,
                loading: true,
                conversationOptions: {
                  conversationId: data.conversationContentId || "",
                  parentMessageId: data.id || "",
                },
              });

              // if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
              //   options.parentMessageId = data.id
              //   message = ''
              //   return fetchChatAPIOnce()
              // }

              scrollToBottomIfAtBottom();

              if (
                data.choices[0].finish_reason === "stop" ||
                data.choices[0].finish_reason === "STOP"
              ) {
                console.log(dataSources.value);
                // updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
                updateChatSome(uuid, dataSources.value.length - 1, {
                  loading: false,
                  answerList: data.answerList,
                  btnFlag: props.questionState == 1, // 状态1才显示按钮
                  conversationContentId: data.id,
                  conversationOptions: {
                    conversationId: data.conversationContentId || "",
                    parentMessageId: data.id || "",
                  },
                });
                loading.value = false;
              }
            } catch (error) {
              console.log(error);
            }
          }
        },
      });
    };

    await fetchChatAPIOnce();
  } catch (error: any) {
    console.log(error);
  } finally {
    await scrollToBottom();
    // loading.value = false
  }
};

function handleEnter(event: KeyboardEvent) {
  if (!isMobile.value) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (props.submitFlag && prompt.value) onConversation();
    }
  } else {
    if (event.key === "Enter" && event.ctrlKey) {
      event.preventDefault();
      if (props.submitFlag && prompt.value) onConversation();
    }
  }
}

// 跳转到最后一页 添加一个预制问题
const generateProblem = async (data: any) => {
  let res = await generateSummarizeProblem(data);
  console.log(res.data);
};

// 重新生成
const onRegenerate = async (flag: boolean, index: any) => {
  if (flag) {
    // 代表是预制问题的重新生成
    await textLength(index);
  } else {
    console.log(dataSources.value[index - 1].text);

    // console.log(obj);
    uuid.value = props.dialogue.id;
    loading.value = true;

    const message = dataSources.value[index - 1].text;
    controller = new AbortController();
    updateChat(uuid.value, index, {
      dateTime: new Date().toLocaleString(),
      text: t("chat.thinking"),
      loading: true,
      answerList: [],
      inversion: false,
      error: false,
      conversationOptions: null,
    });
    await scrollToBottom();

    try {
      let lastText = "";
      const fetchChatAPIOnce = async () => {
        await fetchChatAPIProcess<Chat.ConversationResponse>({
          signal: controller.signal,
          question: message,
          category: "0",
          conversationId: uuid.value,
          agentId: "1950834085600997378",
          onDownloadProgress: ({ event }) => {
            const xhr = event.target;
            const { responseText } = xhr;
            // if (inputFlag.netFlag) {
            // 深度思考
            // 按行分割响应文本
            const lines = responseText
              .split("\n")
              .filter((line) => line.trim() !== "");

            // 重置文本,避免重复累加
            lastText = "";
            // 处理每一行数据
            for (const line of lines) {
              const trimmedLine = line.replace(/^data: /, "").trim();
              // info.setloading(false)
              try {
                // const data = JSON.parse(trimmedLine)
                const data = JSON.parse(trimmedLine?.substring(5));
                console.log(data);
                // 停止回答用
                stopId.value.id = data.id;
                stopId.value.index = index;
                // currectemitterId.value = data.emitterId

                // 直接使用当前响应文本,不进行累加
                const deltaContent = data.choices[0].message.content || "";

                lastText += deltaContent;

                updateChat(uuid.value, index, {
                  dateTime: new Date().toLocaleString(),
                  text: lastText, // 使用完整的lastText,不再和之前的文本拼接
                  // answerList: data.answerList,
                  inversion: false,
                  error: false,
                  loading: true,
                  conversationContentId: data.id,
                  conversationOptions: {
                    conversationId: data.conversationContentId || "",
                    parentMessageId: data.id || "",
                  },
                });

                // if (openLongReply && data.detail.choices[0].finish_reason === 'length') {
                //   options.parentMessageId = data.id
                //   message = ''
                //   return fetchChatAPIOnce()
                // }

                scrollToBottomIfAtBottom();

                if (
                  data.choices[0].finish_reason === "stop" ||
                  data.choices[0].finish_reason === "STOP"
                ) {
                  console.log(dataSources.value);
                  // updateChatSome(uuid, dataSources.value.length - 1, { loading: false })
                  updateChatSome(uuid, index, {
                    loading: false,
                    answerList: data.answerList,
                    btnFlag: true,
                  });
                  loading.value = false;
                }
              } catch (error) {
                console.log(error);
              }
            }
          },
        });
      };

      await fetchChatAPIOnce();
    } catch (error: any) {
      console.log(error);
    } finally {
      await scrollToBottom();
      // loading.value = false
    }
  }
};

// 用于触发获取预制问题 有记录时不触发事件
const textLength = async (index?: any) => {
  if (
    !dataSources.value?.length ||
    (typeof index === "number" && !isNaN(index))
  ) {
    // 如果是重新生成（有index），显示loading
    if (typeof index === "number" && !isNaN(index)) {
      questionLoading.value = true;
    }

    try {
      let res = await agentById("1950834085600997399");
      console.log(props.main);
      let obj = {
        summary: props.main.summary,
        id: props.main.id,
        // textbookTypeText: "2",
        agent: {
          modelId: res.data.modelId,
          modelTemp: res.data.modelTemp,
          maxLength: res.data.maxLength,
        },
      };
      let Problems = await getGenerateProblem(obj);

      if (typeof index === "number" && !isNaN(index)) {
        updateChatSome(uuid.value, index, {
          dateTime: new Date().toLocaleString(),
          text: "", // 使用完整的lastText,不再和之前的文本拼接
          // answerList: data.answerList,
          inversion: false,
          error: false,
          loading: false,
          conversationOptions: null,
          questionArr: Problems.data,
          btnFlag: true,
        });
      } else {
        addChat(uuid.value, {
          dateTime: new Date().toLocaleString(),
          text: "",
          loading: false,
          answerList: [],
          inversion: false,
          error: false,
          conversationOptions: null,
          questionArr: Problems.data,
          btnFlag: true,
        });
      }
    } finally {
      // 只在重新生成时关闭loading（初始化时由clearAndInitState1管理）
      if (typeof index === "number" && !isNaN(index)) {
        questionLoading.value = false;
      }
    }
  }
};

onMounted(() => {
  ToolsStore.ToolInfo.icon = icon1;
});

// 预设问题点击处理
const handleAnswer = (msg: string) => {
  // console.log(msg)
  if (!loading.value) onConversation(msg);
};

// 切换状态2图标开关
const toggleXiuFlag = () => {
  xiuFlag.value = !xiuFlag.value;
};

// 停止响应
const handleStop = async () => {
  if (loading.value) {
    controller.abort();
    loading.value = false;
    await answersStop({ id: stopId.value.id });
  }
};

// 清空并初始化状态1（预设问题模式）
const clearAndInitState1 = async () => {
  dataSources.value = [];
  questionLoading.value = true;
  try {
    await textLength();
  } finally {
    questionLoading.value = false;
  }
};

// 初始化状态2（视频完成总结问题模式）
const initState2 = async () => {
  dataSources.value = [];
  summaryLoading.value = true;
  try {
    await generateSummaryProblem();
  } finally {
    summaryLoading.value = false;
  }
};

// 生成总结问题（状态2）
const generateSummaryProblem = async () => {
  let res = await agentById("1950834085600997399");
  let obj = {
    summary: props.main.summary,
    textbookType: "2",
    agent: {
      modelId: res.data.modelId,
      modelTemp: res.data.modelTemp,
      maxLength: res.data.maxLength,
    },
  };

  let summaryRes = await generateSummarizeProblem(obj);
  console.log("Summary problem:", summaryRes.data);

  // 添加AI的第一条消息
  addChat(uuid.value, {
    dateTime: new Date().toLocaleString(),
    text: summaryRes.data,
    loading: false,
    answerList: [],
    inversion: false,
    error: false,
    conversationOptions: null,
    btnFlag: false,
  });
};

defineExpose({
  textConversation,
  generateProblem,
  textLength,
  clearAndInitState1,
  initState2,
});
</script>

<template>
  <NSpin :show="show || summaryLoading || questionLoading">
    <template #description>
      <span v-if="summaryLoading">正在生成问题，请稍候...</span>
      <span v-else-if="questionLoading">正在生成查询条件，请稍后...</span>
      <span v-else>正在生成PDF，请稍候...</span>
    </template>
    <div class="px-6">
      <div
        id="scrollRef"
        ref="scrollRef"
        :class="[
          'overflow-y-scroll',
          'contentList',
          questionState === 2 ? 'state2' : '',
        ]"
      >
        <Message
          v-for="(item, index) of dataSources"
          :key="index"
          :answer-list="item.answerList"
          :category="item.category"
          :conversation-content-id="item.conversationContentId"
          :date-time="item.dateTime"
          :endstatus="item.endstatus"
          :error="item.error"
          :inversion="item.inversion"
          :loading="item.loading"
          :questionArr="item.questionArr"
          :problem="!xiuFlag && questionState === 2"
          :text="item.text"
          :btnFlag="item.btnFlag"
          :questionState="questionState"
          @handleAnswer="handleAnswer"
          @regenerate="(flag) => onRegenerate(flag, index)"
        />
      </div>

      <div
        :class="questionState === 2 ? 'h-[140px]' : 'h-[64px]'"
        class="rounded-[12px] border-[1px] border-[#E5E5E4] flex align-center submit relative"
      >
        <!-- 状态2的开关图标 -->
        <div
          v-if="questionState === 2 && !xiuFlag"
          class="absolute left-3 bottom-3 z-10 cursor-pointer hover:opacity-80 transition-opacity border-[1px] border-[#E1E1E1] rounded-full p-2"
          @click="toggleXiuFlag"
        >
          <img src="@/assets/toolboxPage/xiu.png" alt="开关" class="w-5 h-5" />
        </div>
        <div
          v-if="questionState === 2 && xiuFlag"
          class="absolute left-3 bottom-3 z-10 cursor-pointer hover:opacity-80 transition-opacity border-[1px] border-[#E1E1E1] rounded-full p-2"
          @click="toggleXiuFlag"
        >
          <img src="@/assets/toolboxPage/xiu2.png" alt="开关" class="w-5 h-5" />
        </div>
        <div
          class="absolute right-3 bottom-3 z-10 cursor-pointer hover:opacity-80 transition-opacity"
        >
          <NButton
            v-show="!loading"
            :color="
              questionState === 2
                ? prompt && prompt.trim()
                  ? '#EBF3FF'
                  : '#D2D4D8'
                : !(submitFlag && prompt && !loading)
                ? '#D2D4D8'
                : '#EBF3FF'
            "
            :disabled="
              questionState === 2
                ? !(prompt && prompt.trim() && !loading)
                : !(submitFlag && prompt && !loading)
            "
            class="!p-[8px] !rounded-[50%] !w-[38px] !h-[38px]"
            @click="() => onConversation()"
          >
            <img
              v-if="
                (questionState === 2 && !(prompt && prompt.trim())) ||
                (questionState !== 2 && !(submitFlag && prompt && !loading))
              "
              alt=""
              class="w-[20px] h-[20px]"
              src="@/assets/workShopPage/test-btn.png"
            />
            <img
              v-else
              alt=""
              class="w-[20px] h-[20px]"
              src="@/assets/toolboxPage/btna.png"
            />
          </NButton>
          <img
            v-show="loading"
            alt=""
            class="w-[38px] h-[38px] cursor-pointer"
            src="@/assets/toolboxPage/stop.png"
            @click="handleStop"
          />
        </div>
        <NInput
          v-model:value="prompt"
          :bordered="false"
          :placeholder="
            questionState === 2 ? '请输入你的回答...' : '向视频学习助手提问'
          "
          :type="questionState === 2 ? 'textarea' : 'text'"
          :autosize="questionState === 2 ? { minRows: 4, maxRows: 5 } : false"
          :class="questionState === 2 ? '!pb-14 h-200px' : ''"
          size="large"
          @keypress="handleEnter"
          style="border-radius: 12px"
        >
        </NInput>
      </div>

      <!--			<div-->
      <!--				:class="'w-[100%] h-[176px] max-w-[1150px] gradient-border'"-->
      <!--				class="relative mx-auto bg-[#fff]"-->
      <!--			>-->
      <!--				<div class="gradient-border-cen  h-[172px] max-w-[1150px]">-->
      <!--					<NAutoComplete-->
      <!--						v-model:value="prompt"-->
      <!--						class=""-->
      <!--					>-->
      <!--						<template #default="{ handleInput, handleBlur, handleFocus }">-->
      <!--							<NInput-->
      <!--								ref="inputRef"-->
      <!--								v-model:value="prompt"-->
      <!--								:autosize="{ minRows: 1, maxRows: isMobile ? 4 : 8 }"-->
      <!--								:bordered="false"-->
      <!--								:class="isMobile ? 'rounded-2xl inputRef' : 'w-full h-[110px] !rounded-2xl !text-base !indent-1'"-->
      <!--								:placeholder="'向课件学习助手提问'"-->
      <!--								type="textarea"-->
      <!--								@blur="handleBlur"-->
      <!--								@focus="handleFocus"-->
      <!--								@input="handleInput"-->
      <!--								@keypress="handleEnter"-->
      <!--							/>-->
      <!--						</template>-->
      <!--					</NAutoComplete>-->
      <!--					<div v-show="lodingFlag" class="flex items-center absolute left-4 bottom-[19px]">-->
      <!--						<NSpin size="small"/>-->
      <!--						<p class="ml-4">-->
      <!--							模型思考中…-->
      <!--						</p>-->
      <!--					</div>-->
      <!--					<div v-show="!lodingFlag" class="flex items-center absolute left-0 bottom-[19px]">-->
      <!--						&lt;!&ndash; 回答问题 &ndash;&gt;-->
      <!--						<div v-show="inputFlag.category === '0' " class="cabtn" @click="categoryTap('1')">-->
      <!--							<img alt="" class="w-5 h-5" src="@/assets/chat/cabtn.png">-->
      <!--						</div>-->
      <!--						<div v-show="inputFlag.category === '1' " class="cabtn" @click="categoryTap('0')">-->
      <!--							<img alt="" class="w-5 h-5" src="@/assets/chat/cabtna.png">-->
      <!--						</div>-->
      <!--						&lt;!&ndash; 建议指导 &ndash;&gt;-->
      <!--						<div-->
      <!--							v-show="!inputFlag.netFlag"-->
      <!--							:class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"-->
      <!--							class="flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer"-->
      <!--							@click="netFlagTap"-->
      <!--						>-->
      <!--							<img-->
      <!--								:class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']" alt=""-->
      <!--								src="@/assets/chat/jyzd.png"-->
      <!--							>-->
      <!--							<p :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']" class=" font-medium text-[#606266]">-->
      <!--								建议指导-->
      <!--							</p>-->
      <!--						</div>-->
      <!--						<div-->
      <!--							v-show="inputFlag.netFlag"-->
      <!--							:class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"-->
      <!--							class="flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer bg-[#125eff0f]"-->
      <!--							@click="netFlagTap"-->
      <!--						>-->
      <!--							<img-->
      <!--								:class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']" alt=""-->
      <!--								src="@/assets/chat/jyzda.png"-->
      <!--							>-->
      <!--							<p :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']" class=" font-medium text-[#125EFF]">-->
      <!--								建议指导-->
      <!--							</p>-->
      <!--						</div>-->
      <!--						&lt;!&ndash; 模型提问 &ndash;&gt;-->
      <!--						<div-->
      <!--							v-show="!inputFlag.mods == '1'"-->
      <!--							:class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"-->
      <!--							class="ml-[18px] flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer"-->
      <!--							@click="modsTap"-->
      <!--						>-->
      <!--							<img-->
      <!--								:class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']" alt=""-->
      <!--								src="@/assets/chat/mxtw.png"-->
      <!--							>-->
      <!--							<p :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']" class=" font-medium text-[#606266]">-->
      <!--								模型提问-->
      <!--							</p>-->
      <!--						</div>-->
      <!--						<div-->
      <!--							v-show="inputFlag.mods && activity?.participationMethod == '1'"-->
      <!--							:class="[isMobile ? 'w-[90px] h-[28px]' : 'w-[125px] h-[36px]']"-->
      <!--							class="ml-[18px] flex justify-center items-center rounded-2xl border-[1px] border-[#E1E1E1] cursor-pointer bg-[#125eff0f]"-->
      <!--							@click="modsTap"-->
      <!--						>-->
      <!--							<img-->
      <!--								:class="[isMobile ? 'w-[14px] h-[14px] mr-[3px]' : 'w-[20px] h-[20px] mr-1']" alt=""-->
      <!--								src="@/assets/chat/mxtwa.png"-->
      <!--							>-->
      <!--							<p :class="[isMobile ? 'text-[12px] mt-[1px]' : 'text-base']" class=" font-medium text-[#125EFF]">-->
      <!--								模型提问-->
      <!--							</p>-->
      <!--						</div>-->
      <!--					</div>-->
      <!--					&lt;!&ndash; 发送 &ndash;&gt;-->
      <!--					<NButton-->
      <!--						v-show="!loading" :circle="true" :disabled="!(submitFlag && prompt && !loading)"-->
      <!--						class="!absolute right-[23px] bottom-5 !w-[42px] !h-[42px] " color="#EBF3FF"-->
      <!--						type="primary" @click="handleSubmit"-->
      <!--					>-->
      <!--						<template #icon>-->
      <!--                  <span class="dark:text-black">-->
      <!--                    <img alt="" class="w-[20px]" src="@/assets/chat/req.png">-->
      <!--                  </span>-->
      <!--						</template>-->
      <!--					</NButton>-->
      <!--					&lt;!&ndash; 停止响应 &ndash;&gt;-->
      <!--					<img v-show="loading" alt=""-->
      <!--							 class="w-[42px] h-[42px] !absolute right-[23px] bottom-5 cursor-pointer" src="@/assets/home/<USER>"-->
      <!--							 @click="handleStop">-->

      <!--					<NButton-->
      <!--						v-show="prompt !== ''" :disabled="!(submitFlag && prompt && !loading)"-->
      <!--						class="!absolute right-[23px] bottom-5 !w-[38px] !h-[38px] !rounded-lg" color="#CA0D00"-->
      <!--						type="primary"-->
      <!--						@click="handleSubmit"-->
      <!--					>-->
      <!--						<template #icon>-->
      <!--                  <span class="dark:text-black">-->
      <!--                    <SvgIcon icon="ri:send-plane-fill"/>-->
      <!--                  </span>-->
      <!--						</template>-->
      <!--					</NButton>-->
      <!--				</div>-->
      <!--			</div>-->
    </div>
  </NSpin>
</template>

<style lang='less' scoped>
.submit {
  :deep(.n-input .n-input__input-el) {
    height: 64px;
  }

  &.state2 {
    :deep(.n-input .n-input__input-el) {
      height: auto;
      min-height: 80px;
    }
  }
}

.contentList {
  height: calc(87vh - 114px);

  &.state2 {
    height: calc(87vh - 170px); /* 状态2稍微调整高度 */
  }
}
/deep/ .n-input .n-input--resizable .n-input--stateful {
  border-radius: 12px;
}
</style>
