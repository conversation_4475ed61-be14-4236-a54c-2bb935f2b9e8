<template>
  <n-modal v-model:show="visible">
    <n-card 
      style="width: 800px; max-height: 70vh;" 
      title="备份管理" 
      :bordered="false" 
      size="huge"
      closable
      @close="handleClose"
    >
      <div class="backup-manager">
        <!-- 操作栏 -->
        <div class="action-bar mb-4 flex items-center justify-between">
          <div class="backup-info">
            <n-statistic label="备份总数" :value="backups.length" />
          </div>
          <div class="actions space-x-2">
            <n-button type="primary" @click="createBackup">
              💾 创建备份
            </n-button>
            <n-button @click="cleanupBackups">
              🗑️ 清理备份
            </n-button>
          </div>
        </div>

        <!-- 备份列表 -->
        <div class="backup-list">
          <div v-if="backups.length === 0" class="empty-state text-center py-8">
            <div class="text-4xl text-gray-300 mb-4">📦</div>
            <p class="text-gray-500">暂无备份</p>
            <p class="text-sm text-gray-400 mt-2">创建备份以保护您的流程数据</p>
          </div>
          
          <div v-else class="backup-items space-y-3">
            <div 
              v-for="backup in sortedBackups" 
              :key="backup.id"
              class="backup-item p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
            >
              <div class="flex items-center justify-between">
                <div class="backup-info flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="backup-name text-lg font-medium text-gray-800 mr-3">
                      {{ backup.name }}
                    </h4>
                    <n-tag 
                      :type="getBackupType(backup)" 
                      size="small"
                    >
                      {{ getBackupTypeLabel(backup) }}
                    </n-tag>
                  </div>
                  
                  <p class="backup-description text-sm text-gray-600 mb-2" v-if="backup.description">
                    {{ backup.description }}
                  </p>
                  
                  <div class="backup-meta flex items-center space-x-4 text-xs text-gray-500">
                    <span>流程数: {{ backup.flowCount }}</span>
                    <span>大小: {{ formatFileSize(backup.size) }}</span>
                    <span>创建时间: {{ formatDate(backup.createdAt) }}</span>
                  </div>
                </div>
                
                <div class="backup-actions flex items-center space-x-2">
                  <n-button 
                    size="small" 
                    type="primary" 
                    @click="restoreBackup(backup)"
                  >
                    🔄 恢复
                  </n-button>
                  
                  <n-button 
                    size="small" 
                    @click="downloadBackup(backup)"
                  >
                    📥 下载
                  </n-button>
                  
                  <n-button 
                    size="small" 
                    @click="renameBackup(backup)"
                  >
                    ✏️ 重命名
                  </n-button>
                  
                  <n-button 
                    size="small" 
                    type="error" 
                    @click="deleteBackup(backup)"
                  >
                    🗑️ 删除
                  </n-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <div class="text-sm text-gray-500">
            备份存储在浏览器本地存储中
          </div>
          <n-button @click="handleClose">关闭</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>

  <!-- 重命名弹窗 -->
  <n-modal v-model:show="showRenameModal">
    <n-card style="width: 400px" title="重命名备份" :bordered="false" size="huge">
      <n-form ref="renameFormRef" :model="renameForm" :rules="renameRules">
        <n-form-item label="备份名称" path="name">
          <n-input v-model:value="renameForm.name" placeholder="请输入新的备份名称" />
        </n-form-item>
        <n-form-item label="备份描述" path="description">
          <n-input 
            v-model:value="renameForm.description" 
            type="textarea" 
            placeholder="请输入备份描述（可选）"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <n-button @click="showRenameModal = false">取消</n-button>
          <n-button type="primary" @click="handleConfirmRename">确定</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  NModal, 
  NCard, 
  NButton, 
  NTag,
  NStatistic,
  NForm,
  NFormItem,
  NInput,
  useDialog,
  useMessage 
} from 'naive-ui'
import { useOrchestrationStore } from '@/store'

interface FlowBackup {
  id: string
  name: string
  description?: string
  data: any
  flowCount: number
  size: number
  createdAt: string
  type: 'manual' | 'auto'
}

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
}>()

const orchestrationStore = useOrchestrationStore()
const dialog = useDialog()
const message = useMessage()

const backups = ref<FlowBackup[]>([])
const showRenameModal = ref(false)
const renameFormRef = ref()
const renameForm = ref({
  name: '',
  description: ''
})
const currentRenameBackup = ref<FlowBackup | null>(null)

const renameRules = {
  name: [
    { required: true, message: '请输入备份名称', trigger: 'blur' }
  ]
}

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 计算属性
const sortedBackups = computed(() => {
  return [...backups.value].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )
})

// 方法
const handleClose = () => {
  visible.value = false
}

const loadBackups = () => {
  try {
    const stored = localStorage.getItem('orchestration-backups')
    if (stored) {
      backups.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('加载备份失败:', error)
    backups.value = []
  }
}

const saveBackups = () => {
  try {
    localStorage.setItem('orchestration-backups', JSON.stringify(backups.value))
  } catch (error) {
    console.error('保存备份失败:', error)
    message.error('保存备份失败')
  }
}

const createBackup = () => {
  const flows = orchestrationStore.exportAllFlows()
  if (flows.length === 0) {
    message.warning('没有可备份的流程')
    return
  }

  const backupData = {
    version: '1.0',
    backupTime: new Date().toISOString(),
    flows: flows
  }

  const backup: FlowBackup = {
    id: `backup-${Date.now()}`,
    name: `备份_${new Date().toLocaleString('zh-CN')}`,
    description: `包含 ${flows.length} 个流程的自动备份`,
    data: backupData,
    flowCount: flows.length,
    size: JSON.stringify(backupData).length,
    createdAt: new Date().toISOString(),
    type: 'manual'
  }

  backups.value.push(backup)
  saveBackups()
  message.success('备份创建成功')
}

const restoreBackup = (backup: FlowBackup) => {
  dialog.warning({
    title: '恢复确认',
    content: `确定要从备份 "${backup.name}" 恢复吗？这将替换当前的所有流程数据。`,
    positiveText: '确定恢复',
    negativeText: '取消',
    onPositiveClick: () => {
      try {
        if (backup.data.flows && Array.isArray(backup.data.flows)) {
          // 清空当前流程
          orchestrationStore.flows.length = 0
          orchestrationStore.currentFlow = null
          
          // 导入备份的流程
          orchestrationStore.importFlows(backup.data.flows)
          message.success(`成功恢复 ${backup.flowCount} 个流程`)
        } else {
          message.error('备份数据格式错误')
        }
      } catch (error) {
        message.error('恢复备份失败')
      }
    }
  })
}

const downloadBackup = (backup: FlowBackup) => {
  const blob = new Blob([JSON.stringify(backup.data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${backup.name}.json`
  a.click()
  URL.revokeObjectURL(url)
  message.success('备份下载成功')
}

const renameBackup = (backup: FlowBackup) => {
  currentRenameBackup.value = backup
  renameForm.value = {
    name: backup.name,
    description: backup.description || ''
  }
  showRenameModal.value = true
}

const handleConfirmRename = async () => {
  try {
    await renameFormRef.value?.validate()
    
    if (currentRenameBackup.value) {
      const index = backups.value.findIndex(b => b.id === currentRenameBackup.value!.id)
      if (index !== -1) {
        backups.value[index] = {
          ...backups.value[index],
          name: renameForm.value.name,
          description: renameForm.value.description
        }
        saveBackups()
        message.success('备份重命名成功')
      }
    }
    
    showRenameModal.value = false
    currentRenameBackup.value = null
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const deleteBackup = (backup: FlowBackup) => {
  dialog.warning({
    title: '删除确认',
    content: `确定要删除备份 "${backup.name}" 吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = backups.value.findIndex(b => b.id === backup.id)
      if (index !== -1) {
        backups.value.splice(index, 1)
        saveBackups()
        message.success('备份删除成功')
      }
    }
  })
}

const cleanupBackups = () => {
  if (backups.value.length === 0) {
    message.info('没有需要清理的备份')
    return
  }

  dialog.warning({
    title: '清理备份',
    content: '确定要清理所有备份吗？此操作不可撤销。',
    positiveText: '确定清理',
    negativeText: '取消',
    onPositiveClick: () => {
      backups.value = []
      saveBackups()
      message.success('备份清理完成')
    }
  })
}

const getBackupType = (backup: FlowBackup) => {
  return backup.type === 'auto' ? 'info' : 'success'
}

const getBackupTypeLabel = (backup: FlowBackup) => {
  return backup.type === 'auto' ? '自动备份' : '手动备份'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  loadBackups()
})
</script>

<style scoped lang="less">
.backup-manager {
  .action-bar {
    .backup-info {
      :deep(.n-statistic) {
        .n-statistic-value {
          font-size: 24px;
          font-weight: 600;
          color: #125EFF;
        }
      }
    }
  }
  
  .backup-list {
    max-height: 400px;
    overflow-y: auto;
    
    .backup-item {
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
    
    .backup-name {
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .backup-description {
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .empty-state {
    padding: 40px 20px;
  }
}

:deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.n-button) {
  border-radius: 6px;
}
</style>
