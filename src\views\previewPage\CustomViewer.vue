<template>
  <div class="custom-viewer">
    <template v-if="isDoc(props.suffix)">
      <vue-office-docx :src="props.url" />
    </template>
    <template v-else-if="isPdf(props.suffix)">
      <!-- <vue-office-pdf :src="props.url" /> -->
      <vue-pdf-embed
        ref="pdfRef"
        :source="props.url"
      />
    </template>
    <template v-else-if="isExcel(props.suffix)">
      <vue-office-excel
        :src="props.url"
        :options="options"
        style="height: 100vh;"
      />
    </template>
    <template v-else>
      <img
        :src="props.url"
        alt=""
      >
    </template>
  </div>
</template>

<script setup>
import { ref, defineProps } from "vue";
// import { isDoc, isPdf, isExcel } from "/@/utils/file/download";
import VueOfficeDocx from "@vue-office/docx";
import VueOfficePdf from "@vue-office/pdf";
import VueOfficeExcel from "@vue-office/excel";
import VuePdfEmbed from 'vue-pdf-embed';
import "@vue-office/docx/lib/index.css";
import "@vue-office/excel/lib/index.css";
import * as pdfjsLib from 'pdfjs-dist'
window.pdfjsLib = pdfjsLib
// pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc;

  function isDoc(suffix) {
  const docSuffixes = ['doc', 'docx'];
  return docSuffixes.includes(suffix.toLowerCase());
}
 function isPdf(suffix) {
  return suffix.toLowerCase() === 'pdf';
}
 function isExcel(suffix) {
  const excelSuffixes = ['xls', 'xlsx'];
  return excelSuffixes.includes(suffix.toLowerCase());
}
const props = defineProps({
  url: {
    type: String,
    default: "",
  },
  suffix: {
    type: String,
    default: "",
  },
  options: {
    type: Object,
    default: () => ({}),
  },
});
window.pdfjsLib.GlobalWorkerOptions.workerSrc
  = 'https://cimg.930703.xyz/file/1744109010421_pdf.worker.min.mjs'
const options = ref({
  minColLength: 0, // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
  minRowLength: 0, // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
  widthOffset: 10, //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
  heightOffset: 10, //在默认渲染的列表高度上再加 Npx高
  beforeTransformData: (workbookData) => {
    return workbookData;
  }, //底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。
  transformData: (workbookData) => {
    return workbookData;
  }, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容
  ...props.options
});
</script>

<style>
.vue-office-pdf-wrapper {
  background: #fff !important;
}
.docx-wrapper {
  background: linear-gradient(153deg, #F6FAFF 0%, #FBFDFF 100%) !important;
}

.custom-size {
  width: 87px;
  height: 87px;
  position: absolute;
  top: 0px;
  right:0px;
  /* background: url('/@/assets/images/editor/usingBig.png') no-repeat top center; */
  background-size: 100% 100%;
}
</style>
