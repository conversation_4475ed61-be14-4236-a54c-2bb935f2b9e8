import type { AxiosProgressEvent, GenericAbortSignal } from 'axios'
import { get, post } from '@/utils/request'
import request from '@/utils/request/axios'
export function getAudio<T>(
  params: {
    msg?: string
    conversationId?: string
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void },
) {
  const data: Record<string, any> = {
    msg: params.msg,
    conversationId: params.conversationId,
  }
  return post<T>({
    url: '/sse_api/text_to_speach',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
    onDownloadProgress: params.onDownloadProgress,
  })
}

export function fetchChatAPI<T = any>(
  prompt: string,
  options?: { conversationId?: string; parentMessageId?: string },
  signal?: GenericAbortSignal,
) {
  return post<T>({
    url: '/chat',
    data: { prompt, options },
    signal,
  })
}

export function fetchChatConfig<T = any>() {
  return post<T>({
    url: '/config',
  })
}

export function fetchChatAPIProcess<T = any>(
  params: {
    signal?: GenericAbortSignal
    question: any
    conversationId: any
    category: string
    agentId: any
    modelId: string
    modelTemp: any
    maxLength: any
    promptTemplate: any
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  // const settingStore = useSettingStore()
  // const authStore = useAuthStore()
  // console.log(params)
  const data: Record<string, any> = {
    question: params.question,
    conversationId: params.conversationId,
    category: params.category,
    agentId: params.agentId,
    modelId: params.modelId,
    modelTemp: params.modelTemp,
    maxLength: params.maxLength,
    promptTemplate: params.promptTemplate,

  }

  return post<T>({
    url: '/emind/conversationContent/text_answers',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function fetchSession<T>() {
  return post<T>({
    url: '/session',
  })
}

export function fetchVerify<T>(token: string) {
  return post<T>({
    url: '/verify',
    data: { token },
  })
}

/**********************/
export function textToVoice<T = any>(
  params: {
    prompt: string
    options?: { conversationId?: string; parentMessageId?: string }
    signal?: GenericAbortSignal
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  // const settingStore = useSettingStore()
  // const authStore = useAuthStore()

  const data: Record<string, any> = {
    msg: params.prompt,
    conversationId: params.conversationId,

  }

  return post<T>({
    url: '/sse_api/session_initiation',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
  })
}

export function voiceToText<T = any>(
  params: {
    data: any
    options?: { conversationId?: string; parentMessageId?: string }
    signal?: GenericAbortSignal
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  return post<T>({
    url: '/sse_api/speech_recognition',
    data: params.data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
  })
}

export function recognition_stop<T>(data: any) {
  return get<T>({
    url: '/sse_api/recognition_stop',
    data,
  })
}

export function setup<T>(data: any) {
  return get<T>({
    url: '/intelligent/setup',
    data,
  })
}

export function fetchWeaknessAnalysisProcess<T = any>(
  params: {
    examQuestion?: any
    conversationContentId: string
    examAnswer?: any
    question?: any
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  // console.log(params)
  const data: Record<string, any> = {
    conversationContentId: params.conversationContentId,
    examQuestion: params.examQuestion,
    examAnswer: params.examAnswer,
    question: params.question,
  }
  return post<T>({
    url: '/eaide/summary/exam_guides/analyze/exam_test_weak',
    data,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function getsessionApi<T = any>(data: any) {
  return post<T>({
    url: '/eaide/intelligent_agent/create_session',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function getqaIdApi<T = any>(data: any) {
  return get<T>({
    url: '/eaide/intelligent_agent/qa_id',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
export function getfileListApi<T = any>(data: any) {
  return post<T>({
    url: '/eaide/intelligent_agent/assistant_file_list',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function sessionHistory<T = any>(data: any) {
  return post<T>({
    url: '/emind/conversation',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// export function exprot<T = any>(data: any) {
//   return post<T>({
//     url: '/emind/agent/questions_export',
//     data,
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   })
// }

export function exprot<T = any>(url: string, data: any) {
  return request({
    method: 'POST',
    url: `/emind/agent/${url}`,
    params: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
