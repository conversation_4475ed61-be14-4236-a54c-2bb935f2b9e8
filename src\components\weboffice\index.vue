<template>
  <div class="custom-mount" />
</template>

<script setup lang="ts">
import { onMounted, defineProps } from "vue";
// @ts-ignore
import WebOfficeSDK from "./web-office-sdk-v1.1.20.es.js";

// 定义props接口
interface WebOfficeProps {
  url: string; // 文档预览编辑地址
}

// 定义组件props
const props = defineProps<WebOfficeProps>();

// 组件挂载后初始化WebOffice
onMounted(() => {
  const jssdk = WebOfficeSDK.config({
    url: props.url, // 使用props传入的url
    mount: document.querySelector(".custom-mount")
  });
});
</script>

<style scoped>
.custom-mount {
  width: 100%;
  height: 100%;
  min-height: 500px;
}
</style>
