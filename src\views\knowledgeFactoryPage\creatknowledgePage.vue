<template>
  <NSpin :show="loading">
    <div class="app">
      <header class="headers">
        <div class="left">
          <div class="gohome" @click="jumpPage()">
            <img src="@/assets/workShopPage/leftarrow.png" />
          </div>
          知识库工厂 / <span>新建知识库</span>
        </div>
        <div class="headbtnrow">
          <NButton @click="jumpPage()">取消</NButton>
          <NButton type="primary" @click="send()">保存</NButton>
        </div>
      </header>
      <div class="setconbg">
        <div>
          <div class="rowtit"><span class="red">*</span>知识库名称</div>
          <n-input
            class="inputset-38"
            v-model:value="storeName"
            type="text"
            placeholder="请输入..."
          />
        </div>
        <div style="margin-top: 24px">
          <div class="rowtit"><span class="red">*</span>知识库描述</div>
          <n-input
            type="textarea"
            class="inputset-120"
            v-model:value="storeDesc"
            placeholder="请输入..."
          />
        </div>
      </div>
    </div>
  </NSpin>
</template>
  
  <script setup>
import { ref, computed, onMounted } from "vue";
import { NInput, NIcon, NButton, NSpin, useMessage } from "naive-ui";
import { useRouter, useRoute } from "vue-router";
import { addknowledgeApi, updateknowledgeApi } from "@/api/knowledgeFactory";
import { getlinksApi } from "@/api/tools";

var message = useMessage();

var storeName = ref("");
var storeDesc = ref("");
var isupdate = ref(false);
var id = ref("");
const loading = ref(false);
const router = useRouter();
const route = useRoute();
function jumpPage(url) {
  if (url) {
    router.push(url);
  } else {
    router.push("/knowledgeFactoryPage");
  }
}
onMounted(() => {
  getlinkfun();
  const params = route.query;
  if (params) {
    storeName.value = params.storeName;
    storeDesc.value = params.storeDesc;
    id.value = params.id;
    if (params.id) {
      isupdate.value = true;
    }
  }
});
var departId = ref("");
function getlinkfun() {
  loading.value = true;
  getlinksApi({ linkType: "ZSK" })
    .then((res) => {
      departId.value = res.data[0].linkUrl;
    })
    .finally(() => {
      loading.value = false;
    });
}
function send() {
  loading.value = true;
  var params = {
    storeName: storeName.value,
    storeDesc: storeDesc.value,
    departId: departId.value,
  };
  console.log(params, "params");
  var requestApi = addknowledgeApi;
  if (id.value) {
    params.id = id.value;
    requestApi = updateknowledgeApi;
  }
  requestApi(params)
    .then((res) => {
      loading.value = false;
      console.log(res, "res");
      if (res.code == "0") {
        jumpPage("/knowledgeFactoryPage");
      } else {
        message.error(res.message);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
  
  <style scoped lang="less">
.app {
  background: url("@/assets/topbg.png") no-repeat;
  background-size: 90% 220px;
  background-position-x: 5%;
}
.app {
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 30px;
}
.red {
  color: #ff5f5f;
  margin-right: 4px;
}
.headers {
  height: 40px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  .left {
    color: #323233;
    font-size: 20px;
    font-weight: 500;
    line-height: 0;
    letter-spacing: 0;
    line-height: 40px;
    display: flex;
    align-items: center;
    .gohome {
      width: 40px;
      height: 40px;
      background: #fafbff;
      border: 1px solid #e9ecf3;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 11px;
      img {
        width: 20px;
        height: 20px;
      }
    }
    span {
      color: #909399;
      margin-left: 8px;
    }
  }
  .headbtnrow {
    button {
      width: 80px;
      height: 40px;
      border-radius: 10px;
      margin-left: 16px;
    }
  }
}
.setconbg {
  padding-top: 90px;
  padding-left: 24px;
  padding-right: 24px;
  background: url("@/assets/conbg.png") no-repeat;
  background-size: 100% 209px;
}
.rowtit {
  height: 21px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 15px;
  color: #323233;
  letter-spacing: 0;
  display: flex;
  align-items: center;
  margin-bottom: 9px;
}
.inputset-38 {
  height: 38px;
  /deep/ .n-input__input-el {
    height: 38px;
  }
}
.inputset-120 {
  height: 120px;
  /deep/ .n-input__input-el {
    height: 120px;
  }
}
.gohome:hover {
  cursor: pointer;
}
</style>
  