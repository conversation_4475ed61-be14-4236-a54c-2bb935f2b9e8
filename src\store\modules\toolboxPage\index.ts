import { defineStore } from 'pinia'
import type { ToolboxPageData, ToolboxPageState } from './helper'
import { defaultSetting, getLocalState, setLocalState } from './helper'

// 定义store
export const useToolboxPageStore = defineStore('toolbox-page-store', {
  state: (): ToolboxPageState => getLocalState(),
  actions: {
    updateToolboxData(data: Partial<ToolboxPageData>) {
      this.toolboxData = { ...this.toolboxData, ...data }
    //   this.recordState()
    },
    recordState() {
      setLocalState(this.$state)
    },
  },
})