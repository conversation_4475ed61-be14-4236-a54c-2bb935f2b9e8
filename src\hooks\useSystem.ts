import { rsa_encrypt, sm4_decrypt, sm4_encrypt } from 'eucp-baselib'

// 是否使用rsa加密
const useRsaCrypto = true
export function useCrypto() {
  /* const publicKey = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDKe+Jx5wt0nOb5M4qbzxyeux1vAgH8sCR0b5/qVkpVLXz3oss7PnxFQkCeT20L79/35Yync/bstFAb6MxHALoztN5e+zMfqf9uCJwhwyucbPVDNDwrgRC1vKiw6PlbS1OPHKGxQKp7zlqoksDdBTN4xJwddT3Tmu+0TROVjAs9XQIDAQAB`;
  const encrypt = (data: string): string => {
    if (!useRsaCrypto) {
      return data;
    }
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(publicKey);
    // base64加密
    // return btoa(encryptor.encrypt(data) as string);
    return encryptor.encrypt(data) as string;
  };
  */
  if (!useRsaCrypto)
    return { encrypt: (data: string) => data }

  function sm4Encrypt(data: string) {
    return sm4_encrypt(data)
  }
  function sm4Decrypt(data: string) {
    return sm4_decrypt(data)
  }
  // 使用wasm rsa加密
  return { encrypt: rsa_encrypt, sm4Encrypt, sm4Decrypt }
}

// test useCrypto
// const { encrypt } = useCrypto();
// console.log(encrypt('123456'));
