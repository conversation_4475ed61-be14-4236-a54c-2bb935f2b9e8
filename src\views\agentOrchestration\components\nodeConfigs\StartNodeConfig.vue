<template>
  <div class="start-node-config">
    <!-- 多轮对话配置 -->
    <n-form-item
      path="config.duolun"
      label-placement="left"
      class="setHeight mb-[9px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 多轮对话
          <n-popover trigger="click">
            <template #trigger>
              <img src="@/assets/agentOrchestration/promptIcon.png" />
            </template>
            <span>多轮对话的描述</span>
          </n-popover>
        </div>
      </template>
      <div class="flex justify-end w-full">
        <n-switch v-model:value="formData.config.duolun" @update:value="handleDuolunChange" />
      </div>
    </n-form-item>

    <!-- 携带历史对话轮数 -->
    <n-form-item
      class="h-[38px] mb-[12px]"
      label=""
      path="config.lunshu"
      v-if="formData.config.duolun"
      label-placement="left"
    >
      <template #label>
        <div class="histit">携带历史对话轮数</div>
      </template>
      <div class="flex justify-end w-full h-[38px]">
        <div class="w-[114px]">
          <n-input-number
            min="0"
            v-model:value="formData.config.lunshu"
            placeholder="轮数"
          />
        </div>
      </div>
    </n-form-item>

    <!-- 输入标题 -->
    <n-form-item
      label-placement="left"
      class="setHeight mt-[12px] mb-[6px]"
    >
      <template #label>
        <div class="rowstit"><span class="rowicon"></span>输入</div>
      </template>
    </n-form-item>

    <!-- 会话数据变量表格 -->
    <n-data-table
      :bordered="false"
      single-column
      :columns="startColumns"
      :data="startData"
    />

    <div class="divider mt-[16px] mb-[16px]"></div>

    <!-- 环境变量表格 -->
    <n-data-table
      :bordered="false"
      single-column
      :columns="environmentVariableColumns"
      :data="environmentVariableData"
    />

    <!-- 文件上传配置 -->
    <n-form-item
      path="config.shangchuan"
      label-placement="left"
      class="setHeight mt-[17px] mb-[6px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 文件上传
          <n-popover trigger="click">
            <template #trigger>
              <img src="@/assets/agentOrchestration/promptIcon.png" />
            </template>
            <span>文件上传的描述</span>
          </n-popover>
        </div>
      </template>
      <div class="flex justify-end w-full">
        <n-switch v-model:value="formData.config.shangchuan" @update:value="handleShangchuanChange" />
      </div>
    </n-form-item>

    <!-- 文件数据表格 -->
    <n-data-table
      v-if="formData.config.shangchuan"
      :bordered="false"
      single-column
      :columns="fileColumns"
      :data="fileData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { 
  NFormItem, 
  NSwitch, 
  NInputNumber, 
  NDataTable, 
  NPopover 
} from 'naive-ui';
import { useNodeConfig } from './composables/useNodeConfig';
import { generateId } from '@/store/modules/orchestration';
import type { NodeConfigProps, NodeConfigEvents } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const { orchestrationStore } = useNodeConfig(props);

// 表格列定义
const startColumns = ref([
  {
    title: "会话数据变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);

const environmentVariableColumns = ref([
  {
    title: "环境变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);

const fileColumns = ref([
  {
    title: "会话数据变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);

// 表格数据
const startData = computed(() => {
  const data = [
    {
      name: "历史对话信息",
      type: "数组 [结构化数据]",
      isreturn: props.formData.config.duolun ? true : false,
    },
    {
      name: "当前对话信息",
      type: "文本",
      isreturn: true,
    },
    {
      name: "当前对话文件",
      type: "数组[文件]",
      isreturn: props.formData.config.shangchuan ? true : false,
    },
    {
      name: "用户ID",
      type: "文本",
      isreturn: true,
    },
    {
      name: "会话ID",
      type: "文本",
      isreturn: props.formData.config.duolun ? true : false,
    },
    {
      name: "对话轮次",
      type: "数值",
      isreturn: props.formData.config.duolun ? true : false,
    },
  ];

  return data.filter((item) => item.isreturn);
});

const environmentVariableData = ref([
  {
    name: "智能体ID",
    type: "文本",
  },
  {
    name: "触发时间",
    type: "文本",
  },
]);

const fileData = ref([
  {
    name: "文件信息",
    type: "结构化数据",
  },
]);

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'start') {
      const config = newNode.data.config;
      
      // 初始化默认配置
      if (config.duolun === undefined) {
        config.duolun = false;
      }
      if (config.shangchuan === undefined) {
        config.shangchuan = false;
      }
      if (!config.lunshu) {
        config.lunshu = 0;
      }
    }
  },
  { immediate: true, deep: true }
);

// 处理多轮对话变更
const handleDuolunChange = (value: boolean) => {
  handleChangeStart(value, "0");
};

// 处理文件上传变更
const handleShangchuanChange = (value: boolean) => {
  handleChangeStart(value, "1");
};

// 处理开始节点变量变更
const handleChangeStart = (value: boolean, type: string) => {
  const multiwheelVariable = [
    { name: "历史对话信息", type: "array" },
    { name: "会话ID", type: "string" },
    { name: "对话轮次", type: "number" },
  ];
  const uploadVariable = [{ name: "当前对话文件", type: "array" }];

  if (value) {
    if (type === "0") {
      multiwheelVariable.forEach((item: any) => {
        orchestrationStore.addVariable({
          id: generateId(),
          name: item.name,
          type: "nodeVariable",
          valueType: item.type,
          value: "",
          readonly: true,
          nodeId: props.node?.id,
          nodeName: props.node?.data.label,
          nodeType: props.node?.type,
        });
      });
    } else {
      uploadVariable.forEach((item: any) => {
        orchestrationStore.addVariable({
          id: generateId(),
          name: item.name,
          type: "nodeVariable",
          valueType: item.type,
          value: "",
          readonly: true,
          nodeId: props.node?.id,
          nodeName: props.node?.data.label,
          nodeType: props.node?.type,
        });
      });
    }
  } else {
    if (type === "0") {
      orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => variable.nodeType === "start")
        .forEach((item: any) => {
          if (
            item.name === "历史对话信息" ||
            item.name === "会话ID" ||
            item.name === "对话轮次"
          ) {
            orchestrationStore.deleteVariable(item.id);
          }
        });
    } else {
      orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => variable.nodeType === "start")
        .forEach((item: any) => {
          if (item.name === "当前对话文件") {
            orchestrationStore.deleteVariable(item.id);
          }
        });
    }
  }
};
</script>

<style scoped lang="less">
.start-node-config {
  .rowstit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
    width: 100%;

    .rowicon {
      width: 5px;
      height: 16px;
      background: #abc6ff;
      background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
      border-radius: 3px;
      margin-right: 9px;
    }

    img {
      width: 16px;
      height: 16px;
      margin-left: 8px;
    }
  }

  .setHeight {
    :deep(.n-form-item-label) {
      height: 22px !important;
      min-height: 22px;
    }

    :deep(.n-form-item-blank) {
      height: 22px;
      min-height: 22px;
    }
  }

  .histit {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #565756;
    letter-spacing: 0;
  }

  .divider {
    width: 100%;
    height: 0.92px;
    background: #0000000f;
    margin-bottom: 20px;
  }

  :deep(.n-data-table-thead) {
    background-color: #ffffff;

    .n-data-table-th {
      background-color: #ffffff;

      .n-data-table-th__title {
        color: #bebebe;
      }
    }
  }

  :deep(.n-data-table .n-data-table-th) {
    border: 0px;
  }

  :deep(.n-data-table.n-data-table--bottom-bordered .n-data-table-td.n-data-table-td--last-row) {
    border: 0px;
  }

  :deep(.n-data-table-thead .n-data-table-th .n-data-table-th__title) {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #c7c7c7;
  }

  :deep(.n-data-table-td) {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #565756;
    letter-spacing: 0;
  }

  :deep(.n-data-table-tr) {
    height: 22px;
    min-height: 22px;

    .n-data-table-th {
      height: 30px;
      padding: 0px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #c7c7c7;
    }

    .n-data-table-td {
      height: 38px;
      padding: 0px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #565756;
      letter-spacing: 0;
    }
  }
}
</style>
