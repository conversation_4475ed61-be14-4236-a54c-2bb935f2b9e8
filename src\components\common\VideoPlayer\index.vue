<script lang="ts" setup>
import {
  computed,
  defineProps,
  onMounted,
  onUnmounted,
  ref,
  watch,
  withDefaults,
} from "vue";
import Hls from "hls.js";
import fullscreenIcon from "@/assets/toolboxPage/fullscreen.png";
// import { NCollapse,NCollapseItem } from 'naive-ui'

// 定义知识点类型
interface KnowledgePoint {
  time: number;
  title: string;
  description: string;
}

// 组件属性定义
interface Props {
  videoSrc: string;
  knowledgePoints: KnowledgePoint[];
  autoHighlightPoints?: boolean;
  autoPauseAtPoints?: boolean;
  courseName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  autoHighlightPoints: true,
  autoPauseAtPoints: false,
});

// 定义事件
const emit = defineEmits<{
  ended: [];
  play: [];
  loadeddata: [];
}>();

// 视频相关状态
const videoRef = ref<HTMLVideoElement | null>(null);
const videoContainerRef = ref<HTMLDivElement | null>(null);
const timelineRef = ref<HTMLDivElement | null>(null);
const isPlaying = ref(false);
const isMuted = ref(false);
const volume = ref(1);
const currentTime = ref(0);
const duration = ref(0);
const hls = ref<Hls | null>(null);

// 知识点相关状态
const timelinePointsRef = ref<HTMLElement | null>(null);
const currentPointIndex = ref(-1);
const selectedPoint = ref<KnowledgePoint | null>(null);
const showTooltip = ref(false);
const tooltipContent = ref("");
const tooltipPosition = ref(0);
const showPointTooltipIndex = ref(-1);
const isFullscreen = ref(false);
const showControls = ref(true);
const controlsTimer = ref<NodeJS.Timeout | null>(null);
const cursorTimer = ref<NodeJS.Timeout | null>(null);
const hideCursor = ref(false);
// 格式化时间
const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || !isFinite(seconds)) return "00:00";

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};

// 初始化HLS播放器
const initializeHls = (videoElement: HTMLVideoElement, src: string) => {
  // 销毁之前的hls实例（如果存在）
  if (hls.value) {
    hls.value.destroy();
    hls.value = null;
  }

  // 创建新的HLS实例
  const newHls = new Hls({
    enableWorker: true,
    lowLatencyMode: true,
    backBufferLength: 90,
  });

  newHls.attachMedia(videoElement);
  newHls.on(Hls.Events.MEDIA_ATTACHED, () => {
    newHls.loadSource(src);
  });

  newHls.on(Hls.Events.ERROR, (event, data) => {
    if (data.fatal) {
      switch (data.type) {
        case Hls.ErrorTypes.NETWORK_ERROR:
          // 尝试恢复网络错误
          console.error("HLS网络错误，尝试恢复", data);
          newHls.startLoad();
          break;
        case Hls.ErrorTypes.MEDIA_ERROR:
          // 尝试恢复媒体错误
          console.error("HLS媒体错误，尝试恢复", data);
          newHls.recoverMediaError();
          break;
        default:
          // 无法恢复的错误
          console.error("HLS致命错误", data);
          newHls.destroy();
          break;
      }
    }
  });

  hls.value = newHls;
};

// 设置视频源
const setVideoSource = () => {
  if (!videoRef.value || !props.videoSrc) return;

  const video = videoRef.value;
  const source = props.videoSrc;

  // 检查是否是m3u8格式
  if (source.includes(".m3u8")) {
    // 检查浏览器是否原生支持HLS
    if (video.canPlayType("application/vnd.apple.mpegurl")) {
      // 某些浏览器(Safari)原生支持HLS
      video.src = source;
    } else if (Hls.isSupported()) {
      // 对于不支持HLS的浏览器，使用hls.js
      initializeHls(video, source);
    } else {
      console.error("当前浏览器既不支持HLS，也不支持hls.js");
    }
  } else {
    // 普通视频格式，直接设置src
    video.src = source;
  }
};

// 监听视频源变化
watch(
  () => props.videoSrc,
  (newSrc, oldSrc) => {
    if (newSrc !== oldSrc) {
      // 重置状态
      currentTime.value = 0;
      duration.value = 0;

      // 设置新的视频源
      setVideoSource();
    }
  },
  { immediate: false }
);

// 视频控制功能
const togglePlay = (): void => {
  if (!videoRef.value) return;

  if (isPlaying.value) videoRef.value.pause();
  else videoRef.value.play();

  // 播放/暂停时显示控制栏
  if (isFullscreen.value) {
    showControlsBar();
  }
};

const toggleMute = (): void => {
  if (!videoRef.value) return;

  isMuted.value = !isMuted.value;
  videoRef.value.muted = isMuted.value;

  // 音量切换时显示控制栏
  if (isFullscreen.value) {
    showControlsBar();
  }
};

// 监听音量变化
watch(volume, (newVolume) => {
  if (!videoRef.value) return;
  videoRef.value.volume = newVolume;

  // 更新音量滑块的样式
  const sliders = document.querySelectorAll(".slider");
  sliders.forEach((slider: any) => {
    slider.style.setProperty("--value", `${newVolume * 100}%`);
  });
});

// 时间轴交互
const onTimelineClick = (event: MouseEvent): void => {
  if (!timelineRef.value || !videoRef.value) return;

  const rect = timelineRef.value.getBoundingClientRect();
  const pos = (event.clientX - rect.left) / rect.width;

  if (videoRef.value && duration.value)
    videoRef.value.currentTime = pos * duration.value;
};

// 视频事件处理
const onTimeUpdate = (): void => {
  if (!videoRef.value) return;

  currentTime.value = videoRef.value.currentTime;

  // 检测当前是否位于知识点
  if (props.autoHighlightPoints) {
    const index = props.knowledgePoints.findIndex((point, idx, arr) => {
      const nextPoint = arr[idx + 1];
      const isInRange =
        currentTime.value >= point.time &&
        (nextPoint ? currentTime.value < nextPoint.time : true);
      return isInRange;
    });

    if (index !== -1 && index !== currentPointIndex.value) {
      currentPointIndex.value = index;

      if (
        props.autoPauseAtPoints &&
        Math.abs(currentTime.value - props.knowledgePoints[index].time) < 0.3
      ) {
        videoRef.value.pause();
        selectedPoint.value = props.knowledgePoints[index];
      }
    }
  }
};

const onVideoLoaded = (): void => {
  if (!videoRef.value) return;

  duration.value = videoRef.value.duration;
};

// 视频数据加载完成处理
const onVideoLoadedData = (): void => {
  // 触发loadeddata事件
  emit("loadeddata");
};

// 视频播放完成处理
const handleVideoEnded = (): void => {
  isPlaying.value = false;
  emit("ended");
};

// 视频播放开始处理
const handleVideoPlay = (): void => {
  isPlaying.value = true;
  emit("play");
};

// 新增函数：点击进度条上的点，并高亮对应的时间轴知识点
const seekToPointAndHighlight = (
  point: KnowledgePoint,
  index: number
): void => {
  seekToPoint(point, index);

  // 滚动到时间轴上对应的知识点
  // setTimeout(() => {
  //   const element = document.getElementById(`timeline-point-${index}`)
  //   if (element)
  //     element.scrollIntoView({ behavior: 'smooth', block: 'center' })
  // }, 300)
};

// 修改原有的seekToPoint函数
const seekToPoint = (point: KnowledgePoint, index: number): void => {
  if (!videoRef.value) return;

  videoRef.value.currentTime = point.time;
  selectedPoint.value = point;
  currentPointIndex.value = index;
};

const showPointTooltip = (point: KnowledgePoint, index: number): void => {
  if (!timelineRef.value || !duration.value) return;
  showPointTooltipIndex.value = index;

  const rect = timelineRef.value.getBoundingClientRect();
  tooltipPosition.value =
    rect.left + (point.time / duration.value) * rect.width;
  tooltipContent.value = `${point.title} (${formatTime(point.time)})`;
  showTooltip.value = true;
};

const hidePointTooltip = (): void => {
  showTooltip.value = false;
};

// 跳转到指定时间
const seekToTime = (time: number): void => {
  if (!videoRef.value) return;
  videoRef.value.currentTime = time;
  // 如果视频暂停，则播放
  if (videoRef.value.paused) {
    videoRef.value.play();
  }
};

// 全屏相关方法
const toggleFullscreen = (): void => {
  if (!videoContainerRef.value) return;

  if (!isFullscreen.value) {
    // 进入全屏
    if (videoContainerRef.value.requestFullscreen) {
      videoContainerRef.value.requestFullscreen();
    } else if ((videoContainerRef.value as any).webkitRequestFullscreen) {
      (videoContainerRef.value as any).webkitRequestFullscreen();
    } else if ((videoContainerRef.value as any).msRequestFullscreen) {
      (videoContainerRef.value as any).msRequestFullscreen();
    }
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }
  }
};

// 监听全屏状态变化
const handleFullscreenChange = (): void => {
  isFullscreen.value = Boolean(
    document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement
  );
};

// 控制栏显示/隐藏逻辑
const showControlsBar = (): void => {
  showControls.value = true;
  hideCursor.value = false;

  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value);
  }
  if (cursorTimer.value) {
    clearTimeout(cursorTimer.value);
  }

  // 全屏模式下3秒后自动隐藏控制栏和光标
  if (isFullscreen.value) {
    controlsTimer.value = setTimeout(() => {
      showControls.value = false;
    }, 3000);

    cursorTimer.value = setTimeout(() => {
      hideCursor.value = true;
    }, 3000);
  }
};

const hideControlsBar = (): void => {
  if (isFullscreen.value) {
    showControls.value = false;
    hideCursor.value = true;
  }
};

const onMouseMove = (): void => {
  if (isFullscreen.value) {
    showControlsBar();
  }
};

const onMouseLeave = (): void => {
  if (isFullscreen.value) {
    hideControlsBar();
  }
};

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent): void => {
  if (!isFullscreen.value) return;

  switch (event.code) {
    case "Space":
      event.preventDefault();
      togglePlay();
      break;
    case "KeyF":
      event.preventDefault();
      toggleFullscreen();
      break;
    case "KeyM":
      event.preventDefault();
      toggleMute();
      break;
    case "Escape":
      if (isFullscreen.value) {
        toggleFullscreen();
      }
      break;
  }

  // 按键时显示控制栏
  showControlsBar();
};

// 组件生命周期
onMounted(() => {
  // 设置视频源
  if (videoRef.value) setVideoSource();

  // 添加全屏事件监听
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  document.addEventListener("msfullscreenchange", handleFullscreenChange);

  // 添加键盘事件监听
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  // 销毁HLS实例
  if (hls.value) {
    hls.value.destroy();
    hls.value = null;
  }

  // 暂停视频
  if (videoRef.value) videoRef.value.pause();

  // 清除定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value);
  }
  if (cursorTimer.value) {
    clearTimeout(cursorTimer.value);
  }

  // 移除全屏事件监听
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
  document.removeEventListener(
    "webkitfullscreenchange",
    handleFullscreenChange
  );
  document.removeEventListener("msfullscreenchange", handleFullscreenChange);

  // 移除键盘事件监听
  document.removeEventListener("keydown", handleKeydown);
});

// 暴露方法给父组件
defineExpose({
  seekToTime,
  toggleFullscreen,
  togglePlay,
});
</script>

<template>
  <div class="p-4 bg-[#fff]">
    <!-- 视频播放区域 -->
    <div
      ref="videoContainerRef"
      class="relative bg-black rounded-lg overflow-hidden video-container pb-20"
      :class="{
        'rounded-none': isFullscreen,
        'fullscreen-cursor-hidden': isFullscreen && hideCursor,
      }"
      @mousemove="onMouseMove"
      @mouseleave="onMouseLeave"
    >
      <video
        ref="videoRef"
        class="w-full aspect-video"
        :class="{ 'aspect-auto h-full': isFullscreen }"
        :src="videoSrc"
        @timeupdate="onTimeUpdate"
        @loadedmetadata="onVideoLoaded"
        @loadeddata="onVideoLoadedData"
        @play="handleVideoPlay"
        @pause="isPlaying = false"
        @ended="handleVideoEnded"
      />

      <!-- 视频中间播放按钮 -->
      <div
        v-if="!isPlaying"
        class="absolute inset-0 flex items-center justify-center cursor-pointer bg-black bg-opacity-30"
        @click="togglePlay"
      >
        <div
          class="w-16 h-16 rounded-full bg-white bg-opacity-80 flex items-center justify-center"
        >
          <svg
            class="w-8 h-8 text-blue-600"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M8 5v10l7-5-7-5z" />
          </svg>
        </div>
      </div>
      <!-- 视频控件区域 - 移到视频容器内部 -->
      <div
        class="bg-gradient-to-b absolute bottom-0 left-0 right-0 from-transparent via-black/50 to-black text-white transition-all duration-300"
      >
        <!-- 进度条区域 -->
        <div class="relative h-8 group px-4 pt-3">
          <div
            ref="timelineRef"
            class="relative h-2 bg-white/20 rounded-full cursor-pointer hover:h-2.5 transition-all duration-200"
            @click="onTimelineClick"
          >
            <!-- 缓冲进度 -->
            <div
              class="absolute top-0 left-0 h-full bg-white/30 rounded-full"
              :style="{ width: '60%' }"
            />

            <!-- 播放进度 -->
            <div
              class="absolute top-0 left-0 h-full bg-blue-500 rounded-full transition-all duration-150"
              :style="{ width: `${(currentTime / duration) * 100}%` }"
            />

            <!-- 进度条拖动把手 -->
            <div
              class="absolute top-1/2 w-4 h-4 transform -translate-y-1/2 scale-0 group-hover:scale-100 bg-blue-500 rounded-full shadow-lg transition-all duration-200 cursor-grab active:cursor-grabbing"
              :style="{
                left: `calc(${(currentTime / duration) * 100}% - 8px)`,
              }"
            />

            <!-- 知识点标记 -->
            <div
              v-for="(point, index) in knowledgePoints"
              :key="index"
              class="absolute top-1/2 w-3 h-3 transform -translate-y-1/2 cursor-pointer transition-all duration-200 rounded-full border-2 border-white shadow-sm"
              :class="[
                currentPointIndex === index
                  ? 'bg-red-500 scale-110'
                  : 'bg-yellow-400 hover:scale-110',
              ]"
              :style="{ left: `calc(${(point.time / duration) * 100}% - 6px)` }"
              @click.stop="seekToPointAndHighlight(point, index)"
              @mouseenter="showPointTooltip(point, index)"
              @mouseleave="hidePointTooltip"
            >
              <!-- 时间提示 -->
              <div
                v-if="showTooltip && showPointTooltipIndex === index"
                class="absolute -top-10 left-1/2 transform -translate-x-1/2 px-3 py-1.5 text-xs text-white bg-black/80 rounded-lg z-20 whitespace-nowrap backdrop-blur-sm"
              >
                {{ tooltipContent }}
                <div
                  class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 控制按钮区域 -->
        <div class="flex items-center justify-between px-4 pb-3">
          <div class="flex items-center space-x-3">
            <!-- 播放/暂停按钮 -->
            <button
              class="flex items-center justify-center w-10 h-10 rounded-full hover:bg-white/20 focus:outline-none transition-all duration-200 bg-white/10"
              @click="togglePlay"
            >
              <svg
                v-if="isPlaying"
                class="w-6 h-6"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M5 4h3v12H5V4zm7 0h3v12h-3V4z" />
              </svg>
              <svg
                v-else
                class="w-6 h-6 ml-0.5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M8 5v10l7-5-7-5z" />
              </svg>
            </button>

            <!-- 音量控制 -->
            <div class="flex items-center space-x-2">
              <button
                class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-white/20 focus:outline-none transition-all duration-200"
                @click="toggleMute"
              >
                <svg
                  v-if="isMuted"
                  class="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z"
                  />
                </svg>
                <svg
                  v-else
                  class="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828a1 1 0 010-1.415z"
                  />
                </svg>
              </button>
              <div class="relative group">
                <input
                  v-model="volume"
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  class="w-20 h-1.5 appearance-none bg-white/30 rounded-full outline-none cursor-pointer slider"
                />
              </div>
            </div>

            <!-- 时间显示 -->
            <div class="text-sm font-medium">
              {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
            </div>
          </div>

          <!-- 右侧工具 -->
          <div class="flex items-center space-x-2">
            <!-- 全屏按钮 -->
            <button
              class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-white/20 focus:outline-none transition-all duration-200"
              @click="toggleFullscreen"
              :title="isFullscreen ? '退出全屏' : '全屏'"
            >
              <img :src="fullscreenIcon" alt="全屏" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 知识点详情区域 -->
    <!--			<n-collapse arrow-placement="right"> -->
    <!--				<n-collapse-item title="青铜" name="1"> -->
    <!--					<div>可以</div> -->
    <!--				</n-collapse-item> -->
    <!--				<n-collapse-item title="白银" name="2"> -->
    <!--					<div>很好</div> -->
    <!--				</n-collapse-item> -->
    <!--				<n-collapse-item title="黄金" name="3"> -->
    <!--					<div>真棒</div> -->
    <!--				</n-collapse-item> -->
    <!--			</n-collapse> -->

    <!-- <div v-if="selectedPoint" class="p-4 border-t border-gray-200">
      <div class="flex justify-between items-center mb-2">
        <h3 class="text-lg font-medium text-gray-900">
          {{ selectedPoint.title }}
        </h3>
        <span class="text-sm text-gray-500">{{
          formatTime(selectedPoint.time)
        }}</span>
      </div>
      <p class="text-gray-700">
        {{ selectedPoint.description }}
      </p>
    </div> -->
  </div>
</template>

<style scoped lang="less">
.containers {
  box-shadow: 0 0 10px 0 #00000021;
  border-radius: 16px;
}

/* 音量滑块样式 */
.slider {
  background: linear-gradient(
    to right,
    #3b82f6 0%,
    #3b82f6 var(--value, 100%),
    rgba(255, 255, 255, 0.3) var(--value, 100%),
    rgba(255, 255, 255, 0.3) 100%
  );
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

.slider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

/* 进度条容器动画 */
.group:hover .bg-white\/20 {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 知识点提示框动画 */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -5px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.tooltip-enter-active {
  animation: tooltipFadeIn 0.2s ease-out;
}

/* 全屏模式下的样式调整 */
:fullscreen {
  .bg-gradient-to-b {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(0, 0, 0, 0.3) 50%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
}

/* 兼容性前缀 */
:-webkit-full-screen {
  .bg-gradient-to-b {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(0, 0, 0, 0.3) 50%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
}

:-moz-full-screen {
  .bg-gradient-to-b {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(0, 0, 0, 0.3) 50%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
}

/* 全屏模式下视频容器样式 */
:fullscreen .video-container {
  height: 100vh;
  width: 100vw;
  border-radius: 0;
}

:-webkit-full-screen .video-container {
  height: 100vh;
  width: 100vw;
  border-radius: 0;
}

:-moz-full-screen .video-container {
  height: 100vh;
  width: 100vw;
  border-radius: 0;
}

/* 鼠标光标在全屏时的隐藏效果 */
.fullscreen-cursor-hidden {
  cursor: none;
}

/* 控制栏动画 */
.controls-fade-enter-active,
.controls-fade-leave-active {
  transition: opacity 0.3s ease;
}

.controls-fade-enter-from,
.controls-fade-leave-to {
  opacity: 0;
}
</style>