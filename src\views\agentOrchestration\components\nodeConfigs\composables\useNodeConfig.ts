import { ref, computed, watch, nextTick } from 'vue';
import { useMessage, useDialog } from 'naive-ui';
import { useOrchestrationStore } from '@/store';
import type { FlowNode } from '@/store/modules/orchestration';
import type { SelectOption, AggregationOption } from '../types';

export function useNodeConfig(props: { node: FlowNode | null }) {
  const message = useMessage();
  const dialog = useDialog();
  const orchestrationStore = useOrchestrationStore();

  // 运行状态
  const isRunning = ref(false);

  // 获取数据类型标签文本
  const getDataTypeLabel = (dataType: string) => {
    const labelMap: Record<string, string> = {
      string: "文本",
      number: "数字",
      boolean: "布尔",
      array: "数组",
      object: "对象",
    };
    return labelMap[dataType] || dataType;
  };

  // 基础选项数据
  const variableOptions: SelectOption[] = [
    { label: "变量", value: "0" },
    { label: "固定", value: "1" },
  ];

  const operateOptions: SelectOption[] = [
    { label: "覆盖", value: "0" },
    { label: "设置", value: "1" },
    { label: "清空", value: "2" },
  ];

  const methodOptions: SelectOption[] = [
    { label: "GET", value: "GET" },
    { label: "POST", value: "POST" },
    { label: "PUT", value: "PUT" },
    { label: "DELETE", value: "DELETE" },
  ];

  // 条件节点相关选项
  const conditionOperatorOptions: SelectOption[] = [
    { label: "等于", value: "等于" },
    { label: "不等于", value: "不等于" },
    { label: "大于", value: "大于" },
    { label: "小于", value: "小于" },
    { label: "包含", value: "包含" },
    { label: "不包含", value: "不包含" },
  ];

  const logicOperatorOptions: SelectOption[] = [
    { label: "且", value: "且" },
    { label: "或", value: "或" },
    { label: "非", value: "非" },
  ];

  // 问题分类器匹配规则选项
  const matchRuleOptions: SelectOption[] = [
    { label: "包含匹配", value: "contains" },
    { label: "精确匹配", value: "exact" },
    { label: "正则匹配", value: "regex" },
  ];

  // 获取节点的直接前置节点
  const getDirectPreviousNodes = (nodeId: string) => {
    if (!orchestrationStore.currentFlow) return [];
    return orchestrationStore.currentFlow.edges
      .filter((edge) => edge.target === nodeId)
      .map((edge) => edge.source);
  };

  // 递归获取所有前置节点
  const getAllPreviousNodes = (
    nodeId: string,
    visited: Set<string> = new Set()
  ): string[] => {
    if (!orchestrationStore.currentFlow) return [];

    if (visited.has(nodeId)) return [];
    visited.add(nodeId);

    const directPrevious = getDirectPreviousNodes(nodeId);
    const allPrevious = new Set(directPrevious);

    directPrevious.forEach((prevNodeId) => {
      const indirectPrevious = getAllPreviousNodes(prevNodeId, visited);
      indirectPrevious.forEach((node) => allPrevious.add(node));
    });

    return Array.from(allPrevious);
  };

  // 检查节点是否与其他节点连接
  const isNodeConnected = (nodeId: string) => {
    const previousNodes = getDirectPreviousNodes(nodeId);
    const nextNodes = orchestrationStore.getNextNodes(nodeId);
    return previousNodes.length > 0 || nextNodes.length > 0;
  };

  // 聚合选项数据
  const aggregationOptions = ref<AggregationOption[]>([]);

  // 更新聚合选项
  const updateAggregationOptions = () => {
    if (!props.node) return;

    const allPreviousNodes = getAllPreviousNodes(props.node.id);
    const isConnected = isNodeConnected(props.node.id);

    if (!isConnected) {
      // 如果没有连接其他节点，只获取环境变量
      const variables = orchestrationStore
        .getVariablesByType("envVariable")
        .map((variable) => ({
          name: variable.name,
          type: getDataTypeLabel(variable.valueType),
          id: variable.id,
        }));

      aggregationOptions.value = variables.length > 0 ? [
        {
          label: "环境变量",
          value: "envVariable",
          variables: variables,
        },
      ] : [];
    } else {
      // 获取节点变量
      const nodeVariableList = orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => {
          return (
            allPreviousNodes.includes(variable.nodeId || "") &&
            variable.nodeId !== props.node?.id &&
            variable.nodeType !== "start"
          );
        })
        .map((variable) => ({
          label: variable.nodeName || "",
          value: variable.nodeId || "",
          variables: [
            {
              name: variable.name,
              type: getDataTypeLabel(variable.valueType),
              id: variable.id,
            },
          ],
        }));

      // 获取开始节点变量
      const startnodevariable = orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => {
          return (
            allPreviousNodes.includes(variable.nodeId || "") &&
            variable.nodeId !== props.node?.id &&
            variable.nodeType === "start"
          );
        })
        .map((variable) => ({
          name: variable.name,
          type: getDataTypeLabel(variable.valueType),
          id: variable.id,
        }));

      const startnodeVariableList: AggregationOption[] = startnodevariable.length > 0 ? [
        {
          label: "开始节点",
          value: "nodeVariable",
          variables: startnodevariable,
        },
      ] : [];

      // 获取环境变量
      const variables = orchestrationStore
        .getVariablesByType("envVariable")
        .map((variable) => ({
          name: variable.name,
          type: getDataTypeLabel(variable.valueType),
          id: variable.id,
        }));

      const envVariablesList: AggregationOption[] = variables.length > 0 ? [
        {
          label: "环境变量",
          value: "envVariable",
          variables: variables,
        },
      ] : [];

      aggregationOptions.value = envVariablesList
        .concat(startnodeVariableList)
        .concat(nodeVariableList);
    }
  };

  // 聚合节点选择处理
  const handleAggregationChange = (value: string, variable: any, group: any) => {
    console.log("选择的聚合变量:", { value, variable, group });
  };

  // 运行节点
  const handleRunNode = async () => {
    if (!props.node || isRunning.value) return;

    try {
      isRunning.value = true;
      await orchestrationStore.runNode(props.node.id);
      message.success("节点运行成功");
    } catch (error) {
      console.error("节点运行失败:", error);
      message.error("节点运行失败");
    } finally {
      isRunning.value = false;
    }
  };

  // 是否显示运行按钮
  const showRunButton = computed(() => {
    return (
      props.node &&
      ["llm", "api", "condition", "questionClassifier"].includes(props.node.type)
    );
  });

  return {
    message,
    dialog,
    orchestrationStore,
    isRunning,
    getDataTypeLabel,
    variableOptions,
    operateOptions,
    methodOptions,
    conditionOperatorOptions,
    logicOperatorOptions,
    matchRuleOptions,
    aggregationOptions,
    updateAggregationOptions,
    handleAggregationChange,
    handleRunNode,
    showRunButton,
    getAllPreviousNodes,
    isNodeConnected,
  };
}
