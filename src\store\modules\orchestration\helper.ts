import { ss } from '@/utils/storage'
import type { FlowVariable } from '@/types/backend'

const LOCAL_NAME = 'orchestrationStorage'

// 节点状态枚举
export enum NodeStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 节点类型枚举
export enum NodeType {
  START = 'start',
  END = 'end',
  INTENTION_RECOGNITION ='question-classifier',//意图识别
  LLM  ='llm',//文本生成
  CONDITION = 'condition',
  API = 'api',
  CHAT = 'chat',
  KNOWLEDGE = 'knowledge',
  QUESTION_CLASSIFIER = 'questionClassifier',
  CYCLE='cycle',//循环
  AGGREGATION  ='aggregation',//聚合
  VARIABLE  ='variable',//变量赋值
  DIRECTREPLY  ='directReply',//直接回复
  TOOL ='tool'//工具
}

// 节点位置接口
export interface Position {
  x: number
  y: number
}

// 运行日志接口
export interface NodeExecutionLog {
  id: string
  timestamp: string
  status: NodeStatus
  duration: number // 执行时长（毫秒）
  input?: any // 输入数据
  output?: any // 输出结果
  error?: string // 错误信息
  metadata?: Record<string, any> // 额外元数据
}

// 节点数据接口
export interface NodeData {
  label: string
  description?: string
  config?: Record<string, any>
  status?: NodeStatus
  // 运行日志相关
  executionLogs?: NodeExecutionLog[] // 执行历史记录
  lastExecution?: NodeExecutionLog // 最后一次执行记录
  totalExecutions?: number // 总执行次数
  [key: string]: any
}

// 问题分类器配置接口
export interface CategoryConfig {
  id: string
  name: string
  keywords: string[]
  description?: string
  matchRule: 'contains' | 'exact' | 'regex'
}

export interface QuestionClassifierConfig {
  categories: CategoryConfig[]
  defaultCategory?: string
}

// 节点接口
export interface FlowNode {
  id: string
  type: NodeType
  position: Position
  data: NodeData
  draggable?: boolean
  selectable?: boolean
  deletable?: boolean
  handleBounds?: Record<string, any>
}

// 边接口
export interface FlowEdge {
  id: string
  source: string
  target: string
  sourceHandle?: string
  targetHandle?: string
  animated?: boolean
  style?: Record<string, any>
  data?: Record<string, any>
}

// 流程数据接口
export interface FlowData {
  id: string
  name: string
  description?: string
  nodes: FlowNode[]
  edges: FlowEdge[]
  variables?: FlowVariable[] // 新增变量管理字段
  createdAt: string
  updatedAt: string
  metadata?: Record<string, any>
}

// 编排状态接口
export interface OrchestrationState {
  newFlowFrom:{
    name: string
    description: string
    scenceCategory:string
  }
  currentFlow: FlowData | null
  flows: FlowData[]
  selectedNodeId: string | null
  isConfigModalVisible: boolean
  isRunning: boolean
  // 撤销/重做历史记录
  history: {
    past: Array<{ nodes: FlowNode[], edges: FlowEdge[] }>
    present: { nodes: FlowNode[], edges: FlowEdge[] } | null
    future: Array<{ nodes: FlowNode[], edges: FlowEdge[] }>
  }
  // 后端交互状态
  backend: {
    isLoading: boolean
    isSyncing: boolean
    lastSyncTime: string | null
    syncStatus: 'idle' | 'syncing' | 'success' | 'error'
    lastError: string | null
  }
}

export function defaultSetting(): OrchestrationState {
  return {
    newFlowFrom:{
      name: '',
      description: '',
      scenceCategory:""
    },
    currentFlow: null,
    flows: [],
    selectedNodeId: null,
    isConfigModalVisible: false,
    isRunning: false,
    history: {
      past: [],
      present: null,
      future: []
    },
    backend: {
      isLoading: false,
      isSyncing: false,
      lastSyncTime: null,
      syncStatus: 'idle',
      lastError: null
    }
  }
}

export function getLocalState(): OrchestrationState {
  const localSetting: OrchestrationState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalState(setting: OrchestrationState): void {
  ss.set(LOCAL_NAME, setting)
}

// 生成唯一ID的工具函数
export function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 创建默认节点的工具函数
export function createDefaultNode(type: NodeType, position: Position): FlowNode {
  const id = generateId()
  const nodeLabels = {
    [NodeType.START]: '开始',
    [NodeType.END]: '结束',
    [NodeType.INTENTION_RECOGNITION]: '意图识别',
    [NodeType.LLM]: '文本生成',
    [NodeType.CONDITION]: '条件判断',
    [NodeType.API]: 'API调用',
    [NodeType.CHAT]: '对话节点',
    [NodeType.KNOWLEDGE]: '知识检索',
    [NodeType.QUESTION_CLASSIFIER]: '问题分类器',
    [NodeType.CYCLE]: '循环',
    [NodeType.AGGREGATION]: '聚合',
    [NodeType.VARIABLE]: '变量赋值',
    [NodeType.DIRECTREPLY]: '直接回复',
    [NodeType.TOOL]: '工具',
  }

  return {
    id,
    type,
    position,
    data: {
      label: nodeLabels[type],
      status: NodeStatus.IDLE,
      config: {},
      // 初始化运行日志相关字段
      executionLogs: [],
      lastExecution: undefined,
      totalExecutions: 0
    },
    draggable: true,
    selectable: true,
    deletable: type !== NodeType.START && type !== NodeType.END
  }
}

// 创建默认边的工具函数
export function createDefaultEdge(source: string, target: string): FlowEdge {
  return {
    id: `${source}-${target}`,
    source,
    target,
    animated: false
  }
}

// 创建执行日志的工具函数
export function createExecutionLog(
  timestamp:any,
  status: NodeStatus,
  duration: number,
  input?: any,
  output?: any,
  error?: string,
  metadata?: Record<string, any>
): NodeExecutionLog {
  return {
    id: generateId(),
    timestamp,
    status,
    duration,
    input,
    output,
    error,
    metadata
  }
}
