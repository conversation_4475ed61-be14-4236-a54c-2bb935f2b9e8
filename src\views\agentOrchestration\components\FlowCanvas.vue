<script setup lang="ts">
import '@vue-flow/core/dist/style.css'
import { nextTick, onMounted, onUnmounted, ref, shallowRef, watch } from 'vue'
import {
  PanOnScrollMode,
  VueFlow,
  useVueFlow,
} from '@vue-flow/core'
import { useDialog, useMessage } from 'naive-ui'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import type {
  Connection,
  Edge,
  EdgeChange,
  EdgeMouseEvent,
  Node,
  NodeChange,
  NodeMouseEvent,
} from '@vue-flow/core'
import { nodeTypes } from './nodes'
import { useOrchestrationStore } from '@/store'
import { NodeType, createDefaultNode, generateId } from '@/store/modules/orchestration'

const emit = defineEmits<{
  nodeClick: [nodeId: string]
  canvasDrop: [event: DragEvent]
  canvasDragOver: [event: DragEvent]
}>()

// Store
const orchestrationStore = useOrchestrationStore()
const message = useMessage()
const dialog = useDialog()

// 简单的拖拽状态管理
const isDraggingOptimized = ref(false)
const dragNodeId = ref<string | null>(null)

// Vue Flow composable - 按照官方示例的简单方式
const {
  addNodes,
  addEdges,
  removeNodes,
  removeEdges,
  findNode,
  findEdge,
  getNodes,
  getEdges,
  onPaneReady,
  onNodeClick,
  onNodeDoubleClick,
  onEdgeClick,
  onConnect,
  onNodesChange,
  onEdgesChange,
  onNodeDragStop,
  fitView,
} = useVueFlow()

// 响应式数据
const isDragOver = ref(false)
const flowInstance = ref<any>(null)
const isUpdatingFromVueFlow = ref(false) // 防止循环更新的标志
const isDraggingNode = ref(false) // 节点拖拽状态

// 响应式数据 - 使用普通ref确保Vue Flow正常工作
const nodes = ref<Node[]>(orchestrationStore.currentNodes || [])
const edges = ref<Edge[]>(orchestrationStore.currentEdges || [])

// Vue Flow 事件钩子 - 按照官方示例
onNodeDragStop(({ event, nodes: draggedNodes, node }) => {
  console.log('Node Drag Stop', { event, nodes: draggedNodes, node })
  // 拖拽结束后的处理
  isDraggingNode.value = false
  isDraggingOptimized.value = false
  dragNodeId.value = null
})

// 监听store变化，同步到本地ref
watch(() => orchestrationStore.currentNodes, (newNodes) => {
  if (!isUpdatingFromVueFlow.value) {
    nodes.value = [...(newNodes || [])]
  }
}, { deep: true })

watch(() => orchestrationStore.currentEdges, (newEdges) => {
  if (!isUpdatingFromVueFlow.value) {
    edges.value = [...(newEdges || [])]
  }
}, { deep: true })

// 监听本地ref变化，同步到store
watch(nodes, (newNodes) => {
  if (!isUpdatingFromVueFlow.value && orchestrationStore.currentFlow) {
    isUpdatingFromVueFlow.value = true
    orchestrationStore.currentFlow.nodes = [...newNodes] as any
    orchestrationStore.saveCurrentFlow()
    nextTick(() => {
      isUpdatingFromVueFlow.value = false
    })
  }
}, { deep: true })

watch(edges, (newEdges) => {
  if (!isUpdatingFromVueFlow.value && orchestrationStore.currentFlow) {
    isUpdatingFromVueFlow.value = true
    orchestrationStore.currentFlow.edges = [...newEdges] as any
    orchestrationStore.saveCurrentFlow()
    nextTick(() => {
      isUpdatingFromVueFlow.value = false
    })
  }
}, { deep: true })

// 节点颜色映射
const getNodeColor = (node: Node) => {
  const colorMap = {
    [NodeType.START]: '#52c41a',
    [NodeType.END]: '#ff4d4f',
    [NodeType.LLM]: '#1890ff',
    [NodeType.API]: '#722ed1',
    [NodeType.CONDITION]: '#fa8c16',
    [NodeType.QUESTION_CLASSIFIER]: '#fa8c16',
    [NodeType.CHAT]: '#13c2c2',
    [NodeType.KNOWLEDGE]: '#2f54eb',
    default: '#d9d9d9',
  }
  return colorMap[node.type as NodeType] || colorMap.default
}

// 拖放处理
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  try {
    const data = event.dataTransfer?.getData('application/json')

    if (data) {
      const nodeData = JSON.parse(data)

      // 确保有当前流程，如果没有则创建一个
      if (!orchestrationStore.currentFlow)
        orchestrationStore.createNewFlow('新流程', '通过拖拽创建的流程')

      // 获取画布坐标
      const rect = (event.target as Element).getBoundingClientRect()
      let position = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      }

      // 如果flowInstance可用，使用project方法转换坐标
      if (flowInstance.value && flowInstance.value.project)
        position = flowInstance.value.project(position)

      // 检查开始节点是否已存在（保持开始节点唯一性）
      if (nodeData.type === 'start') {
        const existingStartNode = nodes.value.find(node => node.type === 'start')
        if (existingStartNode) {
          message.warning('每个流程只能有一个开始节点')
          return
        }
      }

      // 移除结束节点的数量限制，允许多个结束节点

      // 创建新节点
      const newNode = createDefaultNode(nodeData.type, position)
      newNode.data = {
        ...newNode.data,
        label: nodeData.label,
        color: nodeData.color,
        icon: nodeData.icon,
        description: nodeData.description,
      }

      // 使用Vue Flow API添加节点（推荐方式）
      addNodes([newNode])

      // 由于我们已经修复了双向绑定机制，Vue Flow的状态变化会自动同步到store
      // 不需要手动调用 orchestrationStore.addNode(newNode) 避免重复添加
    }
  }
  catch (error) {
    console.error('Error parsing dropped data:', error)
  }

  emit('canvasDrop', event)
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
  emit('canvasDragOver', event)
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  const target = event.currentTarget as Element
  const related = event.relatedTarget as Element

  // 只有当离开整个拖放区域时才设置为false
  if (!target?.contains(related))
    isDragOver.value = false
}

// Vue Flow 事件处理
const handleNodeClick = (param: NodeMouseEvent) => {
  console.log('Node clicked:', param.node)
  emit('nodeClick', param.node.id)
}

const handleNodeDoubleClick = (param: NodeMouseEvent) => {
  console.log('Node double clicked:', param.node)
  orchestrationStore.setSelectedNode(param.node.id)
  orchestrationStore.setConfigModalVisible(true)
}

const handleEdgeClick = (param: EdgeMouseEvent) => {
  console.log('Edge clicked:', param.edge)

  // 取消所有节点的选中状态
  nodes.value.forEach((node) => {
    (node as any).selected = false
  })
  orchestrationStore.setSelectedNode(null)

  // 切换边的选中状态
  const edgeIndex = edges.value.findIndex(e => e.id === param.edge.id)
  if (edgeIndex !== -1) {
    // 先取消所有其他边的选中状态
    edges.value.forEach((e, index) => {
      if (index !== edgeIndex)
        (e as any).selected = false
    })

    // 设置当前边为选中状态
    const currentEdge = edges.value[edgeIndex] as any
    currentEdge.selected = true

    // 强制触发响应式更新
    edges.value = [...edges.value]
  }
}

const handleConnect = (connection: Connection) => {
  console.log('Connection created:', connection)
  if(!connection.sourceHandle?.includes('output') || !connection.targetHandle?.includes('input')){
    return
  }
  // 创建新的边，添加箭头标记
  const newEdge = {
    id: generateId(),
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle || undefined,
    targetHandle: connection.targetHandle || undefined,
    animated: false,
    selected: false,
    markerEnd: {
      type: 'arrow',
      color: '#125EFF',
      width: 20,
      height: 20,
    },
    style: {
      stroke: '#125EFF',
      strokeWidth: 2,
    },
  }

  // 使用Vue Flow API添加边
  addEdges([newEdge])
}

const handleNodesChange = (changes: NodeChange[]) => {
  console.log('Nodes changed:', changes)

  // Vue Flow的v-model会自动处理节点变化，我们只需要处理特殊逻辑
  changes.forEach((change) => {
    switch (change.type) {
      case 'select':
        // 节点选择状态变化
        if (change.selected) {
          orchestrationStore.setSelectedNode(change.id)
        }
        else {
          // 取消选择时，如果当前选中的是这个节点，则清除选择
          if (orchestrationStore.selectedNodeId === change.id)
            orchestrationStore.setSelectedNode(null)
        }
        break

      case 'remove':
        // 节点删除 - 由于我们禁用了默认删除键，这里只处理程序化删除
        orchestrationStore.saveToHistory()
        if (orchestrationStore.currentFlow) {
          orchestrationStore.currentFlow.nodes = orchestrationStore.currentFlow.nodes.filter(n => n.id !== change.id)
          // 删除相关的边
          orchestrationStore.currentFlow.edges = orchestrationStore.currentFlow.edges.filter(
            e => e.source !== change.id && e.target !== change.id,
          )
          orchestrationStore.saveCurrentFlow()
        }
        break

      case 'dimensions':
        // 节点尺寸变化 - Vue Flow内部管理，这里只记录日志
        console.log('Node dimensions changed:', change.id, change.dimensions)
        break

      case 'position':
        // 简化的位置变化处理 - 让Vue Flow自己处理连线更新
        if ('dragging' in change && change.dragging !== undefined) {
          isDraggingNode.value = change.dragging
          isDraggingOptimized.value = change.dragging

          if (change.dragging) {
            dragNodeId.value = change.id
            console.log(`[Drag] 开始拖拽节点: ${change.id}`)
          } else {
            dragNodeId.value = null
            console.log(`[Drag] 停止拖拽节点: ${change.id}`)
          }
        }
        break

      case 'add':
        // 节点添加 - 通常由我们的addNodes调用触发，这里可以做额外处理
        console.log('Node added via Vue Flow:', change.item)
      var variableId=generateId()
      var variableObj={
            id:variableId,
            name:'',
            readonly:true,
            type: 'nodeVariable', // 默认为节点变量
            valueType:'string',
            value: "",
            nodeId: change.item.id,
            nodeName: change.item.data.label,
            nodeType: change.item.type,
      }
        if(change.item.type === 'question-classifier'){
          variableObj.name='匹配结果';
          variableObj.id=variableObj.nodeId

        }else if(change.item.type === 'start'){
          variableObj.name='开始输出';
        }else if(change.item.type === 'llm'){
          variableObj.name='文本输出';
        }

        if(change.item.type != 'end'){
          orchestrationStore.addVariable(variableObj)
          let config={
          icon:change.item.data.icon,
          description:change.item.data.description,
          label:change.item.data.label,
          config:{
            outputKey:variableObj.id
          }
        }
        nextTick(()=>{
          orchestrationStore.updateNodeData(change.item.id, config)
        })
        }
        break

      default:
        console.log('Unhandled node change type:', (change as any).type)
    }
  })
}

const handleEdgesChange = (changes: EdgeChange[]) => {
  console.log('Edges changed:', changes)

  // Vue Flow的v-model会自动处理边变化，我们只需要处理特殊逻辑
  changes.forEach((change) => {
    switch (change.type) {
      case 'select':
        // 边选择状态变化 - 目前不需要特殊处理
        console.log('Edge selection changed:', change.id, change.selected)
        break

      case 'remove':
        // 边删除 - 由于我们禁用了默认删除键，这里只处理程序化删除
        orchestrationStore.saveToHistory()
        if (orchestrationStore.currentFlow) {
          orchestrationStore.currentFlow.edges = orchestrationStore.currentFlow.edges.filter(e => e.id !== change.id)
          orchestrationStore.saveCurrentFlow()
        }
        break

      case 'add':
        // 边添加 - 通常由我们的addEdge调用触发
        console.log('Edge added via Vue Flow:', change.item)
        break

      default:
        console.log('Unhandled edge change type:', (change as any).type)
    }
  })
}

const handlePaneReady = (instance: any) => {
  console.log('Pane ready:', instance)
  flowInstance.value = instance

  // 初始化时适应视图
  nextTick(() => {
    if (nodes.value.length > 0)
      instance.fitView({ padding: 0.2 })
  })
}

// 点击画布空白区域
const handlePaneClick = () => {
  // 取消所有边的选中状态
  edges.value.forEach((edge) => {
    (edge as any).selected = false
  })

  // 取消所有节点的选中状态
  nodes.value.forEach((node) => {
    (node as any).selected = false
  })

  // 取消节点选中状态
  orchestrationStore.setSelectedNode(null)

  // 强制触发响应式更新
  edges.value = [...edges.value]
  nodes.value = [...nodes.value]
}

// 显示删除节点确认对话框
const showDeleteNodeConfirmation = (node: any, onConfirm: () => void) => {
  // 只保护开始节点，允许删除结束节点
  if (node.type === 'start') {
    message.warning('开始节点不能删除')
    return
  }

  dialog.warning({
    title: '删除确认',
    content: `确定要删除节点 "${node.data.label}" 吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: onConfirm,
  })
}

// 显示删除连接线确认对话框
const showDeleteEdgeConfirmation = (onConfirm: () => void) => {
  dialog.warning({
    title: '删除确认',
    content: '确定要删除选中的连接线吗？此操作不可撤销。',
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: onConfirm,
  })
}

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  const isContenteditable = event?.target?.classList.contains('editable-div');
  if(isContenteditable){
    return
  }
  if (
    event?.target?.tagName === 'INPUT' ||
    event?.target?.tagName === 'TEXTAREA' ||
    event?.target?.tagName === 'SELECT'
  ) {
    return
  }
  // Delete/Backspace 删除选中的节点或连接线
  if (event.key === 'Delete' || event.key === 'Backspace') {
    event.preventDefault()

    // 检查是否有选中的节点
    const selectedNodes = nodes.value.filter(node => (node as any).selected)
    const selectedEdges = edges.value.filter(edge => (edge as any).selected)

    if (selectedNodes.length > 0) {
      // 处理多个选中节点的删除
      if (selectedNodes.length === 1) {
        showDeleteNodeConfirmation(selectedNodes[0], () => {
          let variable= orchestrationStore.getVariablesByType('nodeVariable').find(item=>item.nodeId===selectedNodes[0].id)
          if(variable){
            orchestrationStore.deleteVariable(variable.id);
          }
          orchestrationStore.removeNode(selectedNodes[0].id)
          message.success('节点删除成功')
        })
      }
      else {
        // 多选节点删除
        const nodeNames = selectedNodes.map(node => node.data.label).join('、')
        dialog.warning({
          title: '删除确认',
          content: `确定要删除选中的 ${selectedNodes.length} 个节点（${nodeNames}）吗？此操作不可撤销。`,
          positiveText: '确定删除',
          negativeText: '取消',
          onPositiveClick: () => {
            selectedNodes.forEach((node) => {
              // 只保护开始节点，允许删除结束节点
              if (node.type !== 'start')
                orchestrationStore.removeNode(node.id)
            })
            message.success(`成功删除 ${selectedNodes.length} 个节点`)
          },
        })
      }
    }
    else if (selectedEdges.length > 0) {
      // 处理连接线删除
      if (selectedEdges.length === 1) {
        showDeleteEdgeConfirmation(() => {
          // 直接从edges数组中删除，避免通过store删除导致的双重处理
          const edgeId = selectedEdges[0].id
          edges.value = edges.value.filter(e => e.id !== edgeId)

          // 同步到store
          if (orchestrationStore.currentFlow) {
            orchestrationStore.saveToHistory()
            orchestrationStore.currentFlow.edges = orchestrationStore.currentFlow.edges.filter(e => e.id !== edgeId)
            orchestrationStore.saveCurrentFlow()
          }

          message.success('连接线删除成功')
        })
      }
      else {
        // 多选连接线删除
        dialog.warning({
          title: '删除确认',
          content: `确定要删除选中的 ${selectedEdges.length} 条连接线吗？此操作不可撤销。`,
          positiveText: '确定删除',
          negativeText: '取消',
          onPositiveClick: () => {
            const edgeIds = selectedEdges.map(edge => edge.id)

            // 直接从edges数组中删除
            edges.value = edges.value.filter(e => !edgeIds.includes(e.id))

            // 同步到store
            if (orchestrationStore.currentFlow) {
              orchestrationStore.saveToHistory()
              orchestrationStore.currentFlow.edges = orchestrationStore.currentFlow.edges.filter(e => !edgeIds.includes(e.id))
              orchestrationStore.saveCurrentFlow()
            }

            message.success(`成功删除 ${selectedEdges.length} 条连接线`)
          },
        })
      }
    }
  }

  // Ctrl+Z 撤销
  if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
    event.preventDefault()
    const success = orchestrationStore.undo()
    if (success)
      console.log('撤销操作成功')
  }

  // Ctrl+Shift+Z 或 Ctrl+Y 重做
  if ((event.ctrlKey && event.shiftKey && event.key === 'Z')
      || (event.ctrlKey && event.key === 'y')) {
    event.preventDefault()
    const success = orchestrationStore.redo()
    if (success)
      console.log('重做操作成功')
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

// 绑定事件监听器
onPaneReady(handlePaneReady)
onNodeClick(handleNodeClick)
onNodeDoubleClick(handleNodeDoubleClick)
onEdgeClick(handleEdgeClick)
// onConnect(handleConnect)
// onNodesChange(handleNodesChange)
onEdgesChange(handleEdgesChange)
</script>

<template>
  <div class="flow-canvas w-full h-full relative">
    <!-- Vue Flow 画布 -->
    <VueFlow
      v-model:nodes="nodes"
      v-model:edges="edges"
      :node-types="nodeTypes"
      :class="{ 'drag-over': isDragOver }"
      :default-zoom="0.75"
      :min-zoom="0.1"
      :max-zoom="2"
      :zoom-on-scroll="true"
      :zoom-on-pinch="true"
      :zoom-on-double-click="false"
      :pan-on-scroll="false"
      :pan-on-scroll-mode="PanOnScrollMode.Free"
      :pan-on-drag="true"
      :selection-key-code="null"
      :multi-selection-key-code="null"
      :delete-key-code="null"
      :fit-view-on-init="false"
      :nodes-draggable="true"
      :nodes-connectable="true"
      :elements-selectable="true"
      :edges-selectable="true"
      :apply-default="true"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @node-click="handleNodeClick"
      @node-double-click="handleNodeDoubleClick"
      @edge-click="handleEdgeClick"
      @connect="handleConnect"
      @nodes-change="handleNodesChange"
      @edges-change="handleEdgesChange"
      @pane-ready="handlePaneReady"
      @pane-click="handlePaneClick"
    >
      <!-- 网格背景 -->
      <Background
        pattern="dots"
        :gap="20"
        :size="1"
        color="#222"
      />

      <!-- 控制面板 -->
      <Controls
        :show-zoom="true"
        :show-fit-view="true"
        :show-interactive="true"
        position="bottom-right"
      />

      <!-- 小地图 -->
      <MiniMap
        :node-color="getNodeColor"
        mask-color="rgba(240, 242, 247, 0.8)"
        position="bottom-left"
        :pannable="true"
        :zoomable="true"
        class="minimap-custom"
      />

      <!-- 空状态提示 -->
      <div
        v-if="nodes.length === 0"
        class="empty-state absolute inset-0 flex items-center justify-center pointer-events-none z-10"
      >
        <div class="text-center bg-white bg-opacity-90 p-8 rounded-xl shadow-lg">
          <div class="text-6xl text-gray-300 mb-4">
            🎨
          </div>
          <h3 class="text-lg font-medium text-gray-500 mb-2">
            开始创建您的智能体流程
          </h3>
          <p class="text-sm text-gray-400 mb-4">
            从左侧拖拽节点到此处开始构建
          </p>
          <div class="text-xs text-gray-400 space-y-1">
            <p>💡 支持的操作：</p>
            <p>• 拖拽节点到画布</p>
            <p>• 连接节点创建流程</p>
            <p>• 双击节点进行配置</p>
            <p>• 使用鼠标滚轮缩放</p>
          </div>
        </div>
      </div>




    </VueFlow>
  </div>
</template>

<style scoped lang="less">
// 导入Vue Flow样式
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
//@import '@vue-flow/background/dist/style.css';
@import '@vue-flow/controls/dist/style.css';
@import '@vue-flow/minimap/dist/style.css';

// 连线样式自定义 - 移除transition避免干扰连线跟随
:global(.vue-flow__edge-path) {
  stroke: #125EFF !important;
  stroke-width: 2px !important;
  // 移除transition，避免干扰连线实时更新
}

:global(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #125EFF !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 8px rgba(18, 94, 255, 0.6)) drop-shadow(0 0 16px rgba(18, 94, 255, 0.4)) !important;
}

:global(.vue-flow__edge-text) {
  fill: #125EFF !important;
  font-size: 12px !important;
  // 移除transition，避免干扰连线实时更新
}

:global(.vue-flow__edge.selected .vue-flow__edge-text) {
  fill: #125EFF !important;
  font-weight: 600 !important;
  filter: drop-shadow(0 0 4px rgba(18, 94, 255, 0.8)) !important;
}

// 箭头样式
:global(.vue-flow__arrowhead) {
  fill: #125EFF !important;
  // 移除transition，避免干扰连线实时更新
}

:global(.vue-flow__edge.selected .vue-flow__arrowhead) {
  fill: #125EFF !important;
  filter: drop-shadow(0 0 6px rgba(18, 94, 255, 0.8)) !important;
}

// hover状态 - 发光效果
:global(.vue-flow__edge:hover .vue-flow__edge-path) {
  stroke: #125EFF !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 6px rgba(18, 94, 255, 0.5)) drop-shadow(0 0 12px rgba(18, 94, 255, 0.3)) !important;
}

:global(.vue-flow__edge:hover .vue-flow__arrowhead) {
  fill: #125EFF !important;
  filter: drop-shadow(0 0 4px rgba(18, 94, 255, 0.6)) !important;
}

:global(.vue-flow__edge:hover .vue-flow__edge-text) {
  fill: #125EFF !important;
  filter: drop-shadow(0 0 3px rgba(18, 94, 255, 0.6)) !important;
}

.flow-canvas {
  position: relative;
  width: 100%;
  height: 100%;

  // Vue Flow容器样式 - 全屏模式
  :deep(.vue-flow) {
    background: #fafbfc;
    border-radius: 0; // 全屏时去掉圆角
    overflow: hidden;
    width: 100vw;
    height: 100vh;

    &.drag-over {
      background: rgba(18, 94, 255, 0.05);
      box-shadow: inset 0 0 0 2px rgba(18, 94, 255, 0.3);
    }
  }

  // 背景样式
  :deep(.vue-flow__background) {
    background-color: #fafbfc;

    .vue-flow__background-pattern {
      color: #e2e8f0;
    }
  }

  // 控制面板样式
  :deep(.vue-flow__controls) {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 4px;

    .vue-flow__controls-button {
      background: transparent;
      border: none;
      color: #64748b;
      transition: all 0.2s ease;
      width: 32px;
      height: 32px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 2px;

      &:hover {
        background: #f1f5f9;
        color: #125EFF;
      }

      &:active {
        background: #e2e8f0;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  // 小地图样式
  :deep(.vue-flow__minimap) {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .vue-flow__minimap-mask {
      fill: rgba(18, 94, 255, 0.1);
      stroke: #125EFF;
      stroke-width: 2;
    }

    .vue-flow__minimap-node {
      fill: #e2e8f0;
      stroke: #94a3b8;
      stroke-width: 1;
    }
  }

  // 节点样式 - 在保持连线跟随的基础上优化拖拽性能
  :deep(.vue-flow__node) {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    // 只对非transform属性应用transition，避免拖拽冲突
    transition: border-color 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;

    &:hover {
      border-color: #125EFF;
      box-shadow: 0 4px 16px rgba(18, 94, 255, 0.2);
    }

    &.selected {
      border-color: #125EFF;
      box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.2);
    }

    // 拖拽时的性能优化 - 不干扰连线跟随
    &.vue-flow__node-dragging {
      transition: none !important;
      // 启用硬件加速提升拖拽流畅度
      will-change: transform;
      // 使用GPU合成层
      transform: translateZ(0);
    }

    .vue-flow__node-default {
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      color: #1e293b;
    }
  }

  // 全局禁用拖拽时的动画效果，提升性能
  :global(.vue-flow__node.vue-flow__node-dragging) {
    * {
      transition: none !important;
      animation: none !important;
    }

    // 拖拽时的性能优化
    cursor: grabbing !important;
    user-select: none;
    pointer-events: none; // 避免子元素干扰拖拽
  }

  // 拖拽时优化画布性能
  &:has(.vue-flow__node-dragging) {
    // 拖拽时暂时降低画布质量以提升性能
    image-rendering: optimizeSpeed;
    shape-rendering: optimizeSpeed;
  }



  // 边样式 - 移除transition避免干扰连线跟随
  :deep(.vue-flow__edge) {
    .vue-flow__edge-path {
      stroke: #64748b;
      stroke-width: 2;
      // 移除transition，避免干扰连线实时更新
    }

    &:hover .vue-flow__edge-path {
      stroke: #125EFF;
      stroke-width: 3;
    }

    &.selected .vue-flow__edge-path {
      stroke: #125EFF;
      stroke-width: 3;
    }

    .vue-flow__edge-text {
      font-size: 12px;
      fill: #64748b;
    }

    // 箭头标记样式
    marker {
      fill: #64748b;
      stroke: #64748b;
    }

    &:hover marker,
    &.selected marker {
      fill: #125EFF;
      stroke: #125EFF;
    }
  }

  // 连接线样式
  :deep(.vue-flow__connection-line) {
    stroke: #125EFF;
    stroke-width: 2;
    stroke-dasharray: 5, 5;
  }

  // 选择框样式
  :deep(.vue-flow__selection) {
    background: rgba(18, 94, 255, 0.1);
    border: 1px solid #125EFF;
  }

  // 空状态样式
  .empty-state {
    pointer-events: none;
    z-index: 10;

    > div {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(8px);
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
    }
  }



  // 自定义小地图样式
  .minimap-custom {
    :deep(.vue-flow__minimap-node) {
      &[data-type="start"] {
        fill: #52c41a;
      }

      &[data-type="end"] {
        fill: #ff4d4f;
      }

      &[data-type="llm"] {
        fill: #1890ff;
      }

      &[data-type="api"] {
        fill: #722ed1;
      }

      &[data-type="condition"] {
        fill: #fa8c16;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .flow-canvas {
    :deep(.vue-flow__controls) {
      bottom: 10px;
      right: 10px;

      .vue-flow__controls-button {
        width: 36px;
        height: 36px;
      }
    }

    :deep(.vue-flow__minimap) {
      width: 120px;
      height: 80px;
      bottom: 10px;
      left: 10px;
    }

    .empty-state > div {
      padding: 24px;
      margin: 20px;

      .text-6xl {
        font-size: 2rem;
      }

      h3 {
        font-size: 16px;
      }

      p {
        font-size: 12px;
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .flow-canvas {
    :deep(.vue-flow) {
      background: #1a1a1a;
    }

    :deep(.vue-flow__background) {
      background-color: #1a1a1a;

      .vue-flow__background-pattern {
        color: #404040;
      }
    }

    :deep(.vue-flow__node) {
      background: #2a2a2a;
      border-color: #404040;
      color: #ffffff;

      &:hover {
        border-color: #125EFF;
      }
    }

    :deep(.vue-flow__controls) {
      background: rgba(42, 42, 42, 0.95);
      border-color: #404040;

      .vue-flow__controls-button {
        color: #cccccc;

        &:hover {
          background: #404040;
          color: #125EFF;
        }
      }
    }

    :deep(.vue-flow__minimap) {
      background: rgba(42, 42, 42, 0.95);
      border-color: #404040;
    }

    .empty-state > div {
      background: rgba(42, 42, 42, 0.95);
      border-color: #404040;
      color: #ffffff;
    }

    .performance-monitor {
      background: rgba(42, 42, 42, 0.9);
      border-color: rgba(255, 255, 255, 0.2);
      color: #ffffff;
    }
  }
}

// 性能监控样式
.performance-monitor {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  user-select: none;
  pointer-events: none;

  // 确保性能监控不影响拖拽性能
  will-change: auto;
  contain: layout style paint;

  // 动画效果
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>
