<script setup>
import { h, onMounted, ref } from "vue";
import {
  NButton,
  NDataTable,
  NDatePicker,
  NInput,
  NProgress,
  NSelect,
  useDialog,
  useMessage,
  NSpin,
} from "naive-ui";
import { useRoute, useRouter } from "vue-router";
import {
  deletefileApi,
  downloadfileApi,
  getfilelistApi,
  graphbuildingApi,
  parsefileApi,
} from "@/api/knowledgeFactory";
const router = useRouter();
const route = useRoute();
const dialog = useDialog();
const message = useMessage();

const id = ref("");
const searchFor = ref({
  fileName: "",
  fileType: "",
  handleStatus: "",
  beginDate: "",
  endDate: "",
});
const loading = ref(false);
const timeDate = ref(null);
function settimefun(value) {
  if (value && value.length === 2) {
    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    };
    searchFor.value.beginDate = formatDate(value[0]);
    searchFor.value.endDate = formatDate(value[1]);
  } else {
    searchFor.value.beginDate = "";
    searchFor.value.endDate = "";
  }
}
const fileList = ref([]);
const checkedRowKeys = ref([]);
const paginationdata = ref({
  pageNum: 1,
  pageSize: 5,
  pagecount: 0,
});
function handleCheck(rowKeys) {
  checkedRowKeys.value = rowKeys;
  console.log(rowKeys);
}
function rowKey(row) {
  return row.id;
}
const fileTypeoptions = ref([
  { label: "请选择", value: "" },
  { label: "pdf", value: "pdf" },
  { label: "docx", value: "docx" },
  { label: "doc", value: "doc" },
  { label: "pptx", value: "pptx" },
  { label: "ppt", value: "ppt" },
  { label: "xlsx", value: "xlsx" },
  { label: "xls", value: "xls" },
  { label: "csv", value: "csv" },
  { label: "txt", value: "txt" },
  { label: "jpg", value: "jpg" },
  { label: "jpeg", value: "jpeg" },
  { label: "png", value: "png" },
  { label: "json", value: "json" },
  { label: "xml", value: "xml" },
]);
const handleStatusoptions = ref([
  { label: "请选择", value: "" },
  { label: "未解析", value: "NOPARSING" },
  { label: "排队中", value: "QUEUE" },
  { label: "解析中", value: "RUNNING" },
  { label: "解析成功", value: "SUCCESS" },
  { label: "解析失败", value: "FAILED" },
]);
const filecolumns = [
  {
    type: "selection",
  },
  {
    title: "序号",
    key: "key",
    align: "center",
    render: (_, index) => {
      return `${index + 1}`;
    },
  },
  {
    title: "文件名称",
    key: "fileName",
    align: "center",
  },
  {
    title: "文件类型",
    key: "fileType",
    align: "center",
  },
  {
    title: "上传时间",
    key: "uploadDate",
    align: "center",
  },
  {
    title: "文件描述",
    key: "fileDesc",
    align: "center",
  },
  {
    title: "解析状态",
    key: "handleStatus",
    align: "center",
    render(row) {
      return h(
        NProgress,
        {
          type: "line",
          tertiary: true,
          percentage:
            row.handleStatus == "SUCCESS"
              ? 100
              : row.handleStatus == "RUNNING"
              ? row.fileParsePercent
              : 0,
          indicatorPlacement: "inside",
          processing: true,
        },
        {
          default: () =>
            row.handleStatus == "SUCCESS"
              ? 100
              : row.handleStatus == "RUNNING"
              ? row.fileParsePercent
              : 0,
        }
      );
    },
  },
  {
    title: "文件大小",
    key: "fileSize",
    align: "center",
    render: (row) => {
      return `${(row.fileSize / 1024).toFixed(2)}KB`;
    },
  },
  {
    title: "预览次数",
    key: "visitTimes",
    align: "center",
  },
  {
    title: "下载次数",
    key: "downloadTimes",
    align: "center",
  },
  {
    title: "检索次数",
    key: "searchTimes",
    align: "center",
  },
  {
    title: "上传人",
    key: "userName",
    align: "center",
  },
  {
    title: "上传部门",
    key: "departName",
    align: "center",
  },
  {
    title: "操作",
    key: "actions",
    align: "center",
    fixed: "right",
    render(row) {
      return [
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: "small",
            class: "btn previewbtn",

            onClick: () => parsebtnfun([row.id]),
          },
          { default: () => "解析" }
        ),
        // h(
        //   NButton,
        //   {
        //     strong: true,
        //     tertiary: true,
        //     size: 'small',
        //     class:'btn previewbtn',

        //     onClick: () => graphbuildbtnfun([row.id])
        //   },
        //   { default: () => '图谱构建' }
        // ),
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: "small",
            class: "btn uploadbtn",

            onClick: () => downloadbtnfun(row),
          },
          { default: () => "下载" }
        ),
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: "small",
            class: "btn delbtn",
            onClick: () => delbtnfun([row.id]),
          },
          { default: () => "删除" }
        ),
      ];
    },
  },
];
const filedata = ref([]);

function graphbuildbtnfun(rowarr) {
  if (rowarr.length == 0) {
    message.warning("请选择要构建图谱的文件");
    return;
  }
  const ids = rowarr.join(",");
  loading.value = true;
  graphbuildingApi({ ids })
    .then((res) => {
      console.log(res);
      if (res.code == "0") {
        message.success("构建成功");
        checkedRowKeys.value = [];
        getfilelistfun();
      } else {
        message.error(res.message);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function parsebtnfun(rowarr) {
  if (rowarr.length == 0) {
    message.warning("请选择要解析的文件");
    return;
  }
  const ids = rowarr.join(",");
  console.log({ ids });
  loading.value = true;
  parsefileApi({ ids })
    .then((res) => {
      console.log(res);
      if (res.code == "0") {
        message.success("解析成功");
        checkedRowKeys.value = [];
        getfilelistfun();
      } else {
        message.error(res.message);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
function delbtnfun(rowarr) {
  if (rowarr.length == 0) {
    message.warning("请选择要删除的文件");
    return;
  }
  const ids = rowarr.join(",");
  dialog.warning({
    title: "删除确认",
    content: "确定要删除该文件吗？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      loading.value = true;
      deletefun({ ids });
    },
  });
}
function deletefun(params) {
  deletefileApi(params)
    .then((res) => {
      console.log(res);
      if (res.code == "0") {
        message.success("删除成功");
        checkedRowKeys.value = [];
        paginationdata.value.pageNum = 1;
        getfilelistfun();
      } else {
        message.error(res.message);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
function downloadbtnfun(row) {
  loading.value = true;
  downloadfileApi({ filePath: row.filePath, storeId: row.id })
    .then((res) => {
      // 从响应中获取文件名
      let filename = row.fileName; // 默认使用行数据中的文件名

      // 如果响应头中有Content-Disposition，尝试从中提取文件名
      const contentDisposition = res.headers?.["content-disposition"] || "";
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
        if (filenameMatch && filenameMatch[1]) filename = filenameMatch[1];
      }

      // 创建Blob对象
      const blob = new Blob([res.data], { type: "application/octet-stream" });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;

      // 添加到DOM，触发点击，然后移除
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 释放URL对象
      window.URL.revokeObjectURL(url);

      message.success("文件下载成功");
      getfilelistfun();
    })
    .catch((error) => {
      message.error(`文件下载失败：${error.message}`);
    })
    .finally(() => {
      loading.value = false;
    });
}
function jumpPage(url, parameter) {
  if (url) {
    router.push({
      path: url,
      query: {
        ...parameter,
      },
    });
  } else {
    router.push("/knowledgeFactoryPage");
  }
}
function searchfun() {
  console.log(searchFor.value);
  getfilelistfun();
}
function resetsearchfun() {
  searchFor.value = {
    fileName: "",
    fileType: "",
    handleStatus: "",
    beginDate: "",
    endDate: "",
  };
  timeDate.value = null;
  paginationdata.value.pageNum = 1;
  getfilelistfun();
}
function getfilelistfun() {
  const params = {
    storeId: id.value,
    pageNum: paginationdata.value.pageNum,
    pageSize: paginationdata.value.pageSize,
  };

  // 只添加有值的搜索条件
  Object.keys(searchFor.value).forEach((key) => {
    if (searchFor.value[key]) params[key] = searchFor.value[key];
  });
  loading.value = true;
  getfilelistApi(params)
    .then((res) => {
      console.log(res);
      if (res.code == "0") {
        filedata.value = res.data.result.records;
        paginationdata.value.pagecount = res.data.result.pages;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
onMounted(() => {
  const params = route.query;
  id.value = params.id;
  getfilelistfun();
});
</script>

<template>
  <NSpin :show="loading">
    <div class="app">
      <header class="headers">
        <div class="left">
          <div class="gohome" @click="jumpPage()">
            <img src="@/assets/workShopPage/leftarrow.png" />
          </div>
          知识库工厂 /<span>知识文件管理</span>
        </div>
        <div class="headbtnrow">
          <NButton @click="jumpPage()"> 取消 </NButton>
          <NButton type="primary" @click="jumpPage('/knowledgeFactoryPage')">
            保存
          </NButton>
        </div>
      </header>
      <div class="setconbg">
        <div class="biaodan">
          <span>文件名称 : </span>
          <div class="inputsetwidth">
            <NInput
              v-model:value="searchFor.fileName"
              type="text"
              placeholder="请输入文件名称"
            />
          </div>
          <span>文件类型 : </span>
          <div class="inputsetwidth">
            <NSelect
              v-model:value="searchFor.fileType"
              :options="fileTypeoptions"
            />
          </div>
          <span>解析状态 : </span>
          <div class="inputsetwidth">
            <NSelect
              v-model:value="searchFor.handleStatus"
              :options="handleStatusoptions"
            />
          </div>
          <span>上传日期 : </span>
          <div class="inputsetwidth">
            <NDatePicker
              v-model:value="timeDate"
              type="daterange"
              placeholder="请选择日期"
              clearable
              :on-confirm="settimefun"
            />
          </div>
          <NButton type="primary" @click="searchfun">
            <img
              class="setimg"
              src="@/assets/knowledgeFactoryPage/sousuo.png"
            />搜索
          </NButton>
          <NButton @click="resetsearchfun">
            <img
              class="setimg"
              src="@/assets/knowledgeFactoryPage/chongzhi.png"
            />重置
          </NButton>
        </div>
        <div class="btnrow">
          <NButton type="primary" @click="jumpPage('/fileUploadPage', { id })">
            <img src="@/assets/knowledgeFactoryPage/shangchuan.png" />
            文件上传
          </NButton>
          <NButton type="primary" @click="parsebtnfun(checkedRowKeys)">
            <img src="@/assets/knowledgeFactoryPage/fenxi.png" />批量解析
          </NButton>
          <!-- <NButton type="primary" @click="graphbuildbtnfun(checkedRowKeys)"><img src="@/assets/knowledgeFactoryPage/goujian.png">批量构建知识图谱</NButton> -->
          <NButton type="primary" @click="delbtnfun(checkedRowKeys)">
            <img src="@/assets/knowledgeFactoryPage/shanchu.png" />批量删除
          </NButton>
        </div>
        <div style="margin-top: 24px">
          <NDataTable
            :columns="filecolumns"
            :data="filedata"
            :bordered="false"
            :row-key="rowKey"
            :scroll-x="1800"
            :checked-row-keys="checkedRowKeys"
            striped
            :pagination="paginationdata"
            @update:checked-row-keys="handleCheck"
          />
        </div>
      </div>
    </div>
  </NSpin>
</template>

<style scoped lang="less">
.app {
  background: url("@/assets/topbg.png") no-repeat;
  background-size: 90% 220px;
  background-position-x: 5%;
}
.app {
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 30px;
}
.red {
  color: #ff5f5f;
  margin-right: 4px;
}
.headers {
  height: 40px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;

  .left {
    color: #323233;
    font-size: 20px;
    font-weight: 500;
    line-height: 0;
    letter-spacing: 0;
    line-height: 40px;
    display: flex;
    align-items: center;
    .gohome {
      width: 40px;
      height: 40px;
      background: #fafbff;
      border: 1px solid #e9ecf3;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 11px;
      img {
        width: 20px;
        height: 20px;
      }
    }
    span {
      color: #909399;
      margin-left: 8px;
    }
  }
  .headbtnrow {
    button {
      width: 80px;
      height: 40px;
      border-radius: 10px;
      margin-left: 16px;
    }
  }
}
.setconbg {
  padding-top: 90px;
  padding-left: 24px;
  padding-right: 24px;
  background: url("@/assets/conbg.png") no-repeat;
  background-size: 100% 209px;
}

.rowtit {
  height: 21px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 15px;
  color: #323233;
  letter-spacing: 0;
  display: flex;
  align-items: center;
  margin-bottom: 9px;
}
.inputset-38 {
  height: 38px;
  /deep/ .n-input__input-el {
    height: 38px;
  }
}
.inputset-120 {
  height: 120px;
  /deep/ .n-input__input-el {
    height: 120px;
  }
}
.uploadbox {
  height: 247px;
  background-image: linear-gradient(90deg, #e7f9ff 0%, #f5f5ff 100%);
  border: 1px solid #125eff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  padding-top: 70px;
  img {
    width: 83px;
    height: 83px;
  }
  .uploadcon {
    width: 456px;
    text-align: left;
    .uptitle {
      height: 25px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      font-size: 18px;
      color: #125eff;
      letter-spacing: 0;
      line-height: 25px;
      margin-bottom: 2px;
    }
    .upcon {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #464646;
      letter-spacing: 0;
      line-height: 20px;
    }
  }
}
.n-upload-dragger {
  padding: 0;
}
.n-divider:not(.n-divider--vertical) {
  margin-top: 12px;
  margin-bottom: 8.5px;
}
/deep/.btn {
  height: 20px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #125eff;
  text-align: center;
  // margin-left: 10px;
  margin-right: 10px;
  // background-color: #FFF;
}
/deep/.delbtn {
  color: #ff5f5f;
}
.btnrow {
  margin-top: 36px;
  button {
    height: 40px;
    border-radius: 10px;
    margin-right: 16px;
    img {
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
  }
}
.inputsetwidth {
  width: 224px;
  // height: 40px;
  background: #ffffff;
  border-radius: 8px;
  margin-right: 36px;
  .n-input {
    // height: 40px;
    /deep/ input {
      // height: 40px;
    }
  }
}
.setimg {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.biaodan {
  display: flex;
  align-items: center;
  span {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000cc;
    line-height: 40px;
    margin-right: 10px;
  }
}
.gohome:hover {
  cursor: pointer;
}
/deep/.n-progress
  .n-progress-graph
  .n-progress-graph-line.n-progress-graph-line--indicator-inside
  .n-progress-graph-line-rail
  .n-progress-graph-line-indicator {
  text-align: center;
}
</style>
