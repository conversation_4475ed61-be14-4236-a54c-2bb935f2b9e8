/**
 * 智能体执行预览弹框相关的类型定义
 */

// 节点执行状态
export type NodeStatus = 'pending' | 'running' | 'completed' | 'error'

// 文件信息接口
export interface FileInfo {
  name: string
  url: string
  type: string
  size?: number
  lastModified?: number
}

// 执行节点接口
export interface ExecutionNode {
  id: string
  label: string
  description?: string
  details?: string[]
  status: NodeStatus
  icon?: string
  outputFile?: FileInfo
  startTime?: number
  endTime?: number
  duration?: number
  errorMessage?: string
}

// 执行配置接口
export interface ExecutionConfig {
  title?: string
  allowClose?: boolean
  showStopButton?: boolean
  autoClose?: boolean
  autoCloseDelay?: number
}

// 执行统计信息接口
export interface ExecutionStats {
  totalNodes: number
  completedNodes: number
  failedNodes: number
  runningNodes: number
  pendingNodes: number
  totalDuration?: number
  startTime?: number
  endTime?: number
}

// 执行事件接口
export interface ExecutionEvents {
  onStart?: () => void
  onNodeStart?: (node: ExecutionNode) => void
  onNodeComplete?: (node: ExecutionNode) => void
  onNodeError?: (node: ExecutionNode, error: string) => void
  onComplete?: () => void
  onError?: (error: string) => void
  onStop?: () => void
  onClose?: () => void
}

// 常量定义
export const NODE_STATUS_COLORS = {
  pending: '#d1d5db',
  running: '#f59e0b',
  completed: '#10b981',
  error: '#ef4444',
} as const

// 默认配置
export const DEFAULT_EXECUTION_CONFIG: ExecutionConfig = {
  title: '正在生成答案',
  allowClose: true,
  showStopButton: true,
  autoClose: false,
  autoCloseDelay: 3000,
}

// 工具函数
export const createExecutionNode = (
  id: string,
  label: string,
  options?: Partial<ExecutionNode>
): ExecutionNode => ({
  id,
  label,
  status: 'pending',
  ...options,
})

export const calculateProgress = (nodes: ExecutionNode[]): number => {
  if (nodes.length === 0) return 0
  const completed = nodes.filter(node => node.status === 'completed').length
  return Math.round((completed / nodes.length) * 100)
}

export const isExecutionComplete = (nodes: ExecutionNode[]): boolean => {
  return nodes.length > 0 && nodes.every(node => 
    node.status === 'completed' || node.status === 'error'
  )
}

export const isExecutionRunning = (nodes: ExecutionNode[]): boolean => {
  return nodes.some(node => node.status === 'running')
}

export const getExecutionDuration = (nodes: ExecutionNode[]): number => {
  const startTimes = nodes
    .filter(node => node.startTime)
    .map(node => node.startTime!)
  const endTimes = nodes
    .filter(node => node.endTime)
    .map(node => node.endTime!)
  
  if (startTimes.length === 0) return 0
  
  const startTime = Math.min(...startTimes)
  const endTime = endTimes.length > 0 ? Math.max(...endTimes) : Date.now()
  
  return endTime - startTime
}

export const formatDuration = (duration: number): string => {
  if (duration < 1000) return `${duration}ms`
  if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`
  return `${Math.floor(duration / 60000)}m ${Math.floor((duration % 60000) / 1000)}s`
}
