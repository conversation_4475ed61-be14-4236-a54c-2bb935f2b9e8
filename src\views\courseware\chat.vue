<script lang="ts" setup>
import {computed, h, nextTick, onMounted, ref, useTemplateRef} from "vue";
import {useRouter, useRoute} from "vue-router";
import {
	NSpin,
	NStep,
	NSteps,
	NEllipsis,
	NDropdown,
	NButton,
	NBadge,
	NTabs,
	NTabPane,
	NPopover,
	NBreadcrumb,
	NBreadcrumbItem,
	NEmpty,
	useMessage,
} from "naive-ui";
import markPdf from "@/views/chat/components/mark/index-pdf.vue";
import NestedList from "./components/ExpandableList.vue";
import chart from "@/views/chat/chat.vue";
import lesson from "@/views/chat/components/lesson/index.vue";
import {useToolsStore, usestretchoutStore} from "@/store";
import {infoStore} from "@/store/modules/info";
import {SvgIcon} from "@/components/common";
import nd from "@/assets/chat/nd.png";
import zd from "@/assets/chat/zd.png";
import kj from "@/assets/toolboxPage/kj.png";
import sp from "@/assets/toolboxPage/sp.png";
import jc from "@/assets/toolboxPage/jc.png";
import kja from "@/assets/toolboxPage/kja.png";
import spa from "@/assets/toolboxPage/spa.png";
import jca from "@/assets/toolboxPage/jca.png";
import questions from "./components/questions.vue";
import {
	addTextbookAnnotations,
	editTextbookAnnotations,
	edlTextbookAnnotations,
	textbookById,
	knowledge,
	agentById,
} from "@/api/courseware";
import {addConversation} from "@/api/workShop";

const message = useMessage();
const router = useRouter();
const route = useRoute();
const info = infoStore();
const ToolsStore = useToolsStore();
const useTretchOut = usestretchoutStore();
const uid = route.query.id;
const main = ref({});

const markPdfRef = useTemplateRef("markPdfRef");

const breadcrumb = computed(() => {
	return info.breadcrumb;
});

function jumpPage(url: any) {
	console.log(url, "url");
	if (url) router.push(url);
	else router.push("/courseware");
	// else router.go(-1); // 这里改为直接跳转到课程页面
}

const show = ref(false);
const loadTap = (flag: any) => {
	show.value = flag;
};

const StepsList = ref([
	{
		name: "课件材料上传",
	},
	{
		name: "材料格式转换",
	},
	{
		name: "材料内容识别理解",
	},
	{
		name: "知识点解析抽取",
	},
	{
		name: "知识点标记展示",
	},
]);
const renderMenuIcon = (url: any) => {
	return () =>
		h("img", {
			src: url,
			width: 12,
			height: 12,
			style: {verticalAlign: "middle", marginLeft: "4px"}, // 增加样式使其对齐
		});
};

// 标记
const options = ref([]);
// 添加标记
const noteAdded = async (data: any) => {
	console.log(data);
	// let res = await addTextbookAnnotations({
	//   ...data,
	//   textbookId: main.value.id,
	// });
	// console.log(res);
	await init();
};
// 删除标记
const deleteTap = async (id: string) => {
	await edlTextbookAnnotations(id);
	message.success("删除成功");
	await init();
};
// 编辑标记
const editTap = async (data: any) => {
	await editTextbookAnnotations(data.id, {
		textbookId: main.value.id,
		note: data.note,
		page: data.page,
		color: data.color,
		content: data.content,
	});
	message.success("编辑成功");
	await init();
};
// 点击标记列表
const handleSelect = (val: any) => {
	console.log(val);
	markPdfRef.value.openAnnotationById(val);
};

// 核心知识
const contentList = ref([]);

//点击定位原文
const positionTap = (item: any) => {
	console.log(item.position);
	if (item?.position) {
		markPdfRef.value.goPage(Number(item.position));
	}
};

// 点击核心知识图标获取下拉
const popoverShow = async (flag: boolean, item: any) => {
	console.log(flag, item);
	if (flag) {
		item.childrenFlag = false;
		const idsStr = info.breadcrumb.map((item: any) => item.id).join(",");
		let res = await knowledge({content: item.content, textbookId: idsStr});
		item.childrenList = res.data;
		item.childrenFlag = true;
		console.log(item.childrenList);
	}
};

// 点击核心知识下拉
const goTap = (item: any) => {
	console.log(item);
	if (breadcrumb.value?.length >= 3) {
		message.warning("当前仅支持 3 级材料拓展哦，请专注于当前学习~");
		return;
	}
	let arr = info.breadcrumb;
	arr.push({id: item.id, title: item.title, textbookType: item.textbookType});
	info.setBreadcrumb(arr);
	if (item.textbookType == 2) {
		router.push({
			path: "/videoStudyHelperChat",
			query: {id: item.id},
		});
	} else {
		router.push({
			path: "/coursewareChat",
			query: {id: item.id},
		});
	}
};

// 点击面包屑
const breadcrumbTap = (item: any, index: any) => {
	let arr = info.breadcrumb;
	arr.splice(index + 1);
	info.setBreadcrumb(arr);
	if (item.textbookType == 2) {
		router.push({
			path: "/videoStudyHelperChat",
			query: {id: item.id},
		});
	} else {
		router.push({
			path: "/coursewareChat",
			query: {id: item.id},
		});
	}

};

// 暂存会话信息
let dialogue = ref({});
let tabsValue = ref("0");
const questionsRef: any = useTemplateRef("questionsRef");

// 课件跳转到最后一页
const maxPageChange = async () => {
	// if (tabsValue.value !== '1') {
	tabsValue.value = "1";
	await tabsTap("1", true);
	let res = await agentById("1950834085600997378");
	let obj = {
		summary: main.value.summary,
		id: main.value.id,
		agent: {
			modelId: res.data.modelId,
			modelTemp: res.data.modelTemp,
			maxLength: res.data.maxLength,
		},
	};
	await nextTick();
	// 清空聊天内容
	await questionsRef.value.clearChat();
	await questionsRef.value.generateProblem(obj);
	// }
};

// tabs改变  新建会话
const tabsTap = async (val: any, flag?: boolean) => {
	console.log(val);
	tabsValue.value = val;
	// 获取并输出当前页PDF文字内容
	try {
		if (markPdfRef.value && markPdfRef.value.getCurrentPageTextContent) {
			const currentPageText = markPdfRef.value.getCurrentPageTextContent();
			if (currentPageText) {
				console.log("当前页PDF文字内容:", currentPageText);
				console.log("当前页码:", markPdfRef.value.currentPage);
			}
		}
	} catch (error) {
		console.error("获取PDF文字内容失败:", error);
	}

	if (val === "1") {
		const data = await addConversation({
			agentId: "1950834085600997378",
			title: "111",
			category: "1",
		});
		dialogue.value = data.data;
		await nextTick();

		// 如果是翻到教材最后一页 则无需获取预制问题
		if (!flag) {
			await questionsRef.value.textLength();
		}
	}
};

const init = async () => {
	let res = await textbookById(uid);
	main.value = res.data;

	options.value = main.value.textbookAnnotationsList;
	options.value = options.value.map((item: any) => {
		try {
			// 尝试解析position，增加错误处理防止JSON格式错误
			const parsedPosition = JSON.parse(item.position);
			return {
				...item,
				position: parsedPosition,
				text: item.content,
				title:
					item.content?.length > 50
						? item.content.substring(0, 50) + "..."
						: item.content,
			};
		} catch (error) {
			console.error(`解析position失败 for item ${item.id}:`, error);
			// 解析失败时可以选择保留原始值或做其他处理
			return {
				...item,
			};
		}
	});

	if (main.value.textbookType === 0) {
		contentList.value = main.value.textbookTreeNode;
	} else {
		contentList.value = main.value.textbookKnowledgeList;
	}
	console.log(contentList.value);
	await markPdfRef.value.init(main.value.copyUrl);
	console.log(main.value);
};

// ai解释
const aiExplainRequested = async (data: any) => {
	console.log(data);
	tabsValue.value = "1";
	await tabsTap("1");
	await questionsRef.value.textConversation(
		{
			question: data.text + "的概念定义是什么",
			conversationId: dialogue.value.id,
			category: "0",
			agentId: "1950834085600997378",
		},
		data
	);
};

onMounted(async () => {
	await init();
	// console.log(info.breadcrumb)
	// info.setBreadcrumb([{id: uid, title: main.value.title}])
});
</script>

<template>
	<NSpin :show="show">
		<template #description> 正在生成PDF，请稍候...</template>
		<div class="chatbg h-full w-full bg-[#F7F9FF]">
			<header class="headers relative pr-[30px] items-center">
				<div class="left">
					<div class="gohome cursor-pointer" @click="jumpPage('/courseware')">
						<img src="@/assets/workShopPage/leftarrow.png"/>
					</div>
					{{ ToolsStore.ToolInfo.name }}
				</div>
				<n-breadcrumb>
					<n-breadcrumb-item
						v-for="(item, index) in breadcrumb"
						:key="index"
						@click="breadcrumbTap(item, index)"
					>
						{{ item.title }}
					</n-breadcrumb-item>
				</n-breadcrumb>
			</header>
			<div class="flex justify-between px-4 mt-4">
				<div class="h-full documentbg pt-4">
					<div class="flex justify-between items-center ml-[24px]">
						<NEllipsis
							style="max-width: 380px; font-size: 20px; font-weight: 500"
						>
							{{ main?.title }}
						</NEllipsis>
						<!-- 演示环境 暂时调整（隐藏智能标记、考点难点重点） -->
						<div class="flex items-center">
							<div class="flex items-center mr-[18px]">
								<div class="flex items-center mr-[18px]">
									<img :src="zd" alt="" class="w-[18px] mr-[6px]"/>
									<p class="text-sm">知识点</p>
								</div>
								<div class="flex items-center mr-[18px]">
									<img :src="nd" alt="" class="w-[18px] mr-[6px]"/>
									<p class="text-sm">疑难标记</p>
								</div>
								<n-dropdown
									:options="options"
									key-field="id"
									label-field="title"
									trigger="hover"
									@select="handleSelect"
								>
									<!--									<n-button>找个地方休息</n-button>-->
									<n-badge :value="options?.length">
										<div
											class="w-[106px] h-[36px] bg-[#125eff0d] rounded-[10px] flex items-center justify-center cursor-pointer"
										>
											<img
												alt=""
												class="w-4 h-4"
												src="@/assets/toolboxPage/bj.png"
											/>
											<p class="text-[14px] text-[#125EFF] ml-2">标记</p>
											<div class="w-[1px] h-[17px] bg-[#E3E3E3] mx-2"></div>
											<img
												alt=""
												class="w-4 h-4"
												src="@/assets/toolboxPage/bot.png"
											/>
										</div>
									</n-badge>
								</n-dropdown>
							</div>
						</div>
					</div>

					<markPdf
						ref="markPdfRef"
						:enableLog="true"
						:external-annotations="options"
						:main="main"
						:pdfUrl="main?.copyUrl"
						@aiExplainRequested="aiExplainRequested"
						@delete="deleteTap"
						@edit="editTap"
						@maxPageChange="maxPageChange"
						@noteAdded="noteAdded"
					/>
				</div>

				<div class="w-[35%] full-height">
					<n-tabs
						:value="tabsValue"
						animated
						type="segment"
						@update:value="tabsTap"
					>
						<n-tab-pane name="0" tab="导学">
							<div v-if="main.textbookType !== 0" class="">
								<div class="flex items-center mt-[14px]">
									<img
										alt=""
										class="w-[18px] h-[12px]"
										src="@/assets/toolboxPage/tit-minicon.png"
									/>
									<p class="ml-[6px] text-[#3C3D48] font-medium">摘要总结</p>
								</div>
								<div
									class="bg-[#F1F4FE] p-[18px] w-full h-[156px] overflow-y-scroll rounded-[8px] mt-[5px]"
								>
									{{ main?.summary }}
								</div>
								<div class="flex items-center mt-[50px] mb-[5px]">
									<img
										alt=""
										class="w-[18px] h-[12px]"
										src="@/assets/toolboxPage/tit-minicon.png"
									/>
									<p class="ml-[6px] text-[#3C3D48] font-medium">核心知识</p>
								</div>

								<div class="contentList overflow-y-scroll">
									<div
										v-for="(item, index) in contentList"
										:key="index"
										class=""
										@click="positionTap(item)"
									>
										<n-popover trigger="hover">
											<template #trigger>
												<div
													class="bg-[#F1F4FE] w-full mb-[16px] px-[18px] py-[26px] cursor-pointer rounded-[8px] relative center-item"
												>
													<p class="text-[14px] font-semibold">
														{{ item.content }}
													</p>
													<p class="text-[14px]">
														{{ item.description }}
													</p>

													<!--											<n-dropdown :options="[]" trigger="click" @select="handleSelect">-->
													<!--												<img alt="" class="w-4 h-4 absolute right-[16px] top-[14px] invisible center-r"-->
													<!--														 src="@/assets/toolboxPage/center-r.png">-->
													<!--											</n-dropdown>-->
													<n-popover
														placement="left"
														trigger="click"
														@update:show="(flag:boolean)=>popoverShow(flag,item)"
													>
														<template #trigger>
															<img
																alt=""
																class="w-4 h-4 absolute right-[16px] top-[14px] invisible center-r"
																src="@/assets/toolboxPage/center-r.png"
															/>
														</template>
														<div
															v-if="item.childrenList?.length"
															class="flex flex-col"
														>
															<n-button
																v-for="(i, dex) in item.childrenList"
																:key="dex"
																class="children"
																text
																@click="goTap(i)"
															>
																<template #icon>
																	<img
																		:src="
                                      i.textbookType == 0
                                        ? jc
                                        : i.textbookType == 1
                                        ? kj
                                        : sp
                                    "
																		alt=""
																		class="children-img"
																	/>
																	<img
																		:src="
                                      i.textbookType == 0
                                        ? jca
                                        : i.textbookType == 1
                                        ? kja
                                        : spa
                                    "
																		alt=""
																		class="children-imga"
																	/>
																</template>
																{{ i.title }}
															</n-button>
														</div>
														<div v-else class="">
															<n-spin v-if="!item.childrenFlag" size="medium"/>
															<!--														<div  class="">暂无数据</div>-->
															<n-empty v-else description="暂无数据"></n-empty>
														</div>
													</n-popover>
												</div>
											</template>
											<span>定位原文</span>
										</n-popover>
									</div>
								</div>
							</div>
							<div v-else class="">
								<div class="flex items-center mb-[5px]">
									<img
										alt=""
										class="w-[18px] h-[12px]"
										src="@/assets/toolboxPage/tit-minicon.png"
									/>
									<p class="ml-[6px] text-[#3C3D48] font-medium">核心知识</p>
								</div>
								<div class="full-jiaocai overflow-y-scroll">
									<NestedList
										:items="contentList"
										:jc="jc"
										:jca="jca"
										:kj="kj"
										:kja="kja"
										:sp="sp"
										:spa="spa"
										@goTap="goTap"
										@popoverShow="(flag, item) => popoverShow(flag, item)"
										@positionTap="positionTap"
									/>
									<div v-if="!contentList.length" class="mt-[10vh]">
										<n-empty description="暂无数据"></n-empty>
									</div>
								</div>
							</div>
						</n-tab-pane>
						<n-tab-pane name="1" tab="问答">
							<questions
								ref="questionsRef"
								:dialogue="dialogue"
								:main="main"
								:show="false"
								:submitFlag="true"
								@noteAdded="noteAdded"
							></questions>
						</n-tab-pane>
					</n-tabs>
				</div>
			</div>
		</div>
	</NSpin>
</template>

<style lang="less" scoped>
.contentList {
	height: calc(100vh - 424px);
}

.full-height {
	height: calc(100vh - 100px);
}

.full-jiaocai {
	height: calc(100vh - 180px);
}

:deep(.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link) {
	color: #125eff;
}

.children {
	justify-content: left;
}

.children:hover .children-img {
	display: none;
}

.children-imga {
	display: none;
}

.children:hover .children-imga {
	display: block;
}

.center-item {
	transition: 0.25s;

	p {
		transition: 0.25s;
	}

	&:hover {
		background: #fff;
	}
}

.center-item:hover p {
	color: #125eff;
}

.center-item:hover .center-r {
	visibility: visible;
	opacity: 1;
}

.center-r {
	transition: 0.25s;
	opacity: 0;
}

:deep(.n-tabs .n-tabs-rail) {
	background: #f1f4fe;
}

:deep(.n-tabs-tab--active) {
	.n-tabs-tab__label {
		color: #125eff;
		font-weight: bold;
	}
}

.chatbg {
	padding-top: 30px;
	// height: 100vh;
}

.leftbox {
	width: 50%;
}

.rightbox {
	width: 50%;
}

.headers {
	width: 100%;
	height: 40px;
	display: flex;
	justify-content: space-between;
	// margin-bottom: 90px;
	.left {
		color: #323233;
		font-size: 20px;
		font-weight: 500;
		line-height: 0;
		letter-spacing: 0;
		line-height: 40px;
		display: flex;
		align-items: center;
		margin-left: 24px;

		.gohome {
			width: 40px;
			height: 40px;
			background: #fafbff;
			border: 1px solid #e9ecf3;
			border-radius: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 11px;

			img {
				width: 20px;
				height: 20px;
			}
		}

		span {
			color: #909399;
			margin-left: 8px;
		}
	}
}

.documentbg {
	// width: 796px;
	width: 63.5%;
	height: calc(100vh - 100px);
	background: #ffffff;
	box-shadow: 0 0 10px 0 #00000021;
	border-radius: 4px;
	//margin-left: 24px;
	//margin-right: 24px;
	//margin-top: 24px;
	//margin-bottom: 24px;
	overflow-y: auto;
}

.autoheight {
	width: 50%;
	height: 85vh;
	padding-left: 24px;
	padding-right: 32px;
	/* 允许内容溢出时滚动 */
	overflow-y: auto;
	/* 隐藏 WebKit 浏览器的滚动条 */
	scrollbar-width: none;
	/* 隐藏 Firefox 的滚动条 */
	-ms-overflow-style: none;
}

.autoheight::-webkit-scrollbar {
	display: none;
}

/* 优化后的平滑动画效果 */
.slide-fade-enter-active {
	transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-leave-active {
	transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
	transform: translateX(-20px);
	opacity: 0;
}
</style>
