<script lang="ts" setup>
import { computed } from 'vue'
import { NAvatar } from 'naive-ui'
import { useUserStore } from '@/store'
import logo from '@/assets/msgHead.png'
import { useChatStore,useToolsStore } from '@/store'
const chatStore = useChatStore()
const ToolsStore = useToolsStore()
interface Props {
  image?: boolean
}
defineProps<Props>()

const userStore = useUserStore()

const avatar = computed(() => userStore.userInfo.avatar)
</script>

<template>
  <template v-if="image">
    <!--    <NAvatar v-if="isString(avatar) && avatar.length > 0" :src="avatar" :fallback-src="defaultAvatar" /> -->
    <!--    <NAvatar v-else round :src="defaultAvatar" /> -->
  </template>
  <img  class="radioimg" v-else  :src="ToolsStore.ToolInfo.icon" alt="">
  <!-- <NAvatar v-else :size="32" round :src="logo" color="#fff" /> -->
</template>
<style lang="less" scoped>
.radioimg{
  width: 30.72px;
  border-radius: 100%;
  overflow: hidden;
}
</style>
