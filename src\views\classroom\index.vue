<script setup lang='ts'>
import { onMounted, ref } from 'vue'
import { NButton, NInput, NTab, NTabs } from 'naive-ui'
import { useRouter } from 'vue-router'
import {getlinksApi} from '@/api/tools'

const router = useRouter()

const list = ref([
  {
    title: '大模型',
    note: 'LLM/RAG/多模态',
  },
  {
    title: '提示词工程',
    note: 'Prompt Engineering',
  },
  {
    title: 'AI应用开发',
    note: 'AI原生应用开发实战',
  },
  {
    title: 'AI行业案例',
    note: '产业级AI应用案例分析',
  },
  {
    title: 'AI模型部署',
    note: '推理/部署/硬件',
  }, {
    title: '深度学习',
    note: 'CV/NLP/AI4S/RL',
  },
  {
    title: '机器学习',
    note: 'Python/人工智能入门课',
  },
])
import list1 from '@/assets/classroom/list1.png'
import list2 from '@/assets/classroom/list2.png'
import list3 from '@/assets/classroom/list3.png'
import list4 from '@/assets/classroom/list4.png'
import list5 from '@/assets/classroom/list5.png'
const center = ref([
  {
    title: 'AI 通识课（零基础版)',
    note: '专属知识体系、特色教学新模式',
    pepo: '67',
    ke: '1',
    url:list1,
  }, 
  // {
  //   title: '花式打造教育大模型应用',
  //   note: '教育部名师全国示范课程系列',
  //   pepo: '1829',
  //   ke: '3',
  //   url:list2,
  // }, 
  // {
  //   title: '自然语言处理基础与大模型',
  //   note: '专业案例，讲解入微',
  //   pepo: '1829',
  //   ke: '3',
  //   url:list3,
  // }, 
  {
    title: 'AI 通识课（有编程基础版）',
    note: '专业案例，讲解入微',
    pepo: '29',
    ke: '1',
    url:list4,
  },
  //  {
  //   title: '从零开始打造自己的大模型',
  //   note: '行业资深工程师带你步步为营',
  //   pepo: '1829',
  //   ke: '3',
  //   url:list5,
  // },
])
function jumpPage(i:any){
  if(i==0){
    // router.push("/videoPlayPage")
    window.open(videoUrl.value[0].linkUrl)
    // const route = router.resolve({ path: '/videoPlayPage' })
    // window.open(route.href, '_blank')
  }else{
    window.open(videoUrl.value[1].linkUrl)
    // window.open("https://files.momodel.cn/SaaS_AI%E9%80%9A%E8%AF%86%E8%AF%BE/%E7%8C%AB%E7%8B%97%E8%AF%86%E5%88%AB.mp4")

  }
}
var videoUrl=ref([])
function getlinkfun(){
    getlinksApi({linkType:'DKT'}).then(res=>{
    videoUrl.value=res.data;
    })
}
onMounted(() => {
  getlinkfun()
})
</script>

<template>
  <div class="back setheight">
    <div class="flex flex-col w-full h-full  p-8">

    <header class="flex items-center justify-between">
      <div class="flex items-center ">
        <img class="w-[22px] h-[22px]" src="@/assets/classroom/title.png" alt="">
        <div class="text-[26px] ml-[10px] font-semibold">
          智教大课堂
        </div>
      </div>
      <div class="w-[394px]">
        <NInput round placeholder="搜索课程">
          <template #suffix>
            <img src="@/assets/sider/search-logo.png" alt="">
          </template>
        </NInput>
      </div>
    </header>

    <div class="bg-[#fff] shadow px-[46px] mt-4">
      <NTabs size="large" type="line">
        <NTab name="1">
          大厅
        </NTab>
        <NTab name="2">
          课程
        </NTab>
        <NTab name="3">
          精品课
        </NTab>
        <NTab name="4">
          实验实训
        </NTab>
        <NTab name="5">
          项目案例
        </NTab>
        <NTab name="6">
          我的课程
        </NTab>
      </NTabs>
    </div>

    <div class="flex justify-between gap-[10px] w-full mt-3">
      <div class="shadow leftbox flex  flex-col gap-5 rounded-lg px-[30px] flex-shrink-0 w-[320px] bg-[#fff] pt-[38px] pb-[40px]">
        <div v-for="(item, index) in list" :key="index" class="flex">
          <div class="w-[110px] font-semibold leftboxtit">
            {{ item.title }}
          </div>
          <div class="text-[12px] text-[#878D9F] leftboxdes">
            {{ item.note }}
          </div>
        </div>
      </div>
      <div class="flex-grow tupian ">
        <img class="w-full  object-cover" src="@/assets/classroom/center.png" alt="">
      </div>
    </div>

    <div class="flex items-center my-4">
      <div class="font-bold mr-4 text-[26px]">
        推荐学习
      </div>
      <div class="text-[16px]">
        名师打造 精品课程
      </div>
    </div>
    <div class="flex flex-wrap flex-start  gap-4 ">
      <div v-for="(item, i) in center" @click="jumpPage(i)" :key="i" class="videobox w-[290px] h-[322px] bg-[#fff] shadow rounded-[14px]">
        <img class="cen-back " :src="item.url">
        <div class="px-5 pt-[17px]">
          <div class="text-[16px]">
            {{ item.title }}
          </div>
          <div class="text-[12px] mt-[4px]">
            {{ item.note }}
          </div>
          <div class="w-full mt-[18px]  h-[1px] bg-[#E2E2E2]" />
          <div class="flex mt-[18px] justify-center">
            <div class="text-[#125EFF] text-[12px] mr-[10px] leading-[26px]">
              {{ item.pepo }}人在学习
            </div>
            <div class="text-[#7C818F] text-[12px] mr-[42px]  leading-[26px]">
              {{ item.ke }}节课
            </div>
            <NButton color="#0264FA" class="!w-[80px] !h-[26px] !rounded-[16px] !text-[12px]">
              开始学习
            </NButton>
          </div>
        </div>
      </div>
    </div>
  </div>

  </div>
</template>

<style lang='less' scoped>
.cen-back{
	width: 100%;
	height: 172px;
	background-size: 118% 134%;
  background-repeat: no-repeat;
  background-position: center;
}
.back{
	background: url("@/assets/chat/back.png");
	background-size: 100% 100%;
}
.shadow{
	box-shadow: 0 2px 4px 0 #c9c9c980;
  // width: 21%;
}
.tupian{
  width: 76.5rem;
  // height: 22.25rem;
}
.leftbox{
  width: 20rem;
  padding-left: 1.88rem;
  padding-right: 0rem;
  padding-top: 2.38rem;
  padding-bottom: 2.72rem;
  .leftboxtit{
    width: 7rem;
height: 1.38rem;
font-family: PingFangSC-Semibold;
font-weight: 600;
font-size: 1rem;
color: #2F3033;
letter-spacing: 0;
  }
  .leftboxdes{
height: 1.38rem;
font-family: PingFangSC-Regular;
font-weight: 400;
font-size: 0.88rem;
color: #878D9F;
letter-spacing: 0;
line-height: 1.38rem;
  }
// height: 22.25rem;
}
.setheight{
  height: 100vh;
  overflow-y: auto;
}
.videobox{
  /* 添加过渡效果 */
  transition: box-shadow 0.2s ease, transform 0.2s ease; 
}
/* 添加鼠标悬浮效果 */
.videobox:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1); 
  transform: translateY(-5px); 
  cursor: pointer;
}
</style>
