/**
 * 智能体编排系统API接口
 *
 * 【重要说明】当前使用模拟数据实现，用于开发和测试阶段
 *
 * 模拟实现说明：
 * - 使用localStorage作为模拟的后端存储
 * - 使用setTimeout模拟网络延迟（200-800ms）
 * - 所有API函数都返回Promise，保持与真实API的一致性
 *
 * 真实后端对接时需要修改：
 * 1. 恢复import { get, post } from '@/utils/request'
 * 2. 将所有函数内的模拟逻辑替换为真实的HTTP请求
 * 3. 调整响应数据格式以匹配后端API规范
 * 4. 添加适当的错误处理和重试机制
 */

// import { get, post } from '@/utils/request' // 真实后端对接时取消注释
import type {
  BackendFlowData,
  FlowExecutionRequest,
  FlowExecutionResponse
} from '@/types/backend'

// ==================== 模拟数据存储工具 ====================

/**
 * localStorage键名常量
 */
const STORAGE_KEYS = {
  FLOWS: 'orchestration_flows',
  TEMPLATES: 'orchestration_templates',
  EXECUTIONS: 'orchestration_executions'
}

/**
 * 模拟网络延迟
 * @param min 最小延迟时间（毫秒）
 * @param max 最大延迟时间（毫秒）
 */
const mockDelay = (min = 200, max = 800): Promise<void> => {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min
  return new Promise(resolve => setTimeout(resolve, delay))
}

/**
 * 从localStorage获取数据
 * @param key 存储键名
 * @param defaultValue 默认值
 */
const getStorageData = <T>(key: string, defaultValue: T): T => {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : defaultValue
  } catch (error) {
    console.warn(`读取localStorage数据失败: ${key}`, error)
    return defaultValue
  }
}

/**
 * 保存数据到localStorage
 * @param key 存储键名
 * @param data 要保存的数据
 */
const setStorageData = <T>(key: string, data: T): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.error(`保存localStorage数据失败: ${key}`, error)
    throw new Error('存储空间不足或数据格式错误')
  }
}

/**
 * 生成模拟的响应数据
 * @param data 响应数据
 * @param message 响应消息
 * @param status 响应状态
 */
const createMockResponse = <T>(data: T, message = '操作成功', status = 'success'): ApiResponse<T> => ({
  data,
  message,
  status
})

/**
 * 通用API响应接口
 */
export interface ApiResponse<T = any> {
  data: T
  message: string
  status: string
}

/**
 * 分页查询参数接口
 */
export interface PaginationParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  tags?: string[]
}

/**
 * 分页响应接口
 */
export interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// ==================== 流程管理API ====================

/**
 * 保存流程（模拟实现）
 *
 * 【模拟说明】
 * - 当前使用localStorage模拟后端存储
 * - 自动生成流程ID和时间戳
 * - 模拟200-800ms的网络延迟
 *
 * 【真实对接时需要修改】
 * - 恢复HTTP POST请求到 /api/orchestration/flows
 * - 移除localStorage操作
 * - 处理后端返回的实际流程ID
 * - 添加网络错误处理
 *
 * @param flowData 流程数据
 * @returns 保存结果，包含后端生成的流程ID
 */
export async function saveFlowApi<T = any>(flowData: BackendFlowData): Promise<ApiResponse<T>> {
  await mockDelay()

  try {
    // 模拟后端生成ID和时间戳
    const savedFlow = {
      ...flowData,
      id: flowData.id || `flow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      metadata: {
        ...flowData.metadata,
        updatedAt: new Date().toISOString()
      }
    }

    // 保存到localStorage（模拟后端存储）
    const flows = getStorageData<BackendFlowData[]>(STORAGE_KEYS.FLOWS, [])
    const existingIndex = flows.findIndex(f => f.id === savedFlow.id)

    if (existingIndex >= 0) {
      flows[existingIndex] = savedFlow
    } else {
      flows.push(savedFlow)
    }

    setStorageData(STORAGE_KEYS.FLOWS, flows)

    return createMockResponse({ id: savedFlow.id, ...savedFlow } as T, '流程保存成功')
  } catch (error) {
    throw new Error(`保存流程失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 加载指定流程（模拟实现）
 *
 * 【模拟说明】
 * - 从localStorage读取流程数据
 * - 模拟网络延迟
 * - 如果流程不存在，返回404错误
 *
 * 【真实对接时需要修改】
 * - 恢复HTTP GET请求到 /api/orchestration/flows/:id
 * - 移除localStorage操作
 * - 处理后端的错误响应格式
 * - 添加缓存机制（如需要）
 *
 * @param flowId 流程ID
 * @returns 流程数据
 */
export async function loadFlowApi<T = BackendFlowData>(flowId: string): Promise<ApiResponse<T>> {
  await mockDelay()

  try {
    const flows = getStorageData<BackendFlowData[]>(STORAGE_KEYS.FLOWS, [])
    const flow = flows.find(f => f.id === flowId)

    if (!flow) {
      throw new Error(`流程不存在: ${flowId}`)
    }

    return createMockResponse(flow as T, '流程加载成功')
  } catch (error) {
    throw new Error(`加载流程失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 获取当前用户的默认流程（模拟实现）
 *
 * 【模拟说明】
 * - 返回localStorage中的第一个流程作为默认流程
 * - 如果没有流程，返回null
 * - 用于页面初始化时的自动加载
 *
 * 【真实对接时需要修改】
 * - 恢复HTTP GET请求到 /api/orchestration/flows/default
 * - 根据用户身份获取默认流程
 * - 处理用户权限和流程访问控制
 *
 * @returns 默认流程数据或null
 */
export async function loadDefaultFlowApi<T = BackendFlowData>(): Promise<ApiResponse<T | null>> {
  await mockDelay()

  try {
    const flows = getStorageData<BackendFlowData[]>(STORAGE_KEYS.FLOWS, [])
    const defaultFlow = flows.length > 0 ? flows[0] : null

    return createMockResponse(defaultFlow as T | null, defaultFlow ? '默认流程加载成功' : '暂无流程数据')
  } catch (error) {
    throw new Error(`加载默认流程失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 更新流程（模拟实现）
 *
 * 【模拟说明】
 * - 在localStorage中查找并更新指定流程
 * - 自动更新时间戳
 * - 如果流程不存在，抛出错误
 *
 * 【真实对接时需要修改】
 * - 恢复HTTP PUT请求到 /api/orchestration/flows/:id
 * - 移除localStorage操作
 * - 处理并发更新冲突
 * - 添加版本控制机制
 *
 * @param flowId 流程ID
 * @param flowData 更新的流程数据
 * @returns 更新结果
 */
export async function updateFlowApi<T = any>(flowId: string, flowData: BackendFlowData): Promise<ApiResponse<T>> {
  await mockDelay()

  try {
    const flows = getStorageData<BackendFlowData[]>(STORAGE_KEYS.FLOWS, [])
    const existingIndex = flows.findIndex(f => f.id === flowId)

    if (existingIndex === -1) {
      throw new Error(`流程不存在: ${flowId}`)
    }

    // 更新流程数据
    const updatedFlow = {
      ...flowData,
      id: flowId,
      metadata: {
        ...flowData.metadata,
        updatedAt: new Date().toISOString()
      }
    }

    flows[existingIndex] = updatedFlow
    setStorageData(STORAGE_KEYS.FLOWS, flows)

    return createMockResponse(updatedFlow as T, '流程更新成功')
  } catch (error) {
    throw new Error(`更新流程失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 删除流程（模拟实现）
 *
 * 【模拟说明】
 * - 从localStorage中删除指定流程
 * - 如果流程不存在，抛出错误
 *
 * 【真实对接时需要修改】
 * - 恢复HTTP DELETE请求到 /api/orchestration/flows/:id
 * - 移除localStorage操作
 * - 添加删除权限验证
 * - 处理级联删除（如相关执行记录）
 *
 * @param flowId 流程ID
 * @returns 删除结果
 */
export async function deleteFlowApi<T = any>(flowId: string): Promise<ApiResponse<T>> {
  await mockDelay()

  try {
    const flows = getStorageData<BackendFlowData[]>(STORAGE_KEYS.FLOWS, [])
    const existingIndex = flows.findIndex(f => f.id === flowId)

    if (existingIndex === -1) {
      throw new Error(`流程不存在: ${flowId}`)
    }

    flows.splice(existingIndex, 1)
    setStorageData(STORAGE_KEYS.FLOWS, flows)

    return createMockResponse({ deleted: true } as T, '流程删除成功')
  } catch (error) {
    throw new Error(`删除流程失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// ==================== 流程执行API（简化模拟实现） ====================

/**
 * 执行流程（模拟实现）
 *
 * 【模拟说明】
 * - 模拟流程执行，返回成功状态
 * - 不进行实际的节点执行
 * - 生成模拟的执行ID和结果
 *
 * 【真实对接时需要修改】
 * - 恢复HTTP POST请求到 /api/orchestration/flows/:id/execute
 * - 实现真实的异步执行逻辑
 * - 添加执行状态轮询机制
 * - 处理执行过程中的错误和中断
 *
 * @param executionRequest 执行请求参数
 * @returns 执行结果
 */
export async function executeFlowApi<T = FlowExecutionResponse>(executionRequest: FlowExecutionRequest): Promise<ApiResponse<T>> {
  await mockDelay(1000, 2000) // 模拟较长的执行时间

  try {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const result = {
      executionId,
      status: 'completed' as const,
      result: { message: '流程执行成功', output: '模拟执行结果' },
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString()
    }

    return createMockResponse(result as T, '流程执行完成')
  } catch (error) {
    throw new Error(`执行流程失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// ==================== 流程验证API（模拟实现） ====================

/**
 * 验证流程数据格式（模拟实现）
 *
 * 【模拟说明】
 * - 使用本地验证函数检查数据格式
 * - 返回验证结果和错误信息
 *
 * 【真实对接时需要修改】
 * - 恢复HTTP POST请求到 /api/orchestration/flows/validate
 * - 使用后端的验证规则和业务逻辑
 * - 添加更复杂的验证场景
 *
 * @param flowData 流程数据
 * @returns 验证结果
 */
export async function validateFlowApi<T = { valid: boolean; errors: any[] }>(flowData: BackendFlowData): Promise<ApiResponse<T>> {
  await mockDelay()

  try {
    // 这里可以调用本地的验证函数
    // import { validateBackendFlowData } from '@/utils/orchestration'
    // const validation = validateBackendFlowData(flowData)

    // 简单的模拟验证
    const isValid = !!(flowData.id && flowData.name && flowData.nodes && flowData.edges)
    const errors = isValid ? [] : ['流程数据格式不完整']

    const result = {
      valid: isValid,
      errors
    }

    return createMockResponse(result as T, isValid ? '验证通过' : '验证失败')
  } catch (error) {
    throw new Error(`验证流程失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}
