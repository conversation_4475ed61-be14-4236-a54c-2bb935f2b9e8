import { defineStore } from 'pinia'
import type {
  OrchestrationState,
  FlowNode,
  FlowEdge,
  FlowData,
  NodeExecutionLog
} from './helper'
import {
  defaultSetting,
  getLocalState,
  setLocalState,
  generateId,
  createDefaultNode,
  createDefaultEdge,
  createExecutionLog,
  NodeType,
  NodeStatus
} from './helper'
import {
  transformToBackendFormat,
  transformFromBackendFormat,
  validateBackendFlowData,
  isValidBackendFlowData,
  analyzeEdgeOptimization,
  analyzeVariableUsage,
  getVariableUsageSummary
} from '@/utils/orchestration'
import type { BackendFlowData, FlowVariable, VariableExportData } from '@/types/backend'
import {
  saveFlowApi,
  loadFlowApi,
  loadDefaultFlowApi,
  updateFlowApi,
  deleteFlowApi,
  executeFlowApi
} from '@/api/orchestration'
import startIcon from "@/assets/agentOrchestration/startIcon.png";
import endIcon from "@/assets/agentOrchestration/endIcon.png";

export const useOrchestrationStore = defineStore('orchestration-store', {
  state: (): OrchestrationState => getLocalState(),

  getters: {
    getNewFlowFrom: (state) => state.newFlowFrom,
    // 获取当前流程的节点
    currentNodes: (state) => state.currentFlow?.nodes || [],
    
    // 获取当前流程的边
    currentEdges: (state) => state.currentFlow?.edges || [],
    
    // 获取选中的节点
    selectedNode: (state) => {
      if (!state.selectedNodeId || !state.currentFlow) return null
      return state.currentFlow.nodes.find(node => node.id === state.selectedNodeId) || null
    },
    
    // 检查是否有正在运行的节点
    hasRunningNodes: (state) => {
      if (!state.currentFlow) return false
      return state.currentFlow.nodes.some(node => node.data.status === NodeStatus.RUNNING)
    },

    // 后端交互状态相关getters
    isBackendLoading: (state) => state.backend.isLoading,
    isBackendSyncing: (state) => state.backend.isSyncing,
    backendSyncStatus: (state) => state.backend.syncStatus,
    lastSyncTime: (state) => state.backend.lastSyncTime,
    lastBackendError: (state) => state.backend.lastError,

    // 检查是否需要同步
    needsSync: (state) => {
      if (!state.currentFlow || !state.backend.lastSyncTime) return true
      const lastModified = new Date(state.currentFlow.updatedAt).getTime()
      const lastSync = new Date(state.backend.lastSyncTime).getTime()
      return lastModified > lastSync
    }
  },

  actions: {
    updateNewFlowFrom(data:OrchestrationState['newFlowFrom']){
      this.newFlowFrom = data
    },
     getstartAndEndNode():FlowNode[]{
      var  nodeList=[{
        type:'start',position:{x:501,y:318},label:'开始',description:'流程的起始点，每个流程只能有一个开始节点',icon:startIcon,color:'#52c41a'
      },{
        type:'end',position:{x:901,y:318},label:'结束',description:'流程的终止点，可以有多个结束节点',icon:endIcon,color:'#ff4d4f'
      }]
     return nodeList.map((item)=>{
      console.log(item);
      
       const newNode = createDefaultNode(item.type, item.position)
           newNode.data = {
             ...newNode.data,
             label: item.label,
             color: item.color,
             icon: item.icon,
             description: item.description,
           }
           console.log(newNode);
           
           return newNode
      })
     },
     initvariable(node:FlowNode){
      var variableList=[
        {name:"当前对话信息",value:"",valueType:"string"},
        {name:"用户ID",value:"",valueType:"string"},
        {name:"智能体ID",value:"",valueType:"string"},
        {name:"触发时间",value:"",valueType:"string"},
      ]
      variableList.forEach((item)=>{
        let variableObj={
              id: item.name==="当前对话信息" ? "query" : generateId(),
              name:item.name,
              readonly:true,
              type: 'nodeVariable',
              valueType:item.valueType,
              value: "",
              nodeId: node.id,
              nodeName: node.data.label,
              nodeType: node.type,
        }
     
        this.addVariable(variableObj)
      })
 
      // let config={
      //   icon:node.data.icon,
      //   description:node.data.description,
      //   label:node.data.label,
      //   config:{
      //     outputKey:variableId
      //   }
      // }
      // this.updateNodeData(node.id, config)
     },
    // 创建新流程
    async createNewFlow(name: string, description?: string): Promise<void> {
        await this.clearCurrentFlow()
      const newFlow: FlowData = {
        id: generateId(),
        name,
        description,
        nodes: [],
        edges: [],
        variables: [], // 初始化变量数组
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      newFlow.nodes=this.getstartAndEndNode()
      this.currentFlow = newFlow
      this.flows.push(newFlow)

      // 初始化历史记录
      this.history.present = { nodes: [], edges: [] }
      this.history.past = []
      this.history.future = []

      this.initvariable(newFlow.nodes[0])
      // 暂不清除，否则刷新页面数据丢失，需要在保存流程中清除
      // this.newFlowFrom={
      //   name:"",
      //   description:"",
      //   scenceCategory:"",
      // }
      this.recordState()
    },

    // 加载流程
    loadFlow(flowId: string): void {
      const flow = this.flows.find(f => f.id === flowId)
      if (flow) {
        this.currentFlow = { ...flow }

        // 初始化历史记录
        this.history.present = {
          nodes: JSON.parse(JSON.stringify(flow.nodes)),
          edges: JSON.parse(JSON.stringify(flow.edges))
        }
        this.history.past = []
        this.history.future = []
      }
    },

    // 保存当前流程
    saveCurrentFlow(): void {
      if (!this.currentFlow) return
      
      this.currentFlow.updatedAt = new Date().toISOString()
      const index = this.flows.findIndex(f => f.id === this.currentFlow!.id)
      if (index !== -1) {
        this.flows[index] = { ...this.currentFlow }
      }
      this.recordState()
    },

    // 删除流程
    deleteFlow(flowId: string): void {
      this.flows = this.flows.filter(f => f.id !== flowId)
      if (this.currentFlow?.id === flowId) {
        this.currentFlow = null
      }
      this.recordState()
    },

    // 添加节点
    addNode(node: FlowNode): void {
      if (!this.currentFlow) return
      
      this.currentFlow.nodes.push(node)
      this.saveCurrentFlow()
    },

    // 更新节点
    updateNode(nodeId: string, updates: Partial<FlowNode>): void {
      if (!this.currentFlow) return
      
      const nodeIndex = this.currentFlow.nodes.findIndex(n => n.id === nodeId)
      if (nodeIndex !== -1) {
        this.currentFlow.nodes[nodeIndex] = { 
          ...this.currentFlow.nodes[nodeIndex], 
          ...updates 
        }
        this.saveCurrentFlow()
      }
    },

    // 更新节点数据
    updateNodeData(nodeId: string, data: Partial<any>): void {
      if (!this.currentFlow) return
      
      const nodeIndex = this.currentFlow.nodes.findIndex(n => n.id === nodeId)
      if (nodeIndex !== -1) {
        this.currentFlow.nodes[nodeIndex].data = {
          ...this.currentFlow.nodes[nodeIndex].data,
          ...data
        }
        this.saveCurrentFlow()
      }
    },

    // 更新节点状态
    updateNodeStatus(nodeId: string, status: NodeStatus): void {
      this.updateNodeData(nodeId, { status })
    },

    // 添加节点执行日志
    addNodeExecutionLog(nodeId: string, log: NodeExecutionLog): void {
      if (!this.currentFlow) return

      const nodeIndex = this.currentFlow.nodes.findIndex(n => n.id === nodeId)
      if (nodeIndex !== -1) {
        const node = this.currentFlow.nodes[nodeIndex]

        // 初始化日志数组（如果不存在）
        if (!node.data.executionLogs) {
          node.data.executionLogs = []
        }

        // 添加新日志到数组开头（最新的在前面）
        node.data.executionLogs.unshift(log)

        // 限制日志数量（最多保存50条）
        if (node.data.executionLogs.length > 50) {
          node.data.executionLogs = node.data.executionLogs.slice(0, 50)
        }

        // 更新最后执行记录和总执行次数
        node.data.lastExecution = log
        node.data.totalExecutions = (node.data.totalExecutions || 0) + 1

        this.saveCurrentFlow()
      }
    },

    // 清除节点执行日志
    clearNodeExecutionLogs(nodeId: string): void {
      if (!this.currentFlow) return

      const nodeIndex = this.currentFlow.nodes.findIndex(n => n.id === nodeId)
      if (nodeIndex !== -1) {
        const node = this.currentFlow.nodes[nodeIndex]
        node.data.executionLogs = []
        node.data.lastExecution = null
        node.data.totalExecutions = 0
        this.saveCurrentFlow()
      }
    },

    // 获取节点执行日志
    getNodeExecutionLogs(nodeId: string): NodeExecutionLog[] {
      if (!this.currentFlow) return []

      const node = this.currentFlow.nodes.find(n => n.id === nodeId)
      return node?.data.executionLogs || []
    },

    // 清除所有节点的执行日志
    clearAllNodeExecutionLogs(): void {
      if (!this.currentFlow) return

      this.currentFlow.nodes.forEach(node => {
        node.data.executionLogs = []
        node.data.lastExecution = null
        node.data.totalExecutions = 0
      })

      this.saveCurrentFlow()
    },

    // 运行单个节点
    async runNode(nodes: any): Promise<void> {
      console.log(nodes);
      
      if (!this.currentFlow) return
       let nodeId = nodes.node.nodeId;
       console.log(nodeId);
       
      const node = this.currentFlow.nodes.find(n => n.id === nodeId)
      if (!node) return

      let executionLog: NodeExecutionLog
      let output: any = null
//这里做测试节点成功失败逻辑，需要自己计算用时，需要之前的nodestart函数进行传参保存
        var duration 
        var timestamp
        if(node.type=='start' || node.type=='end'){
          duration = Math.floor(Math.random() * 10) + 1; // 生成1-10毫秒的随机数
          timestamp = Date.now();
        }else{
          duration =nodes.timestamp - nodes.starttimestamp
          timestamp =nodes.starttimestamp
        }
      if(nodes.node.nodeStatus=='0' || node.type=='start' || node.type=='end'){
        // 成功
            // 执行节点并获取输出
        output = await this.executeNode(node)
        // 创建成功的执行日志
        executionLog = createExecutionLog(
          timestamp,
          NodeStatus.SUCCESS,
          duration,
          {
            nodeConfig: node.data.config,
            nodeType: node.type,
            nodeLabel: node.data.label
          }, // 输入数据
          output, // 输出结果
          undefined, // 无错误
          {
            nodeType: node.type,
            nodeLabel: node.data.label
          }
        )

        // 设置节点为成功状态
        this.updateNodeStatus(nodeId, NodeStatus.SUCCESS)
      }else{
        // 失败
        const errorMessage = '错误信息'
        // 创建失败的执行日志
        executionLog = createExecutionLog(
          timestamp,
          NodeStatus.ERROR,
          duration,
          {
            nodeConfig: node.data.config,
            nodeType: node.type,
            nodeLabel: node.data.label
          }, // 输入数据
          null, // 无输出
          errorMessage, // 错误信息
          {
            nodeType: node.type,
            nodeLabel: node.data.label
          }
        )

        // 设置节点为错误状态
        this.updateNodeStatus(nodeId, NodeStatus.ERROR)
      }
        // 更新节点的执行日志
        this.addNodeExecutionLog(nodeId, executionLog!)
    },

    // 执行节点逻辑
    async executeNode(node: FlowNode): Promise<any> {
      const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

      switch (node.type) {
        case NodeType.START:
          await delay(500)
          return { message: '流程开始', timestamp: new Date().toISOString() }

        case NodeType.END:
          await delay(500)
          return { message: '流程结束', timestamp: new Date().toISOString() }

        case NodeType.API:
          await delay(1000 + Math.random() * 2000) // 1-3秒模拟API调用
          return {
            status_code: 200,
            data: { result: 'API调用成功', value: Math.random() * 100 },
            response_time: Math.floor(Math.random() * 1000) + 200
          }

        case NodeType.CONDITION:
          await delay(200 + Math.random() * 300) // 200-500ms模拟条件判断
          const result = Math.random() > 0.5
          return {
            condition_result: result,
            branch: result ? 'true' : 'false',
            evaluation_time: Math.floor(Math.random() * 100) + 10
          }

        default:
          await delay(1000)
          return { message: '节点执行完成' }
      }
    },

    // 运行整个流程
    async runFlow(): Promise<void> {
      if (!this.currentFlow || this.isRunning) return

      this.setRunning(true)

      try {
        // 重置所有节点状态
        this.resetAllNodeStatus()

        // 找到开始节点
        const startNode = this.currentFlow.nodes.find(n => n.type === NodeType.START)
        if (!startNode) {
          throw new Error('未找到开始节点')
        }

        // 从开始节点开始执行
        await this.executeFlowFromNode(startNode.id)

      } catch (error) {
        console.error('流程执行失败:', error)
        throw error
      } finally {
        this.setRunning(false)
      }
    },

    // 从指定节点开始执行流程
    async executeFlowFromNode(nodeId: string): Promise<void> {
      if (!this.currentFlow) return

      const node = this.currentFlow.nodes.find(n => n.id === nodeId)
      if (!node) return

      // 执行当前节点
      await this.runNode(nodeId)

      // 如果是结束节点，停止执行
      if (node.type === NodeType.END) {
        return
      }

      // 找到下一个节点
      const nextNodes = this.getNextNodes(nodeId)

      // 如果是条件节点，根据结果选择分支
      if (node.type === NodeType.CONDITION) {
        const result = await this.executeNode(node)
        const targetHandle = result.condition_result ? `${nodeId}-true` : `${nodeId}-false`
        const nextEdge = this.currentFlow.edges.find(e =>
          e.source === nodeId && e.sourceHandle === targetHandle
        )
        if (nextEdge) {
          await this.executeFlowFromNode(nextEdge.target)
        }
      } else {
        // 普通节点，执行所有下一个节点
        for (const nextNodeId of nextNodes) {
          await this.executeFlowFromNode(nextNodeId)
        }
      }
    },

    // 获取下一个节点
    getNextNodes(nodeId: string): string[] {
      if (!this.currentFlow) return []

      return this.currentFlow.edges
        .filter(e => e.source === nodeId)
        .map(e => e.target)
    },

    // 重置所有节点状态
    resetAllNodeStatus(): void {
      if (!this.currentFlow) return

      this.currentFlow.nodes.forEach(node => {
        // 只重置节点状态，保留执行日志数据
        this.updateNodeStatus(node.id, NodeStatus.IDLE)
      })

      // 保存更改
      this.saveCurrentFlow()
    },

    // 停止流程执行
    stopFlow(): void {
      this.setRunning(false)
      this.resetAllNodeStatus()
    },

    // 删除节点
    removeNode(nodeId: string): void {
      if (!this.currentFlow) return

      // 保存当前状态到历史记录
      this.saveToHistory()

      // 删除节点
      this.currentFlow.nodes = this.currentFlow.nodes.filter(n => n.id !== nodeId)

      // 删除相关的边
      this.currentFlow.edges = this.currentFlow.edges.filter(
        e => e.source !== nodeId && e.target !== nodeId
      )

      // 如果删除的是选中节点，清除选择
      if (this.selectedNodeId === nodeId) {
        this.selectedNodeId = null
      }

      this.saveCurrentFlow()
    },

    // 复制节点
    duplicateNode(nodeId: string): void {
      if (!this.currentFlow) return

      const originalNode = this.currentFlow.nodes.find(n => n.id === nodeId)
      if (!originalNode) return

      // 保存当前状态到历史记录
      this.saveToHistory()

      // 深度克隆原节点数据
      const newNode: FlowNode = {
        ...JSON.parse(JSON.stringify(originalNode)),
        id: generateId(),
        position: {
          x: originalNode.position.x + 50,
          y: originalNode.position.y + 50
        },
        data: {
          ...originalNode.data,
          label: `${originalNode.data.label} (副本)`,
          status: NodeStatus.IDLE // 重置状态为空闲
        }
      }

      // 添加新节点到当前流程
      this.currentFlow.nodes.push(newNode)
      this.saveCurrentFlow()
    },

    // 添加边
    addEdge(edge: FlowEdge): void {
      if (!this.currentFlow) return
      
      // 检查是否已存在相同的边
      const exists = this.currentFlow.edges.some(
        e => e.source === edge.source && e.target === edge.target
      )
      
      if (!exists) {
        this.currentFlow.edges.push(edge)
        this.saveCurrentFlow()
      }
    },

    // 删除边
    removeEdge(edgeId: string): void {
      if (!this.currentFlow) return

      // 保存当前状态到历史记录
      this.saveToHistory()

      this.currentFlow.edges = this.currentFlow.edges.filter(e => e.id !== edgeId)
      this.saveCurrentFlow()
    },

    // 更新边
    updateEdge(edgeId: string, updates: Partial<FlowEdge>): void {
      if (!this.currentFlow) return
      
      const edgeIndex = this.currentFlow.edges.findIndex(e => e.id === edgeId)
      if (edgeIndex !== -1) {
        this.currentFlow.edges[edgeIndex] = {
          ...this.currentFlow.edges[edgeIndex],
          ...updates
        }
        this.saveCurrentFlow()
      }
    },

    // 设置选中节点
    setSelectedNode(nodeId: string | null): void {
      this.selectedNodeId = nodeId
    },

    // 显示/隐藏配置弹窗
    setConfigModalVisible(visible: boolean): void {
      this.isConfigModalVisible = visible
    },

    // 设置运行状态
    setRunning(running: boolean): void {
      this.isRunning = running
    },

    // 清空当前流程
    clearCurrentFlow(): void {
      this.currentFlow = null
      this.selectedNodeId = null
      this.isConfigModalVisible = false
      this.isRunning = false
    },

    // 导出流程数据
    exportFlow(flowId?: string): FlowData | null {
      const flow = flowId 
        ? this.flows.find(f => f.id === flowId)
        : this.currentFlow
      
      return flow ? JSON.parse(JSON.stringify(flow)) : null
    },

    // 导入流程数据
    importFlow(flowData: FlowData): void {
      // 验证流程数据格式
      if (!this.validateFlowData(flowData)) {
        throw new Error('无效的流程数据格式')
      }

      // 生成新的ID避免冲突
      const importedFlow: FlowData = {
        ...flowData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      this.flows.push(importedFlow)
      this.currentFlow = importedFlow
      this.recordState()
    },

    // 验证流程数据格式
    validateFlowData(flowData: any): flowData is FlowData {
      return (
        flowData &&
        typeof flowData === 'object' &&
        typeof flowData.name === 'string' &&
        Array.isArray(flowData.nodes) &&
        Array.isArray(flowData.edges) &&
        typeof flowData.createdAt === 'string' &&
        typeof flowData.updatedAt === 'string'
      )
    },

    // 导出所有流程
    exportAllFlows(): FlowData[] {
      return JSON.parse(JSON.stringify(this.flows))
    },

    // 导入多个流程
    importFlows(flowsData: FlowData[]): void {
      if (!Array.isArray(flowsData)) {
        throw new Error('无效的流程数据格式')
      }

      const validFlows = flowsData.filter(flow => this.validateFlowData(flow))

      validFlows.forEach(flowData => {
        const importedFlow: FlowData = {
          ...flowData,
          id: generateId(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        this.flows.push(importedFlow)
      })

      this.recordState()
    },

    // 复制流程
    duplicateFlow(flowId: string): FlowData | null {
      const originalFlow = this.flows.find(f => f.id === flowId)
      if (!originalFlow) return null

      const duplicatedFlow: FlowData = {
        ...JSON.parse(JSON.stringify(originalFlow)),
        id: generateId(),
        name: `${originalFlow.name} (副本)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      this.flows.push(duplicatedFlow)
      this.recordState()
      return duplicatedFlow
    },

    // 重命名流程
    renameFlow(flowId: string, newName: string): boolean {
      const flow = this.flows.find(f => f.id === flowId)
      if (!flow) return false

      flow.name = newName
      flow.updatedAt = new Date().toISOString()

      // 如果是当前流程，也要更新当前流程
      if (this.currentFlow?.id === flowId) {
        this.currentFlow.name = newName
        this.currentFlow.updatedAt = flow.updatedAt
      }

      this.recordState()
      return true
    },

    // 获取流程统计信息
    getFlowStats(flowId: string): { nodeCount: number; edgeCount: number; lastModified: string } | null {
      const flow = this.flows.find(f => f.id === flowId)
      if (!flow) return null

      return {
        nodeCount: flow.nodes.length,
        edgeCount: flow.edges.length,
        lastModified: flow.updatedAt
      }
    },

    // 清理无效的流程数据
    cleanupFlows(): void {
      this.flows = this.flows.filter(flow => this.validateFlowData(flow))
      this.recordState()
    },

    // 记录状态到本地存储
    recordState(): void {
      setLocalState(this.$state)
    },

    // 重置状态
    resetState(): void {
      this.$state = defaultSetting()
      this.recordState()
    },

    // 保存当前状态到历史记录
    saveToHistory(): void {
      if (!this.currentFlow) return

      const currentState = {
        nodes: JSON.parse(JSON.stringify(this.currentFlow.nodes)),
        edges: JSON.parse(JSON.stringify(this.currentFlow.edges))
      }

      // 如果当前状态与历史记录中的最新状态相同，则不保存
      if (this.history.present &&
          JSON.stringify(this.history.present) === JSON.stringify(currentState)) {
        return
      }

      // 将当前状态移到past中
      if (this.history.present) {
        this.history.past.push(this.history.present)
      }

      // 限制历史记录数量（最多保存20个状态）
      if (this.history.past.length > 20) {
        this.history.past.shift()
      }

      // 设置新的当前状态
      this.history.present = currentState

      // 清空future（因为有了新的操作）
      this.history.future = []
    },

    // 撤销操作
    undo(): boolean {
      if (!this.currentFlow || this.history.past.length === 0) return false

      // 保存当前状态到future
      if (this.history.present) {
        this.history.future.unshift(this.history.present)
      }

      // 从past中取出最新状态
      const previousState = this.history.past.pop()!
      this.history.present = previousState

      // 恢复状态
      this.currentFlow.nodes = JSON.parse(JSON.stringify(previousState.nodes))
      this.currentFlow.edges = JSON.parse(JSON.stringify(previousState.edges))

      this.saveCurrentFlow()
      return true
    },

    // 重做操作
    redo(): boolean {
      if (!this.currentFlow || this.history.future.length === 0) return false

      // 保存当前状态到past
      if (this.history.present) {
        this.history.past.push(this.history.present)
      }

      // 从future中取出状态
      const nextState = this.history.future.shift()!
      this.history.present = nextState

      // 恢复状态
      this.currentFlow.nodes = JSON.parse(JSON.stringify(nextState.nodes))
      this.currentFlow.edges = JSON.parse(JSON.stringify(nextState.edges))

      this.saveCurrentFlow()
      return true
    },

    // 检查是否可以撤销
    canUndo(): boolean {
      return this.history.past.length > 0
    },

    // 检查是否可以重做
    canRedo(): boolean {
      return this.history.future.length > 0
    },

    // ==================== 后端交互方法 ====================

    /**
     * 导出当前流程的后端格式数据
     * @param flowId 可选的流程ID，不提供则使用当前流程
     * @returns 后端格式的流程数据
     */
    exportBackendFlow(flowId?: string): BackendFlowData | null {
      const flow = flowId
        ? this.flows.find(f => f.id === flowId)
        : this.currentFlow

      if (!flow) return null

      try {
        // 打印原始前端数据
        console.group('🔄 数据转换过程')
        console.log('📥 原始前端数据:', {
          id: flow.id,
          name: flow.name,
          nodesCount: flow.nodes.length,
          edgesCount: flow.edges.length,
          frontendDataSize: JSON.stringify(flow).length + ' 字符'
        })
        console.log('📥 前端节点详情:', flow.nodes.map(node => ({
          id: node.id,
          type: node.type,
          label: node.data?.label,
          position: node.position,
          hasUIData: !!(node.data?.style || node.data?.selected || node.data?.dragging),
          dataKeys: Object.keys(node.data || {})
        })))

        const backendData = transformToBackendFormat(flow)

        console.log('📤 转换后后端数据:', {
          id: backendData.id,
          name: backendData.name,
          nodesCount: backendData.nodes.length,
          edgesCount: backendData.edges.length,
          backendDataSize: JSON.stringify(backendData).length + ' 字符',
          compressionRatio: Math.round((1 - JSON.stringify(backendData).length / JSON.stringify(flow).length) * 100) + '%'
        })
        console.log('📤 后端节点详情:', backendData.nodes.map(node => ({
          id: node.id,
          type: node.type,
          label: node.label,
          configKeys: Object.keys(node.config || {}),
          hasPosition: false, // 后端数据不包含位置信息
          cleanedUIData: true // UI数据已被清理
        })))
        console.groupEnd()

        return backendData
      } catch (error) {
        console.error('❌ 导出后端格式数据失败:', error)
        this.backend.lastError = error instanceof Error ? error.message : '导出失败'
        return null
      }
    },

    /**
     * 从后端格式数据导入流程
     * @param backendFlow 后端格式的流程数据
     */
    importBackendFlow(backendFlow: BackendFlowData): void {
      // 验证后端数据格式
      if (!isValidBackendFlowData(backendFlow)) {
        throw new Error('无效的后端流程数据格式')
      }

      try {
        const frontendFlow = transformFromBackendFormat(backendFlow)
        this.importFlow(frontendFlow)
      } catch (error) {
        console.error('导入后端数据失败:', error)
        this.backend.lastError = error instanceof Error ? error.message : '导入失败'
        throw error
      }
    },

    /**
     * 保存当前流程到后端
     */
    async saveFlowToBackend(): Promise<void> {
      if (!this.currentFlow) {
        throw new Error('没有可保存的流程')
      }

      this.backend.isLoading = true
      this.backend.syncStatus = 'syncing'
      this.backend.lastError = null

      try {
        const backendData = this.exportBackendFlow()
        if (!backendData) {
          throw new Error('导出后端数据失败')
        }

        // 打印保存到后端的数据
        console.group('🚀 保存到后端的数据')
        console.log('📊 完整数据结构:', backendData)
        console.log('📝 基本信息:', {
          id: backendData.id,
          name: backendData.name,
          description: backendData.description,
          category: backendData.category
        })
        console.log('🔗 节点数量:', backendData.nodes.length)
        console.log('📋 节点详情:', backendData.nodes.map(node => ({
          id: node.id,
          type: node.type,
          label: node.label,
          configKeys: Object.keys(node.config || {}),
          hasInputParams: !!(node.inputParams && node.inputParams.length > 0),
          hasOutputParams: !!(node.outputParams && node.outputParams.length > 0)
        })))
        console.log('🔀 边数量:', backendData.edges.length)
        console.log('🔀 边详情:', backendData.edges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle
        })))

        // 显示节点位置信息保存状态
        console.log('📍 节点位置信息已保存:', backendData.nodes.length, '个节点')

        // 分析边数据优化效果
        if (this.currentFlow && this.currentFlow.edges.length > 0) {
          const optimization = analyzeEdgeOptimization(this.currentFlow.edges, backendData.edges)
          console.log('📊 边数据优化分析:')
          console.log(`  - 前端数据大小: ${optimization.frontendSize} 字节`)
          console.log(`  - 后端数据大小: ${optimization.backendSize} 字节`)
        }

        console.log('🔧 变量数量:', backendData.variables?.length || 0)
        if (backendData.variables && backendData.variables.length > 0) {
          console.log('🔧 变量详情:', backendData.variables)
        }
        console.log('📊 元数据:', backendData.metadata)
        console.log('💾 数据大小:', JSON.stringify(backendData).length, '字符')
        console.groupEnd()

        // 检查是否是新流程还是更新现有流程
        const isNewFlow = !this.currentFlow.metadata?.backendId

        if (isNewFlow) {
          console.log('📝 执行新建流程操作')
          const response = await saveFlowApi(backendData)
          // 保存后端返回的ID到元数据
          if (this.currentFlow.metadata) {
            this.currentFlow.metadata.backendId = response.data?.id || backendData.id
          } else {
            this.currentFlow.metadata = { backendId: response.data?.id || backendData.id }
          }
          console.log('✅ 新建流程成功，后端ID:', this.currentFlow.metadata.backendId)
        } else {
          console.log('📝 执行更新流程操作，流程ID:', this.currentFlow.metadata.backendId)
          await updateFlowApi(this.currentFlow.metadata.backendId, backendData)
          console.log('✅ 更新流程成功')
        }

        // 更新同步状态
        this.backend.syncStatus = 'success'
        this.backend.lastSyncTime = new Date().toISOString()

        // 同时保存到本地
        this.saveCurrentFlow()

      } catch (error) {
        console.error('❌ 保存到后端失败:', error)
        this.backend.syncStatus = 'error'
        this.backend.lastError = error instanceof Error ? error.message : '保存失败'
        throw error
      } finally {
        this.backend.isLoading = false
        this.backend.isSyncing = false
      }
    },

    /**
     * 自动加载默认流程（页面初始化时调用）
     *
     * 【功能说明】
     * - 页面加载时自动从后端获取用户的默认流程
     * - 如果后端没有数据，显示空白画布
     * - 静默加载，不显示错误提示给用户
     *
     * 【真实对接时需要调整】
     * - 根据用户身份获取对应的默认流程
     * - 添加用户权限验证
     * - 处理多租户场景下的数据隔离
     */
    async autoLoadDefaultFlow(): Promise<void> {
      // 如果已经有当前流程，不进行自动加载
      if (this.currentFlow) {
        return
      }

      this.backend.isLoading = true
      this.backend.syncStatus = 'syncing'
      this.backend.lastError = null

      try {
        const response = await loadDefaultFlowApi()
        const defaultFlow = response.data

        if (defaultFlow) {
          // 有默认流程，导入它
          this.importBackendFlow(defaultFlow)
          console.log('自动加载默认流程成功:', defaultFlow.name)
        } else {
          // 没有默认流程，显示空白画布
          console.log('后端暂无流程数据，显示空白画布')
        }

        // 更新同步状态
        this.backend.syncStatus = 'success'
        this.backend.lastSyncTime = new Date().toISOString()

      } catch (error) {
        // 自动加载失败时，静默处理，不影响用户体验
        console.warn('自动加载默认流程失败:', error)
        this.backend.syncStatus = 'error'
        this.backend.lastError = error instanceof Error ? error.message : '自动加载失败'
        // 不抛出错误，让用户可以正常使用空白画布
      } finally {
        this.backend.isLoading = false
        this.backend.isSyncing = false
      }
    },

    /**
     * 从后端加载指定流程（保留用于同步功能）
     *
     * 【使用场景】
     * - 同步功能中需要获取最新的后端数据
     * - 内部方法，不直接暴露给UI层
     *
     * @param flowId 流程ID
     */
    async loadFlowFromBackend(flowId: string): Promise<void> {
      this.backend.isLoading = true
      this.backend.syncStatus = 'syncing'
      this.backend.lastError = null

      try {
        const response = await loadFlowApi(flowId)
        const backendFlow = response.data

        if (!backendFlow) {
          throw new Error('未找到指定的流程')
        }

        this.importBackendFlow(backendFlow)

        // 更新同步状态
        this.backend.syncStatus = 'success'
        this.backend.lastSyncTime = new Date().toISOString()

      } catch (error) {
        console.error('从后端加载流程失败:', error)
        this.backend.syncStatus = 'error'
        this.backend.lastError = error instanceof Error ? error.message : '加载失败'
        throw error
      } finally {
        this.backend.isLoading = false
        this.backend.isSyncing = false
      }
    },

    /**
     * 同步本地和后端数据
     */
    async syncWithBackend(): Promise<void> {
      if (!this.currentFlow) {
        throw new Error('没有可同步的流程')
      }

      this.backend.isSyncing = true
      this.backend.syncStatus = 'syncing'
      this.backend.lastError = null

      try {
        // 如果有后端ID，尝试从后端加载最新数据
        if (this.currentFlow.metadata?.backendId) {
          const response = await loadFlowApi(this.currentFlow.metadata.backendId)
          const backendFlow = response.data

          if (backendFlow) {
            // 比较更新时间，决定是否需要更新本地数据
            const localTime = new Date(this.currentFlow.updatedAt).getTime()
            const backendTime = new Date(backendFlow.metadata.updatedAt).getTime()

            if (backendTime > localTime) {
              // 后端数据更新，更新本地数据
              const frontendFlow = transformFromBackendFormat(backendFlow)
              this.currentFlow = frontendFlow
              this.saveCurrentFlow()
            } else if (localTime > backendTime) {
              // 本地数据更新，推送到后端
              await this.saveFlowToBackend()
              return // saveFlowToBackend已经更新了同步状态
            }
          }
        } else {
          // 没有后端ID，直接保存到后端
          await this.saveFlowToBackend()
          return // saveFlowToBackend已经更新了同步状态
        }

        // 更新同步状态
        this.backend.syncStatus = 'success'
        this.backend.lastSyncTime = new Date().toISOString()

      } catch (error) {
        console.error('同步失败:', error)
        this.backend.syncStatus = 'error'
        this.backend.lastError = error instanceof Error ? error.message : '同步失败'
        throw error
      } finally {
        this.backend.isSyncing = false
      }
    },

    // ==================== 变量管理方法（本地操作，随流程一起保存）====================

    /**
     * 添加流程变量（本地操作）
     * 变量将随流程一起保存到后端，不需要单独的API调用
     * @param variable 变量数据
     */
    addVariable(variable: Omit<FlowVariable,  'createdAt' | 'updatedAt'>): void {
      if (!this.currentFlow) {
        throw new Error('没有当前流程')
      }

      // 检查变量名是否重复
      if (this.currentFlow.variables?.some(v => v.name === variable.name) && variable.type !== 'nodeVariable') {
        throw new Error(`变量名 "${variable.name}" 已存在`)
      }
      const newVariable: FlowVariable = {
        ...variable,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      if(!newVariable.id){
        newVariable.id= generateId()
      }

      if (!this.currentFlow.variables) {
        this.currentFlow.variables = []
      }

      this.currentFlow.variables.push(newVariable)
      this.currentFlow.updatedAt = new Date().toISOString()

      // 记录历史状态（支持撤销）
      this.recordState()

      // 保存到本地存储
      // this.saveCurrentFlow()
    },

    /**
     * 更新流程变量（本地操作）
     * @param variableId 变量ID
     * @param updates 更新数据
     */
    updateVariable(variableId: string, updates: Partial<Omit<FlowVariable, 'id' | 'createdAt'>>): void {
      if (!this.currentFlow?.variables) {
        throw new Error('没有找到变量列表')
      }

      const variableIndex = this.currentFlow.variables.findIndex(v => v.id === variableId)
      if (variableIndex === -1) {
        throw new Error(`变量 ID "${variableId}" 不存在`)
      }
      // 如果更新变量名，检查是否重复
      if (updates.name && updates.name !== this.currentFlow.variables[variableIndex].name && this.currentFlow.variables[variableIndex].type !== 'nodeVariable') {
        if (this.currentFlow.variables.some(v => v.name === updates.name && v.id !== variableId)) {
          throw new Error(`变量名 "${updates.name}" 已存在`)
        }
      }

      this.currentFlow.variables[variableIndex] = {
        ...this.currentFlow.variables[variableIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      }

      this.currentFlow.updatedAt = new Date().toISOString()

      // 记录历史状态（支持撤销）
      this.recordState()

      // 保存到本地存储
      // this.saveCurrentFlow()
    },

    /**
     * 删除流程变量（本地操作）
     * @param variableId 变量ID
     */
    deleteVariable(variableId: string): void {
      if (!this.currentFlow?.variables) {
        throw new Error('没有找到变量列表')
      }

      const variableIndex = this.currentFlow.variables.findIndex(v => v.id === variableId)
      if (variableIndex === -1) {
        throw new Error(`变量 ID "${variableId}" 不存在`)
      }

      this.currentFlow.variables.splice(variableIndex, 1)
      this.currentFlow.updatedAt = new Date().toISOString()

      // 记录历史状态（支持撤销）
      this.recordState()

      // 保存到本地存储
      // this.saveCurrentFlow()
    },
    getVariablesByNodeId(nodeId: string): FlowVariable[] {
      if (!this.currentFlow?.variables) {
        return []
      }
      if (!nodeId) {
        return this.currentFlow.variables
      }
      return this.currentFlow.variables.filter(v => v.nodeId === nodeId)
    },
    /**
     * 获取指定类型的变量列表
     * @param type 变量类型
     * @returns 变量列表
     */
    getVariablesByType(type?: 'envVariable' | 'user' | 'nodeVariable'): FlowVariable[] {
      if (!this.currentFlow?.variables) {
        return []
      }

      if (!type) {
        return this.currentFlow.variables
      }

      return this.currentFlow.variables.filter(v => v.type === type)
    },

    /**
     * 根据名称查找变量
     * @param name 变量名称
     * @returns 变量对象或undefined
     */
    getVariableByName(name: string): FlowVariable | undefined {
      if (!this.currentFlow?.variables) {
        return undefined
      }

      return this.currentFlow.variables.find(v => v.name === name)
    },
      /**
     * 根据名称查找变量
     * @param id 变量id
     * @returns 变量对象或undefined
     */
    getVariableById(id: string): FlowVariable | undefined {
      if (!this.currentFlow?.variables) {
        return undefined
      }
      var variableArr =this.currentFlow.variables.filter(v => v.id === id);
      return variableArr[0]
    },

    /**
     * 批量导入变量（本地操作）
     * 导入的变量将随流程一起保存到后端
     * @param variables 变量列表
     * @param overwrite 是否覆盖同名变量
     */
    importVariables(variables: FlowVariable[], overwrite = false): void {
      if (!this.currentFlow) {
        throw new Error('没有当前流程')
      }

      if (!this.currentFlow.variables) {
        this.currentFlow.variables = []
      }

      const conflicts: string[] = []
      const imported: FlowVariable[] = []

      variables.forEach(variable => {
        const existingIndex = this.currentFlow!.variables!.findIndex(v => v.name === variable.name)

        if (existingIndex >= 0) {
          if (overwrite) {
            // 覆盖现有变量
            this.currentFlow!.variables![existingIndex] = {
              ...variable,
              id: this.currentFlow!.variables![existingIndex].id, // 保持原有ID
              updatedAt: new Date().toISOString()
            }
            imported.push(variable)
          } else {
            conflicts.push(variable.name)
          }
        } else {
          // 添加新变量
          const newVariable: FlowVariable = {
            ...variable,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
          this.currentFlow!.variables!.push(newVariable)
          imported.push(newVariable)
        }
      })

      if (conflicts.length > 0 && !overwrite) {
        throw new Error(`以下变量名已存在: ${conflicts.join(', ')}`)
      }

      this.currentFlow.updatedAt = new Date().toISOString()

      // 记录历史状态（支持撤销）
      this.recordState()

      // 保存到本地存储
      this.saveCurrentFlow()
    },

    /**
     * 导出变量数据
     * @param type 变量类型过滤
     * @returns 变量导出数据
     */
    exportVariables(type?: 'envVariable' | 'user' | 'nodeVariable'): VariableExportData {
      const variables = this.getVariablesByType(type)

      return {
        version: '1.0',
        exportedAt: new Date().toISOString(),
        variables
      }
    },

    // ==================== 变量分析方法 ====================

    /**
     * 分析当前流程中的变量使用情况
     * @returns 变量分析结果
     */
    analyzeCurrentFlowVariables() {
      if (!this.currentFlow) {
        throw new Error('没有当前流程')
      }

      return analyzeVariableUsage(this.currentFlow)
    },

    /**
     * 获取变量使用统计摘要
     * @returns 统计摘要文本
     */
    getVariableUsageSummaryText(): string {
      if (!this.currentFlow) {
        return '没有当前流程'
      }

      const analysisResult = this.analyzeCurrentFlowVariables()
      return getVariableUsageSummary(analysisResult)
    },

    /**
     * 获取未使用的变量列表
     * @returns 未使用的变量名列表
     */
    getUnusedVariables(): string[] {
      if (!this.currentFlow) {
        return []
      }

      const analysisResult = this.analyzeCurrentFlowVariables()
      return analysisResult.unusedVariablesList
    },

    /**
     * 获取变量在指定节点中的使用次数
     * @param variableName 变量名称
     * @param nodeId 节点ID（可选，不指定则统计所有节点）
     * @returns 使用次数
     */
    getVariableUsageCount(variableName: string, nodeId?: string): number {
      if (!this.currentFlow) {
        return 0
      }

      const analysisResult = this.analyzeCurrentFlowVariables()
      const variableUsage = analysisResult.usageDetails.find(usage => usage.variableName === variableName)

      if (!variableUsage) {
        return 0
      }

      if (nodeId) {
        const nodeUsage = variableUsage.usedInNodes.find(usage => usage.nodeId === nodeId)
        return nodeUsage?.count || 0
      }

      return variableUsage.usageCount
    },

    /**
     * 检查变量是否在流程中被使用
     * @param variableName 变量名称
     * @returns 是否被使用
     */
    isVariableUsed(variableName: string): boolean {
      return this.getVariableUsageCount(variableName) > 0
    }
  }
})

export * from './helper'
