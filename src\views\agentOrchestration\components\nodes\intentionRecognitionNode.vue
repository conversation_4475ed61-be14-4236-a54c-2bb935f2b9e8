<template>
  <div
    class="intention-recognition-node"
    :class="{ selected: selected, running: isRunning }"
  >
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />

    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 节点头部 -->
      <div class="node-header">
        <img
          class="node-icon"
          src="@/assets/agentOrchestration/intentionRecognitionIcon.png"
          alt="意图识别"
        />
        <div class="node-title">{{ data.label || "意图识别" }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>

      <!-- 模型名称 -->
      <div class="model-name">{{ data.config?.modelConfig?.modelName || "请选择模型" }}</div>
      <!-- 分类项列表 -->
      <div class="categories-list" v-if="categories.length > 0">
        <div
          v-for="(category, index) in displayCategories"
          :key="category.id || index"
          class="category-item"
        >
          <span class="category-label">{{ category.name || '请添加意图' }}</span>
          <!-- 每个分类的连接点 -->
          <Handle
            type="source"
            :position="Position.Right"
          :id="`${category.id}`"
            class="output-handle category-handle"
          />
        </div>
        <div
          v-if="categories.length > maxDisplayCategories"
          class="more-indicator"
        >
          其他 {{ categories.length - maxDisplayCategories }} 项
          <!-- 隐藏分类的连接点 -->
          <Handle
            v-for="(category, index) in hiddenCategories"
            :key="`hidden-${category.id || index}`"
            type="source"
            :position="Position.Right"
            :id="`${category.id}`"
            class="output-handle category-handle hidden-handle"
          />
        </div>
      </div>
      <div v-else class="empty-intention-tip">请添加问题意图</div>
    </div>

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Handle, Position } from "@vue-flow/core";
import { NodeStatus } from "@/store/modules/orchestration";
import NodeLogDisplay from "../NodeLogDisplay.vue";

interface IntentionRecognitionNodeProps {
  id: string;
  data: {
    label?: string;
    description?: string;
    status?: NodeStatus;
    config?: {
      model?: string;
      categories?: string[] | any[];
      [key: string]: any;
    };
    [key: string]: any;
  };
  selected?: boolean;
}

const props = defineProps<IntentionRecognitionNodeProps>();

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING);

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return "status-running";
    case NodeStatus.SUCCESS:
      return "status-success";
    case NodeStatus.ERROR:
      return "status-error";
    default:
      return "status-idle";
  }
});

const categories = computed(() => {
  return props.data.config?.classes || []; //['意图', '其他']
});

const maxDisplayCategories = 3;

const displayCategories = computed(() => {
  return categories.value.slice(0, maxDisplayCategories);
});

const hiddenCategories = computed(() => {
  return categories.value.slice(maxDisplayCategories);
});
</script>

<style scoped lang="less">
@import "./styles/unified-node-styles.less";

.intention-recognition-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .model-name {
      .node-subtitle-style();
      background: #f3f4f6;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }

    .categories-list {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .category-item {
        .node-list-item-style();
        position: relative;

        .category-label {
          font-size: 11px;
          color: #6b7280;
        }

        // 分类连接点样式 - 显示在节点卡片上方，位置与意图对齐
        :deep(.category-handle) {
          position: absolute;
          right: -16px; // 放在卡片外侧，视觉上更明显
          top: 50%;
          transform: translateY(-50%);
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border: 2px solid #ffffff;
          border-radius: 50%;
          // 分离transform和其他属性的transition，避免位移
          transition: border-color 0.2s ease, background-color 0.2s ease,
            box-shadow 0.2s ease;
          z-index: 100; // 高层级，显示在卡片上方
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            // 使用原地缩放而不是改变尺寸，避免位移
            transform: translateY(-50%) scale(1.2);
            transform-origin: center;
            background: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            // 为transform单独设置快速transition
            transition: border-color 0.2s ease, background-color 0.2s ease,
              box-shadow 0.2s ease, transform 0.1s ease;
          }

          &.vue-flow__handle-connecting {
            background: #125eff;
            box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.3);
          }
        }
      }

      .more-indicator {
        .node-label-style();
        font-style: italic;
        text-align: center;
        padding: 2px 4px;
        color: #9ca3af;
        position: relative;

        // 隐藏分类的连接点样式
        :deep(.hidden-handle) {
          position: absolute;
          right: -12px; // 放在卡片外侧，视觉上更明显
          top: 50%;
          transform: translateY(-50%);
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border: 2px solid #ffffff;
          border-radius: 50%;
          // 分离transform和其他属性的transition，避免位移
          transition: border-color 0.2s ease, background-color 0.2s ease,
            box-shadow 0.2s ease;
          z-index: 100; // 高层级，显示在卡片上方
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            // 使用原地缩放而不是改变尺寸，避免位移
            transform: translateY(-50%) scale(1.2);
            transform-origin: center;
            background: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            // 为transform单独设置快速transition
            transition: border-color 0.2s ease, background-color 0.2s ease,
              box-shadow 0.2s ease, transform 0.1s ease;
          }

          &.vue-flow__handle-connecting {
            background: #125eff;
            box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.3);
          }
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }

  .empty-intention-tip {
    .node-subtitle-style();
    background: #f3f4f6;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
    text-align: left;
    user-select: none;
  }
}
</style>
