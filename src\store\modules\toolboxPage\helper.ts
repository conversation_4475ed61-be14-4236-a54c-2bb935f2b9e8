import { ss } from '@/utils/storage'

const LOCAL_NAME = 'toolboxPageStorage'

// 定义数据结构
export interface ToolboxPageData {
  // 可根据实际返回数据添加字段，示例如下
  applicationList: any[]
}

// 定义state结构
export interface ToolboxPageState {
  toolboxData: ToolboxPageData
}

// 默认设置
export function defaultSetting(): ToolboxPageState {
  return {
    toolboxData: {
      applicationList: []
    }
  }
}

// 获取本地状态
export function getLocalState(): ToolboxPageState {
  const localSetting: ToolboxPageState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

// 设置本地状态
export function setLocalState(setting: ToolboxPageState): void {
  ss.set(LOCAL_NAME, setting)
}