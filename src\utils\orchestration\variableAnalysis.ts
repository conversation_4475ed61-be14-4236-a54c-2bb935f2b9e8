/**
 * 变量使用情况分析工具
 * 
 * 提供变量在流程中的使用分析、引用统计、依赖关系等功能
 */

import type { FlowData, FlowNode } from '@/store/modules/orchestration'
import type { FlowVariable } from '@/types/backend'

/**
 * 变量使用信息接口
 */
export interface VariableUsage {
  /** 变量名称 */
  variableName: string
  /** 使用次数 */
  usageCount: number
  /** 使用的节点列表 */
  usedInNodes: VariableNodeUsage[]
  /** 是否被修改 */
  isModified: boolean
  /** 修改的节点列表 */
  modifiedInNodes: VariableNodeUsage[]
}

/**
 * 变量在节点中的使用信息
 */
export interface VariableNodeUsage {
  /** 节点ID */
  nodeId: string
  /** 节点名称 */
  nodeName: string
  /** 节点类型 */
  nodeType: string
  /** 使用位置（字段路径） */
  usageLocation: string[]
  /** 使用类型：read(读取) | write(写入) */
  usageType: 'read' | 'write'
  /** 使用次数 */
  count: number
}

/**
 * 变量依赖关系
 */
export interface VariableDependency {
  /** 变量名称 */
  variableName: string
  /** 依赖的变量列表 */
  dependencies: string[]
  /** 被依赖的变量列表 */
  dependents: string[]
}

/**
 * 变量分析结果
 */
export interface VariableAnalysisResult {
  /** 总变量数 */
  totalVariables: number
  /** 已使用的变量数 */
  usedVariables: number
  /** 未使用的变量数 */
  unusedVariables: number
  /** 变量使用详情 */
  usageDetails: VariableUsage[]
  /** 未使用的变量列表 */
  unusedVariablesList: string[]
  /** 变量依赖关系 */
  dependencies: VariableDependency[]
}

/**
 * 分析流程中的变量使用情况
 * @param flowData 流程数据
 * @returns 变量分析结果
 */
export function analyzeVariableUsage(flowData: FlowData): VariableAnalysisResult {
  const variables = flowData.variables || []
  const nodes = flowData.nodes || []
  
  const usageDetails: VariableUsage[] = []
  const unusedVariablesList: string[] = []
  
  // 分析每个变量的使用情况
  variables.forEach(variable => {
    const usage = analyzeVariableInNodes(variable.name, nodes)
    
    if (usage.usageCount === 0) {
      unusedVariablesList.push(variable.name)
    } else {
      usageDetails.push(usage)
    }
  })
  
  // 分析变量依赖关系
  const dependencies = analyzeVariableDependencies(variables, nodes)
  
  return {
    totalVariables: variables.length,
    usedVariables: usageDetails.length,
    unusedVariables: unusedVariablesList.length,
    usageDetails,
    unusedVariablesList,
    dependencies
  }
}

/**
 * 分析单个变量在所有节点中的使用情况
 * @param variableName 变量名称
 * @param nodes 节点列表
 * @returns 变量使用信息
 */
function analyzeVariableInNodes(variableName: string, nodes: FlowNode[]): VariableUsage {
  const usedInNodes: VariableNodeUsage[] = []
  const modifiedInNodes: VariableNodeUsage[] = []
  let totalUsageCount = 0
  let isModified = false
  
  nodes.forEach(node => {
    const nodeUsage = analyzeVariableInNode(variableName, node)
    
    if (nodeUsage.count > 0) {
      totalUsageCount += nodeUsage.count
      
      if (nodeUsage.usageType === 'read') {
        usedInNodes.push(nodeUsage)
      } else if (nodeUsage.usageType === 'write') {
        modifiedInNodes.push(nodeUsage)
        isModified = true
      }
    }
  })
  
  return {
    variableName,
    usageCount: totalUsageCount,
    usedInNodes,
    isModified,
    modifiedInNodes
  }
}

/**
 * 分析变量在单个节点中的使用情况
 * @param variableName 变量名称
 * @param node 节点
 * @returns 节点中的变量使用信息
 */
function analyzeVariableInNode(variableName: string, node: FlowNode): VariableNodeUsage {
  const variablePattern = new RegExp(`{{\\s*${escapeRegExp(variableName)}\\s*}}`, 'g')
  const configStr = JSON.stringify(node.data.config || {})
  const matches = configStr.match(variablePattern) || []
  
  // 分析使用位置
  const usageLocation: string[] = []
  const config = node.data.config || {}
  
  // 递归查找变量使用位置
  findVariableInObject(config, variableName, '', usageLocation)
  
  // 判断使用类型（简化版本，实际可能需要更复杂的逻辑）
  let usageType: 'read' | 'write' = 'read'
  
  // 变量赋值节点通常是写入操作
  if (node.type === 'variable') {
    usageType = 'write'
  }
  
  return {
    nodeId: node.id,
    nodeName: node.data.label || `${node.type}节点`,
    nodeType: node.type,
    usageLocation,
    usageType,
    count: matches.length
  }
}

/**
 * 在对象中递归查找变量使用位置
 * @param obj 对象
 * @param variableName 变量名称
 * @param path 当前路径
 * @param locations 位置列表
 */
function findVariableInObject(obj: any, variableName: string, path: string, locations: string[]): void {
  if (typeof obj === 'string') {
    if (obj.includes(`{{${variableName}}}`)) {
      locations.push(path || 'root')
    }
  } else if (Array.isArray(obj)) {
    obj.forEach((item, index) => {
      findVariableInObject(item, variableName, `${path}[${index}]`, locations)
    })
  } else if (obj && typeof obj === 'object') {
    Object.keys(obj).forEach(key => {
      const newPath = path ? `${path}.${key}` : key
      findVariableInObject(obj[key], variableName, newPath, locations)
    })
  }
}

/**
 * 分析变量依赖关系
 * @param variables 变量列表
 * @param nodes 节点列表
 * @returns 变量依赖关系列表
 */
function analyzeVariableDependencies(variables: FlowVariable[], nodes: FlowNode[]): VariableDependency[] {
  const dependencies: VariableDependency[] = []
  
  variables.forEach(variable => {
    const deps: string[] = []
    const dependents: string[] = []
    
    // 分析该变量的默认值是否依赖其他变量
    if (variable.defaultValue && typeof variable.defaultValue === 'string') {
      const referencedVars = extractVariableReferences(variable.defaultValue)
      deps.push(...referencedVars)
    }
    
    // 分析其他变量是否依赖该变量
    variables.forEach(otherVar => {
      if (otherVar.name !== variable.name && otherVar.defaultValue && typeof otherVar.defaultValue === 'string') {
        const referencedVars = extractVariableReferences(otherVar.defaultValue)
        if (referencedVars.includes(variable.name)) {
          dependents.push(otherVar.name)
        }
      }
    })
    
    dependencies.push({
      variableName: variable.name,
      dependencies: [...new Set(deps)], // 去重
      dependents: [...new Set(dependents)] // 去重
    })
  })
  
  return dependencies
}

/**
 * 从字符串中提取变量引用
 * @param text 文本
 * @returns 引用的变量名列表
 */
function extractVariableReferences(text: string): string[] {
  const variablePattern = /{{\\s*([^}]+)\\s*}}/g
  const matches: string[] = []
  let match
  
  while ((match = variablePattern.exec(text)) !== null) {
    matches.push(match[1].trim())
  }
  
  return matches
}

/**
 * 转义正则表达式特殊字符
 * @param string 字符串
 * @returns 转义后的字符串
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 获取变量使用统计摘要
 * @param analysisResult 分析结果
 * @returns 统计摘要
 */
export function getVariableUsageSummary(analysisResult: VariableAnalysisResult): string {
  const { totalVariables, usedVariables, unusedVariables } = analysisResult
  const usageRate = totalVariables > 0 ? Math.round((usedVariables / totalVariables) * 100) : 0
  
  return `共 ${totalVariables} 个变量，已使用 ${usedVariables} 个（${usageRate}%），未使用 ${unusedVariables} 个`
}

/**
 * 检查变量名是否冲突
 * @param variables 变量列表
 * @returns 冲突的变量名列表
 */
export function checkVariableNameConflicts(variables: FlowVariable[]): string[] {
  const nameCount: Record<string, number> = {}
  const conflicts: string[] = []
  
  variables.forEach(variable => {
    nameCount[variable.name] = (nameCount[variable.name] || 0) + 1
  })
  
  Object.keys(nameCount).forEach(name => {
    if (nameCount[name] > 1) {
      conflicts.push(name)
    }
  })
  
  return conflicts
}
