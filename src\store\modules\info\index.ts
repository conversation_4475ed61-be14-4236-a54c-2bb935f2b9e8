import {defineStore} from 'pinia'

export const infoStore = defineStore('infoStore', {
	state: () => {
		// 从 sessionStorage 初始化 breadcrumb
		const storedBreadcrumb = sessionStorage.getItem('breadcrumb');
		return {
			loading: false,
			userInfo: {
				accessToken: '',
				userId: '12345',
				roleId: '',
				nickName: '',
			},
			ToolLoding: {
				loding: false,
				current: 0,
			},
			breadcrumb: storedBreadcrumb ? JSON.parse(storedBreadcrumb) : []
		};
	},
	getters: {},

	actions: {
		clearToolLoding() {

			this.ToolLoding = {
				loding: false,
				current: 0,
			}
		},
		updateToolLoding(userInfo: any) {
			console.log(userInfo)
			this.ToolLoding = {...this.ToolLoding, ...userInfo}
		},
		setloading(val: any) {
			this.loading = val
		},
		setuserInfo(val: any) {
			this.userInfo = val
		},
		setBreadcrumb(val: any) {
			this.breadcrumb = val
			// 更新 sessionStorage
			sessionStorage.setItem('breadcrumb', JSON.stringify(val));
		},
	},
})
