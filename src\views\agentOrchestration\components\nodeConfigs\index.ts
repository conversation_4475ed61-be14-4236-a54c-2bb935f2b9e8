// 统一导出所有节点配置组件
import { defineAsyncComponent } from 'vue';
import type { NodeType, NodeConfigComponentMap } from './types';

// 动态导入配置组件，实现懒加载
export const StartNodeConfig = defineAsyncComponent(() => import('./StartNodeConfig.vue'));
export const LLMNodeConfig = defineAsyncComponent(() => import('./LLMNodeConfig.vue'));
export const EndNodeConfig = defineAsyncComponent(() => import('./EndNodeConfig.vue'));
export const QuestionClassifierNodeConfig = defineAsyncComponent(() => import('./QuestionClassifierNodeConfig.vue'));
export const KnowledgeNodeConfig = defineAsyncComponent(() => import('./KnowledgeNodeConfig.vue'));
export const VariableNodeConfig = defineAsyncComponent(() => import('./VariableNodeConfig.vue'));
export const ConditionNodeConfig = defineAsyncComponent(() => import('./ConditionNodeConfig.vue'));
export const AggregationNodeConfig = defineAsyncComponent(() => import('./AggregationNodeConfig.vue'));
export const APINodeConfig = defineAsyncComponent(() => import('./APINodeConfig.vue'));

// 节点类型到组件的映射
export const nodeConfigComponentMap: Record<string, any> = {
  'start': StartNodeConfig,
  'llm': LLMNodeConfig,
  'end': EndNodeConfig,
  'question-classifier': QuestionClassifierNodeConfig,
  'knowledge': KnowledgeNodeConfig,
  'variable': VariableNodeConfig,
  'condition': ConditionNodeConfig,
  'aggregation': AggregationNodeConfig,
  'api': APINodeConfig,
  'questionClassifier': QuestionClassifierNodeConfig, // 兼容新的问题分类器节点类型
};

// 支持的节点类型列表
export const supportedNodeTypes = Object.keys(nodeConfigComponentMap);

// 根据节点类型获取对应的配置组件
export const getNodeConfigComponent = (nodeType: string) => {
  return nodeConfigComponentMap[nodeType] || null;
};

// 检查节点类型是否支持配置
export const isNodeConfigurable = (nodeType: string): boolean => {
  return nodeType in nodeConfigComponentMap;
};

// 导出类型
export * from './types';

// 导出 composables
export * from './composables/useNodeConfig';
export * from './composables/useVariableSelector';
export * from './composables/useModelConfig';
export * from './composables/useFormValidation';
