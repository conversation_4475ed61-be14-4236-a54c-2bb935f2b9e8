<template>
  <div>
    <div class="setcenter">
      <header>历史对话</header>
      <div>
        <n-select
          size="large"
          placeholder="请选择智能体"
          :options="options"
          :filterable="true"
          :filter="defaultFilterOption"
          :on-update:value="changeListtypefun"
        />
      </div>
      <n-spin :show="spinshow">
        <n-collapse
          arrow-placement="right"
          :default-expanded-names="'1'"
          accordion
        >
          <n-collapse-item title="置顶" name="1">
            <div
              v-for="(item, index) of topUpList"
              :key="index"
              class="historyrow text-[#0C0D0D] relative gap-2 px-3 py-3 break-all rounded-xl cursor-pointer hover:bg-[#125eff0d] group dark:hover:bg-[#24272e]"
              :class="
                item.ischeck && ['bg-[#FFF]', 'shadow-[0_0_3px_0_#0000000f]']
              "
              @click="handleSelect(item)"
            >
              <div class="flex justify-between items-center">
                <div class="titleleft">
                  <div class="toolIcon">
                    <img :src="item.icon" />
                  </div>
                  <n-ellipsis style="width: 600px" class="historytitle">
                    {{ item.title }}
                  </n-ellipsis>
                </div>
                <div class="titleright relative break-all">
                  <div class="toolname">{{ item.agentName || '智能体名称' }}</div>
                  <div class="ellipsisbox">
                    <div class="ellipsis">
                      <n-popover
                        placement="right"
                        :show-arrow="false"
                        trigger="manual"
                        :show="item.showPopover"
                      >
                        <template #trigger>
                          <div
                            size="small"
                            @click.stop="changeshowPopover(item)"
                            style="grid-area: 3 / 3 / 4 / 4"
                          >
                            <img src="../../assets/diandian.png">
                          </div>
                        </template>
                        <div>
                          <div
                            class="historyOperate"
                            @click="setTopUpfun(item)"
                            v-if="item.topUp == 0"
                          >
                            <img :src="topUpIcon" />置顶
                          </div>
                          <div
                            class="historyOperate"
                            @click="setTopUpfun(item)"
                            v-else
                          >
                            <img :src="noTopUpIcon" />取消置顶
                          </div>
                          <div class="historyOperate" @click="changeedit(item)">
                            <img :src="renameIcon" />重命名
                          </div>
                          <div
                            class="historyOperate"
                            @click="delHistoryfun(item)"
                          >
                            <img :src="delIcon" /> 删除
                          </div>
                        </div>
                      </n-popover>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div class="firstAnswer" v-if="item.firstAnswer">
                  {{ item.firstAnswer }}
                </div>
              </div>
            </div>
          </n-collapse-item>
        </n-collapse>

        <n-divider />
        <div class="datatype">今天</div>
        <div
          v-for="(item, index) of todayList"
          :key="index"
          class="historyrow text-[#0C0D0D] relative gap-2 px-3 py-3 break-all rounded-xl cursor-pointer hover:bg-[#125eff0d] group dark:hover:bg-[#24272e]"
          :class="item.ischeck && ['bg-[#FFF]', 'shadow-[0_0_3px_0_#0000000f]']"
          @click="handleSelect(item)"
        >
          <div class="flex justify-between items-center">
            <div class="titleleft">
              <div class="toolIcon">
                <img :src="item.icon" />
              </div>
              <n-ellipsis style="width: 600px" class="historytitle">
                {{ item.title }}
              </n-ellipsis>
            </div>
            <div class="titleright relative break-all">
              <div class="toolname">{{ item.agentName || '智能体名称' }}</div>
              <div class="ellipsisbox">
                <div class="ellipsis">
                  <n-popover
                    placement="right"
                    trigger="manual"
                    :show-arrow="false"
                    :show="item.showPopover"
                  >
                    <template #trigger>
                      <div
                        size="small"
                        @click.stop="changeshowPopover(item)"
                        style="grid-area: 3 / 3 / 4 / 4"
                      >
                        <img src="../../assets/diandian.png">
                      </div>
                    </template>
                    <div>
                      <div
                        class="historyOperate"
                        @click="setTopUpfun(item)"
                        v-if="item.topUp == 0"
                      >
                        <img :src="topUpIcon" />置顶
                      </div>
                      <div
                        class="historyOperate"
                        @click="setTopUpfun(item)"
                        v-else
                      >
                        <img :src="noTopUpIcon" />取消置顶
                      </div>
                      <div class="historyOperate" @click="changeedit(item)">
                        <img :src="renameIcon" />重命名
                      </div>
                      <div class="historyOperate" @click="delHistoryfun(item)">
                        <img :src="delIcon" /> 删除
                      </div>
                    </div>
                  </n-popover>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="firstAnswer" v-if="item.firstAnswer">
              {{ item.firstAnswer }}
            </div>
          </div>
        </div>

        <div class="datatype">近7天</div>
        <div
          v-for="(item, index) of lastSevenDayList"
          :key="index"
          class="historyrow text-[#0C0D0D] relative gap-2 px-3 py-3 break-all rounded-xl cursor-pointer hover:bg-[#125eff0d] group dark:hover:bg-[#24272e]"
          :class="item.ischeck && ['bg-[#FFF]', 'shadow-[0_0_3px_0_#0000000f]']"
          @click="handleSelect(item)"
        >
          <div class="flex justify-between items-center">
            <div class="titleleft">
              <div class="toolIcon">
                <img :src="item.icon" />
              </div>
              <n-ellipsis style="width: 600px" class="historytitle">
                {{ item.title }}
              </n-ellipsis>
            </div>
            <div class="titleright relative break-all">
              <div class="toolname">{{ item.agentName || '智能体名称' }}</div>
              <div class="ellipsisbox">
                <div class="ellipsis">
                  <n-popover
                    placement="right"
                    trigger="manual"
                    :show-arrow="false"
                    :show="item.showPopover"
                  >
                    <template #trigger>
                      <div
                        size="small"
                        @click.stop="changeshowPopover(item)"
                        style="grid-area: 3 / 3 / 4 / 4"
                      >
                        <img src="../../assets/diandian.png">
                      </div>
                    </template>
                    <div>
                      <div
                        class="historyOperate"
                        @click="setTopUpfun(item)"
                        v-if="item.topUp == 0"
                      >
                        <img :src="topUpIcon" />置顶
                      </div>
                      <div
                        class="historyOperate"
                        @click="setTopUpfun(item)"
                        v-else
                      >
                        <img :src="noTopUpIcon" />取消置顶
                      </div>
                      <div class="historyOperate" @click="changeedit(item)">
                        <img :src="renameIcon" />重命名
                      </div>
                      <div class="historyOperate" @click="delHistoryfun(item)">
                        <img :src="delIcon" /> 删除
                      </div>
                    </div>
                  </n-popover>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="firstAnswer" v-if="item.firstAnswer">
              {{ item.firstAnswer }}
            </div>
          </div>
        </div>
        <div class="datatype">更早</div>
        <div
          v-for="(item, index) of earlierDayList"
          :key="index"
          class="historyrow text-[#0C0D0D] relative gap-2 px-3 py-3 break-all rounded-xl cursor-pointer hover:bg-[#125eff0d] group dark:hover:bg-[#24272e]"
          :class="item.ischeck && ['bg-[#FFF]', 'shadow-[0_0_3px_0_#0000000f]']"
          @click="handleSelect(item)"
        >
          <div class="flex justify-between items-center">
            <div class="titleleft">
              <div class="toolIcon">
                <img :src="item.icon" />
              </div>
              <n-ellipsis style="width: 600px" class="historytitle">
                {{ item.title }}
              </n-ellipsis>
            </div>
            <div class="titleright relative break-all">
              <div class="toolname">{{ item.agentName || '智能体名称' }}</div>
              <div class="ellipsisbox">
                <div class="ellipsis">
                  <n-popover
                    placement="right"
                    :show-arrow="false"
                    trigger="manual"
                    :show="item.showPopover"
                  >
                    <template #trigger>
                      <div
                        size="small"
                        @click.stop="changeshowPopover(item)"
                        style="grid-area: 3 / 3 / 4 / 4"
                      >
                        <img src="../../assets/diandian.png">
                      </div>
                    </template>
                    <div>
                      <div
                        class="historyOperate"
                        @click="setTopUpfun(item)"
                        v-if="item.topUp == 0"
                      >
                        <img :src="topUpIcon" />置顶
                      </div>
                      <div
                        class="historyOperate"
                        @click="setTopUpfun(item)"
                        v-else
                      >
                        <img :src="noTopUpIcon" />取消置顶
                      </div>
                      <div class="historyOperate" @click="changeedit(item)">
                        <img :src="renameIcon" />重命名
                      </div>
                      <div class="historyOperate" @click="delHistoryfun(item)">
                        <img :src="delIcon" /> 删除
                      </div>
                    </div>
                  </n-popover>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="firstAnswer" v-if="item.firstAnswer">
              {{ item.firstAnswer }}
            </div>
          </div>
        </div>
      </n-spin>
    </div>
  </div>
</template>
<script setup>
import { computed, defineEmits, onMounted, ref, watch, h } from "vue";
import { useRoute, useRouter } from "vue-router";

import {
  NInput,
  NPopover,
  NEllipsis,
  useMessage,
  useDialog,
  NSelect,
  NCollapse,
  NCollapseItem,
  NDivider,
  NButton,
  NSpin,
} from "naive-ui";
import {
  getHistorylist,
  delHistory,
  updateHistory,
  getHistoryMsg,
} from "@/api/index";
import { useChatStore, useToolsStore } from "@/store";

import delIcon from "@/assets/chat/delIcon.png";
import noTopUpIcon from "@/assets/chat/noTopUpIcon.png";
import topUpIcon from "@/assets/chat/topUpIcon.png";
import renameIcon from "@/assets/chat/renameIcon.png";
import icon1 from '@/assets/applicationPage/icon1.png'

import { useToolboxPageStore } from "@/store/modules/toolboxPage";
import { getAgentlistAll } from "@/api/workShop";
import { getpictureDictTextByCodeValue } from "@/utils/microFrontEnd";

const ToolsStore = useToolsStore();
const router = useRouter();
const message = useMessage();
const dialog = useDialog();
const chatStore = useChatStore();

var agentId = ref(null); // 选中的值，用于绑定 v-model:value

var topUpList = ref([]); // 置顶列表
var todayList = ref([]); // 今天列表
var lastSevenDayList = ref([]); // 最近7天列表
var earlierDayList = ref([]); // 更早的列表
var spinshow = ref(false); // 加载loading
var isrefreshHistory = computed(() => chatStore.refreshHistory);
watch(isrefreshHistory, () => {
  if (isrefreshHistory.value) {
    setTimeout(() => {
      getList();
    }, 100);
  }
});
// 移除原有定义
// var applicationData=ref([])
const toolboxPageStore = useToolboxPageStore();
const applicationData = computed(
  () => toolboxPageStore.toolboxData.applicationList
);
var options = ref([]);
// 定义默认过滤逻辑
function defaultFilterOption(input, option) {
  console.log(input, option);
  return option.label.toLowerCase().includes(input.toLowerCase());
}

function changeListtypefun(val) {
  console.log(val);
  agentId.value = val;
  getList();
}
function gettooldetailfun() {
  getAgentlistAll({ status: 1 }).then((res) => {
    console.log(res);
    // res.data.forEach((item) => {
    //   item.iconUrl = getpictureDictTextByCodeValue(
    //     "profile_picture",
    //     item.icon
    //   );
    // });
    options.value = [
      {
        label: "全部",
        value: "",
      },
      ...res.data.map((item) => ({
        label: item.name,
        value: item.id,
      })),
    ];
  });
}
function getList() {
  spinshow.value = true;
  getHistorylist({ agentId: agentId.value }).then((res) => {
    console.log(res, "res");

    spinshow.value = false;

    if (isrefreshHistory.value) {
      chatStore.setrefreshHistory(false);
    }
    res.data.topUpList.forEach((item) => {
      item.showPopover = false;
      item.isEdit = false;
      item.icon = icon1
    });
    res.data.lastSevenDayList.forEach((item) => {
      item.showPopover = false;
      item.isEdit = false;
      item.icon = icon1

    });
    res.data.earlierDayList.forEach((item) => {
      item.showPopover = false;
      item.isEdit = false;
      item.icon = icon1
  
    });
    res.data.todayList.forEach((item) => {
      item.showPopover = false;
      item.isEdit = false;
      item.icon = icon1

    });
    topUpList.value = res.data.topUpList; // 置顶列表
    todayList.value = res.data.todayList; //今天列表
    lastSevenDayList.value = res.data.lastSevenDayList; // 最近7天列表
    earlierDayList.value = res.data.earlierDayList; // 更早的列表
  });
}
function changeshowPopover(item) {
  topUpList.value.forEach((items) => {
    if (item.id != items.id) {
      items.showPopover = false;
    } else {
      items.showPopover = !items.showPopover;
    }
  });
  todayList.value.forEach((items) => {
    if (item.id != items.id) {
      items.showPopover = false;
    } else {
      items.showPopover = !items.showPopover;
    }
  });
  lastSevenDayList.value.forEach((items) => {
    if (item.id != items.id) {
      items.showPopover = false;
    } else {
      items.showPopover = !items.showPopover;
    }
  });
  earlierDayList.value.forEach((items) => {
    if (item.id != items.id) {
      items.showPopover = false;
    } else {
      items.showPopover = !items.showPopover;
    }
  });
}
function changeedit(item) {
  changeshowPopover(item);
  var updatetitle = "";
  dialog.warning({
    title: "编辑对话名称",
    content: () =>
      h(NInput, {
        "default-value": item.title,
        "on-update:value": (value) => {
          updatetitle = value;
        },
      }),
    positiveText: "确定",
    negativeText: "取消",
    draggable: true,
    positiveButtonProps: {
      class: "delbtn",
    },
    onPositiveClick: () => {
      if (updatetitle) {
        updateHistory({ id: item.id, title: updatetitle }).then((res) => {
          getList();
          chatStore.setnewAddHistory(true);
        });
      }
    },
    onNegativeClick: () => {},
  });
}
function delHistoryfun(item) {
  if (item.ischeck) {
    message.warning("不可删除选中的历史记录");
    changeshowPopover(item);
    return;
  }
  changeshowPopover(item);
  dialog.warning({
    title: "确定删除对话吗?",
    content: "删除后，聊天记录将不可恢复。",
    positiveText: "删除",
    negativeText: "取消",
    draggable: true,
    positiveButtonProps: {
      class: "delbtn",
    },
    onPositiveClick: () => {
      delHistory({ id: item.id }).then((res) => {
        console.log(res);
        getList();
        chatStore.setnewAddHistory(true);
      });
    },
    onNegativeClick: () => {},
  });
}

function setTopUpfun(item) {
  item.isEdit = false;
  updateHistory({ id: item.id, topUp: item.topUp == "0" ? "1" : "0" }).then(
    (res) => {
      console.log(res);
      getList();
      chatStore.setnewAddHistory(true);
    }
  );
}

async function handleSelect(item) {

  // let res = await getHistoryMsg({ conversationId: item.id });
  // chatStore.editHistory(item.id, processData(res.data));
  // chatStore.setActive(item.id);

  const toolitem = {
    name: item.agentName || '智能体名称',
    title: item.openingWords,
    des: item.description,
    agentId: item.agentId,
    openingQuestionArr: [],
    icon: icon1,
    modelSessionId: item.modelId,
    modelTemp: item.modelTemp,
    maxLength: item.maxLength,
    promptTemplate: item.promptTemplate,
  }
  toolitem.openingQuestionArr = toolitem.openingQuestionArr.concat(JSON.parse(item.preQuestions || '[]'))
  ToolsStore.updateToolInfo(toolitem)
  // let res = await getHistoryMsg({ conversationId: item.id });
  // chatStore.editHistory(item.id, processData(res.data));
  chatStore.activeHistory(item.id);
  // chatStore.setnewAddHistory(true);
  
  router.push({
    path: '/tankChat',
    query: {
      id: item.agentId,
      uuid: item.id,
    },
  })
}
const processData = (data) => {
  var newData = [];

  data.forEach((subItem) => {
    // 为每个对象添加 uuid 字段，其值等于 id 字段的值
    const newItemone = {
      ...subItem,
      uuid: subItem.id,
      // 聊天记录
      text: subItem.question,
      // text: subItem.sendUserType === '0' && subItem.status === '1' && subItem.answer ? JSON.parse(subItem.answer).answer : subItem.answer,
      inversion: true,
      // answerList: subItem.sendUserType === '1' ? [] : JSON.parse(subItem.answer).answerList,
      // 搜索来源
      answerList:
        subItem.sendUserType === "0"
          ? subItem.answer
            ? subItem.status === "1"
              ? subItem.answer
              : []
            : []
          : [],
      endstatus: "1",
      category: subItem.category + "",
      conversationOptions: {
        conversationId: subItem.id,
      },
      requestOptions: {
        prompt: subItem.question,
        options: {
          conversationId: subItem.id,
        },
      },
    };

    const newItemtwo = {
      ...subItem,
      uuid: subItem.id,
      // 聊天记录
      text: subItem.answer,
      // text: subItem.sendUserType === '0' && subItem.status === '1' && subItem.answer ? JSON.parse(subItem.answer).answer : subItem.answer,
      inversion: false,
      // answerList: subItem.sendUserType === '1' ? [] : JSON.parse(subItem.answer).answerList,
      // 搜索来源
      answerList:
        subItem.sendUserType === "0"
          ? subItem.answer
            ? subItem.status === "1"
              ? subItem.answer
              : []
            : []
          : [],
      endstatus: "1",
      category: subItem.category + "",
      conversationOptions: {
        conversationId: subItem.id,
      },
      requestOptions: {
        prompt: subItem.question,
        options: {
          conversationId: subItem.id,
        },
      },
    };
    newData.push(newItemone);
    newData.push(newItemtwo);
  });
  return newData;
};
onMounted(() => {
  gettooldetailfun();
  getList();
});
</script>
<style scoped lang="less">
.setcenter {
  width: 60%;
  margin: 0 auto;
}
header {
  height: 33px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 24px;
  color: #323233;
  letter-spacing: 0;
  text-align: center;
  line-height: 33px;
  margin-top: 46px;
  margin-bottom: 24px;
}
/deep/ .n-collapse-item__header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 28px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 20px;
  color: #323233;
  letter-spacing: 0;
  margin-top: 18px;
}
.titleright,
.titleleft {
  display: flex;
  align-items: center;
}
.toolIcon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}
/deep/.historytitle {
  height: 26px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #0c0d0d;
  letter-spacing: 0;
  line-height: 26px;
}
.historyrow :hover {
  .ellipsis {
    display: inline-block;
  }
}
.toolname {
  height: 22px;
  background: #e6f4ff;
  border: 1px solid #bae0ff;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #1677ff;
  line-height: 22px;
  padding-left: 8px;
  padding-right: 8px;
}
.ellipsisbox {
  width: 22px;
}
.ellipsis {
  display: none;
  margin-left: 10px;
  /deep/ .n-popover {
    width: 122px !important;
  }
}
.firstAnswer {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #606266;
  letter-spacing: 0;
  line-height: 26px;
  height: 52px;
  overflow: hidden;
}
.historyOperate {
  display: flex;
  align-items: center;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #323233;
  cursor: pointer; // 添加这一行让鼠标滑过显示小手
  width: 86px;
  line-height: 23px;
  img {
    width: 12px;
    height: 12px;
    margin-right: 10px;
  }
}
.historyOperate:hover {
  background-color: #f0f0f0; /* 可以根据需要调整背景色 */
}
.datatype {
  height: 26px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #7c818f;
  letter-spacing: 0;
  line-height: 26px;
}
</style>