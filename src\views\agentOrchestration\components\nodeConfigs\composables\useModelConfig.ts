import { ref, onMounted } from 'vue';
import { agentParam, museModels, optimize } from "@/api/workShop";
import { useMessage } from 'naive-ui';
import type { SelectOption, ModelConfig } from '../types';

export function useModelConfig() {
  const message = useMessage();

  // 模型选项
  const modelOptions = ref<SelectOption[]>([
    { label: "GPT-3.5-turbo", value: "gpt-3.5-turbo" },
    { label: "GPT-4", value: "gpt-4" },
    { label: "Claude-3-sonnet", value: "claude-3-sonnet" },
  ]);

  // 模型参数选项
  const modelParameterOptions = ref<SelectOption[]>([
    { label: "创意", value: "0" },
    { label: "平衡", value: "1" },
    { label: "严肃", value: "2" },
    { label: "自定义", value: "3" },
  ]);

  // 智能体参数
  const agentParams = ref<any>(null);

  // 模型配置弹窗状态
  const modelParameterShow = ref(false);

  // 模型参数表单数据
  const modelParameterformData = ref({
    style: "",
    temperature: "",
    topP: "",
    maxTokens: "",
  });

  // 模型参数表单引用
  const modelParameterformRef = ref();

  // 处理模型变更
  const handleModelChange = (value: string, formData: any) => {
    console.log(value);
    const model = modelOptions.value.find((item: any) => {
      return item.value === value;
    });
    if (model && formData.config.modelConfig) {
      formData.config.modelConfig.modelName = model.label;
    }
  };

  // 显示/隐藏模型参数配置弹窗
  const changemodelParameterShow = (status: boolean, formData?: any) => {
    if (status && formData?.config?.modelConfig?.completionParams) {
      modelParameterformData.value.temperature = formData.config.modelConfig.completionParams.temperature;
      modelParameterformData.value.maxTokens = formData.config.modelConfig.completionParams.maxTokens;
      modelParameterformData.value.topP = formData.config.modelConfig.completionParams.topP;
      modelParameterformData.value.style = formData.config.modelConfig.completionParams.style;
    }
    modelParameterShow.value = status;
  };

  // 保存模型参数配置
  const changemodelParameterfun = async (formData: any) => {
    await modelParameterformRef.value?.validate();
    
    if (formData.config.modelConfig?.completionParams) {
      formData.config.modelConfig.completionParams.temperature = modelParameterformData.value.temperature;
      formData.config.modelConfig.completionParams.maxTokens = modelParameterformData.value.maxTokens;
      formData.config.modelConfig.completionParams.topP = modelParameterformData.value.topP;
      formData.config.modelConfig.completionParams.style = modelParameterformData.value.style;
    }
    
    changemodelParameterShow(false);
  };

  // 更新模型参数
  const updatemodelParameterfun = (value: string) => {
    modelParameterformData.value.temperature = value;
  };

  // 初始化模型配置
  const initializeModelConfig = (config: any) => {
    if (!config.modelConfig) {
      config.modelConfig = {
        model: null,
        modelName: "",
        completionParams: {},
      };
      
      if (agentParams.value) {
        config.modelConfig.completionParams = {
          temperature: agentParams.value.model_style[0].dictKey,
          maxTokens: agentParams.value.model_max_length,
          topP: agentParams.value.model_topp,
          style: agentParams.value.model_style[0].dictKey,
        };
      }
    }
  };

  // 一键优化提示词
  const optimizing = async (formData: any) => {
    try {
      const res = await optimize({
        modelTemp: formData.config.modelConfig.completionParams.temperature,
        promptTemplate: formData.config.tishici,
        modelId: formData.config.modelConfig.model,
        maxLength: formData.config.modelConfig.completionParams.maxTokens,
      });
      console.log(res);
      message.success("优化成功");
      return res.data;
    } catch (e) {
      message.error("优化失败");
      throw e;
    }
  };

  // 初始化数据
  const initModelData = async () => {
    try {
      const [modelsRes, paramsRes] = await Promise.all([
        museModels(),
        agentParam()
      ]);
      
      console.log(paramsRes);
      modelParameterOptions.value = paramsRes.data.model_style;
      agentParams.value = paramsRes.data;
      
      modelOptions.value = modelsRes.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    } catch (error) {
      console.error("初始化模型数据失败:", error);
      message.error("初始化模型数据失败");
    }
  };

  return {
    modelOptions,
    modelParameterOptions,
    agentParams,
    modelParameterShow,
    modelParameterformData,
    modelParameterformRef,
    handleModelChange,
    changemodelParameterShow,
    changemodelParameterfun,
    updatemodelParameterfun,
    initializeModelConfig,
    optimizing,
    initModelData,
  };
}
