import {defineStore} from 'pinia'
import type {stretchoutState, stretchoutStorageInfos} from './helper'
import {getLocalState, setLocalState} from './helper'

export const usestretchoutStore = defineStore('stretchout-store', {
	state: (): stretchoutState => getLocalState(),
	actions: {
		setdocumentisshow(isShow: boolean) {
			this.stretchoutStorageInfo.documentisshow = isShow
			// this.recordState()
		},
		setconversationContentId(conversationContentId: string) {
			this.stretchoutStorageInfo.conversationContentId = conversationContentId
			if (conversationContentId) {
				this.stretchoutStorageInfo.documentisshow = true
			} else {
				// this.stretchoutStorageInfo.documentisshow = false;
			}
		},
		setContentId(id: any, flag: boolean, text: string) {
			this.stretchoutStorageInfo.conversationContentId = id
			this.stretchoutStorageInfo.lessonShow = flag
			this.stretchoutStorageInfo.lessonText = text
			this.stretchoutStorageInfo.documentisshow = false
		},
		setContentShow(flag: boolean) {
			this.stretchoutStorageInfo.documentisshow = flag
			this.stretchoutStorageInfo.lessonShow = flag
			this.stretchoutStorageInfo.lessonText = ''
		},
		updateToolInfo(stretchoutStorageInfo: Partial<stretchoutStorageInfos>) {
			this.stretchoutStorageInfo = {...this.stretchoutStorageInfo, ...stretchoutStorageInfo}
			// this.recordState()
		},
		recordState() {
			setLocalState(this.$state)
		},
	},
})
