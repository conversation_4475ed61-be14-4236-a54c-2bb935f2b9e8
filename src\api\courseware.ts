import { get, post } from '@/utils/request'
import type { AxiosProgressEvent, GenericAbortSignal } from "axios";

export function textbook<T = any>(data: any) {
	return get<T>({
		url: '/emind/textbook',
		data,
	})
}

export function agentById<T = any>(id: any) {
	return get<T>({
		url: `/emind/agent/${id}`,
	})
}

export function textbookById<T = any>(id: any) {
	return get<T>({
		url: `/emind/textbook/${id}`,
	})
}

export function generateSummarizeProblem<T = any>(data: any) {
	return post<T>({
		url: `/emind/agent/generate_summarize_problem`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function getGenerateProblem<T = any>(data: any) {
	return post<T>({
		url: `/emind/agent/generate_problem`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function addTextbook<T = any>(data: any) {
	return post<T>({
		url: '/emind/textbook',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

// export function checkFileMD5<T = any>(md5: string) {
// 	return get<T>({
// 		url: `/emind/textbook/check-md5/${md5}`,
// 	})
// }

export function addTextbookAnnotations<T = any>(data: any) {
	return post<T>({
		url: '/emind/textbookAnnotations',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function edlTextbookAnnotations<T = any>(id: any) {
	return post<T>({
		url: `/emind/textbookAnnotations/${id}/delete`,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function editTextbookAnnotations<T = any>(id: any, data: any) {
	return post<T>({
		url: `/emind/textbookAnnotations/${id}/update`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function knowledge<T = any>(data: any) {
	return get<T>({
		url: '/emind/textbookKnowledge/find_textbook_by_knowledge',
		data,
	})
}

export function fetchChatAPIProcess<T = any>(
	params: {
		signal?: GenericAbortSignal
		question: any
		conversationId: any
		category?: string
		agentId?: any
		modelId?: string
		modelTemp?: any
		maxLength?: any
		promptTemplate?: any
		prompt?: any
		onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
	},
) {
	// const settingStore = useSettingStore()
	// const authStore = useAuthStore()
	// console.log(params)
	const data: Record<string, any> = {
		question: params.question,
		conversationId: params.conversationId,
		category: params.category,
		agentId: params.agentId,
		modelId: params.modelId,
		modelTemp: params.modelTemp,
		maxLength: params.maxLength,
		promptTemplate: params.promptTemplate,
		prompt: params.prompt

	}

	return post<T>({
		url: '/emind/conversationContent/text_answers_by_prompt',
		data,
		signal: params.signal,
		onDownloadProgress: params.onDownloadProgress,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function conversationContentUpdate<T = any>(id: string, data: any) {
	return post<T>({
		url: `/emind/conversationContent/${id}/update`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function answersStop<T = any>(data: any) {
	return post<T>({
		url: `/emind/conversationContent/stop`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}
