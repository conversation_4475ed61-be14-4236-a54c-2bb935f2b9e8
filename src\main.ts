import '@babel/polyfill' // (一定要在最上面，第一行)
import 'core-js'
import initEucpBaseLib from 'eucp-baselib';
import { createApp } from 'vue'
import App from './App.vue'
import { setupI18n } from './locales'
import { setupAssets, setupScrollbarStyle } from './plugins'
import { setupStore } from './store'
import { setupRouter } from './router'
import wasmfilePath from 'eucp-baselib/eucp_baselib_bg.wasm?url'

async function bootstrap() {
	// 初始化eucp-baselib
	await initEucpBaseLib(wasmfilePath);

  const app = createApp(App)
  setupAssets()

  setupScrollbarStyle()

  setupStore(app)

  setupI18n(app)

  await setupRouter(app)

  app.mount('#app')
}

bootstrap()
