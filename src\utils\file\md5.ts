import md5 from 'md5';

/**
 * 计算文件的MD5值
 * @param file 文件对象
 * @returns Promise<string> 返回文件的MD5值
 */
export function calculateFileMD5(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = function (event) {
            try {
                const arrayBuffer = event.target?.result as ArrayBuffer;
                const uint8Array = new Uint8Array(arrayBuffer);

                // 将ArrayBuffer转换为字符串用于MD5计算
                let binary = '';
                for (let i = 0; i < uint8Array.length; i++) {
                    binary += String.fromCharCode(uint8Array[i]);
                }

                const hash = md5(binary);
                resolve(hash);
            } catch (error) {
                reject(error);
            }
        };

        reader.onerror = function () {
            reject(new Error('文件读取失败'));
        };

        reader.readAsArrayBuffer(file);
    });
}

/**
 * 快速计算文件MD5（基于文件内容的部分采样）
 * 适用于大文件的快速检查，精度较低但速度快
 * @param file 文件对象
 * @returns Promise<string> 返回文件的快速MD5值
 */
export function calculateFileMD5Fast(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const chunkSize = 1024 * 1024; // 1MB
        const chunks = Math.ceil(file.size / chunkSize);

        // 对于小文件，直接计算完整MD5
        if (file.size < chunkSize * 2) {
            return calculateFileMD5(file).then(resolve).catch(reject);
        }

        // 对于大文件，采样首尾和中间部分
        const samplesToRead = Math.min(chunks, 10); // 最多读取10个块
        const reader = new FileReader();
        let currentSample = 0;
        let combinedData = '';

        function readNextSample() {
            if (currentSample >= samplesToRead) {
                try {
                    const hash = md5(combinedData + file.size.toString() + file.lastModified.toString());
                    resolve(hash);
                } catch (error) {
                    reject(error);
                }
                return;
            }

            let start: number;
            if (currentSample === 0) {
                // 读取文件开头
                start = 0;
            } else if (currentSample === samplesToRead - 1) {
                // 读取文件末尾
                start = Math.max(0, file.size - chunkSize);
            } else {
                // 读取中间部分
                const step = file.size / (samplesToRead - 1);
                start = Math.floor(step * currentSample);
            }

            const end = Math.min(start + chunkSize, file.size);
            const blob = file.slice(start, end);

            reader.readAsArrayBuffer(blob);
        }

        reader.onload = function (event) {
            try {
                const arrayBuffer = event.target?.result as ArrayBuffer;
                const uint8Array = new Uint8Array(arrayBuffer);

                let binary = '';
                for (let i = 0; i < uint8Array.length; i++) {
                    binary += String.fromCharCode(uint8Array[i]);
                }

                combinedData += binary;
                currentSample++;
                readNextSample();
            } catch (error) {
                reject(error);
            }
        };

        reader.onerror = function () {
            reject(new Error('文件读取失败'));
        };

        readNextSample();
    });
}
