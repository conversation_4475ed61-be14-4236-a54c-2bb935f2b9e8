<template>
  <div class="execution-log-container">
    <!-- 日志指示器 -->
    <div 
      v-if="showIndicator && lastExecution"
      class="log-indicator"
      :class="indicatorClass"
      @click="toggleLogPanel"
      @mouseenter="showTooltip = true"
      @mouseleave="showTooltip = false"
      :title="indicatorTitle"
    >
      <div class="indicator-icon">
        <svg v-if="lastExecution.status === 'success'" width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
        </svg>
        <svg v-else-if="lastExecution.status === 'error'" width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
        <svg v-else-if="lastExecution.status === 'running'" width="12" height="12" viewBox="0 0 24 24" fill="currentColor" class="animate-spin">
          <path d="M12 2v4m0 12v4m10-10h-4M6 12H2m15.364-7.364l-2.828 2.828M9.464 9.464L6.636 6.636m12.728 12.728l-2.828-2.828M9.464 14.536l-2.828 2.828"/>
        </svg>
      </div>
      <div class="indicator-count" v-if="totalExecutions && totalExecutions > 1">
        {{ totalExecutions }}
      </div>
    </div>

    <!-- 悬停提示框 -->
    <div 
      v-if="showTooltip && lastExecution && !showLogPanel"
      class="log-tooltip"
      :class="tooltipPosition"
    >
      <div class="tooltip-header">
        <span class="status-badge" :class="lastExecution.status">
          {{ statusText(lastExecution.status) }}
        </span>
        <span class="execution-time">
          {{ formatDuration(lastExecution.duration) }}
        </span>
      </div>
      <div class="tooltip-content">
        <div class="tooltip-row">
          <span class="label">执行时间:</span>
          <span class="value">{{ formatTimestamp(lastExecution.timestamp) }}</span>
        </div>
        <div class="tooltip-row" v-if="lastExecution.output">
          <span class="label">输出:</span>
          <span class="value">{{ formatOutput(lastExecution.output) }}</span>
        </div>
        <div class="tooltip-row" v-if="lastExecution.error">
          <span class="label">错误:</span>
          <span class="value error-text">{{ lastExecution.error }}</span>
        </div>
      </div>
      <div class="tooltip-footer">
        点击查看详细日志
      </div>
    </div>

    <!-- 详细日志面板 -->
    <div 
      v-if="showLogPanel"
      class="log-panel"
      :class="panelPosition"
    >
      <div class="panel-header">
        <h4 class="panel-title">
          <span class="mr-2">📋</span>
          执行日志
        </h4>
        <div class="panel-actions">
          <button 
            class="action-btn"
            @click="clearLogs"
            title="清除日志"
            v-if="executionLogs.length > 0"
          >
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m3 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6"/>
            </svg>
          </button>
          <button 
            class="action-btn"
            @click="closeLogPanel"
            title="关闭"
          >
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <div class="panel-content">
        <div v-if="executionLogs.length === 0" class="empty-logs">
          <div class="empty-icon">📝</div>
          <p>暂无执行日志</p>
        </div>
        
        <div v-else class="log-list">
          <div 
            v-for="(log, index) in executionLogs" 
            :key="log.id"
            class="log-item"
            :class="log.status"
          >
            <div class="log-header">
              <div class="log-status">
                <span class="status-dot" :class="log.status"></span>
                <span class="status-text">{{ statusText(log.status) }}</span>
              </div>
              <div class="log-meta">
                <span class="duration">{{ formatDuration(log.duration) }}</span>
                <span class="timestamp">{{ formatTimestamp(log.timestamp) }}</span>
              </div>
            </div>
            
            <div class="log-details" v-if="log.output || log.error">
              <div v-if="log.output" class="log-output">
                <div class="detail-label">输出结果:</div>
                <pre class="detail-content">{{ JSON.stringify(log.output, null, 2) }}</pre>
              </div>
              
              <div v-if="log.error" class="log-error">
                <div class="detail-label">错误信息:</div>
                <div class="detail-content error-text">{{ log.error }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div 
      v-if="showLogPanel"
      class="log-overlay"
      @click="closeLogPanel"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { NodeExecutionLog } from '@/store/modules/orchestration'
import { useOrchestrationStore } from '@/store'
import { useMessage } from 'naive-ui'

interface Props {
  nodeId: string
  showIndicator?: boolean
  position?: 'top' | 'bottom' | 'left' | 'right'
}

const props = withDefaults(defineProps<Props>(), {
  showIndicator: true,
  position: 'top'
})

const message = useMessage()
const orchestrationStore = useOrchestrationStore()

// 响应式数据
const showTooltip = ref(false)
const showLogPanel = ref(false)

// 计算属性
const executionLogs = computed(() => orchestrationStore.getNodeExecutionLogs(props.nodeId))
const lastExecution = computed(() => executionLogs.value[0] || null)
const totalExecutions = computed(() => {
  const node = orchestrationStore.currentNodes.find(n => n.id === props.nodeId)
  return node?.data.totalExecutions || 0
})

const indicatorClass = computed(() => {
  if (!lastExecution.value) return 'status-idle'
  return `status-${lastExecution.value.status}`
})

const indicatorTitle = computed(() => {
  if (!lastExecution.value) return '暂无执行记录'
  const status = statusText(lastExecution.value.status)
  const time = formatTimestamp(lastExecution.value.timestamp)
  return `${status} - ${time}`
})

const tooltipPosition = computed(() => `tooltip-${props.position}`)
const panelPosition = computed(() => `panel-${props.position}`)

// 方法
const statusText = (status: string): string => {
  const statusMap = {
    idle: '空闲',
    running: '运行中',
    success: '成功',
    error: '失败'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

const formatDuration = (duration: number): string => {
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatOutput = (output: any): string => {
  if (typeof output === 'string') return output
  if (typeof output === 'object') {
    const str = JSON.stringify(output)
    return str.length > 100 ? str.substring(0, 100) + '...' : str
  }
  return String(output)
}

const toggleLogPanel = () => {
  showLogPanel.value = !showLogPanel.value
  showTooltip.value = false
}

const closeLogPanel = () => {
  showLogPanel.value = false
}

const clearLogs = () => {
  orchestrationStore.clearNodeExecutionLogs(props.nodeId)
  message.success('日志已清除')
}

// 点击外部关闭面板
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as Element
  if (showLogPanel.value && !target.closest('.log-panel') && !target.closest('.log-indicator')) {
    closeLogPanel()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.execution-log-container {
  position: relative;
  display: inline-block;
}

/* 日志指示器 */
.log-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 500;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  z-index: 10;
}

.log-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.log-indicator.status-idle {
  background: #d9d9d9;
  color: #666;
}

.log-indicator.status-running {
  background: #faad14;
  animation: pulse 2s infinite;
}

.log-indicator.status-success {
  background: #52c41a;
}

.log-indicator.status-error {
  background: #ff4d4f;
}

.indicator-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.indicator-count {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid white;
}

/* 悬停提示框 */
.log-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  max-width: 300px;
  white-space: normal;
}

.tooltip-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
}

.tooltip-bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
}

.tooltip-left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-right: 8px;
}

.tooltip-right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.status-badge.success {
  background: #52c41a;
  color: white;
}

.status-badge.error {
  background: #ff4d4f;
  color: white;
}

.status-badge.running {
  background: #faad14;
  color: white;
}

.execution-time {
  color: #ccc;
  font-size: 10px;
}

.tooltip-content {
  margin-bottom: 8px;
}

.tooltip-row {
  display: flex;
  margin-bottom: 4px;
}

.tooltip-row .label {
  color: #aaa;
  margin-right: 8px;
  min-width: 40px;
}

.tooltip-row .value {
  flex: 1;
  word-break: break-all;
}

.error-text {
  color: #ff7875;
}

.tooltip-footer {
  color: #888;
  font-size: 10px;
  text-align: center;
  border-top: 1px solid #333;
  padding-top: 6px;
}

/* 详细日志面板 */
.log-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.log-panel {
  position: fixed;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  width: 400px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.panel-top {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.panel-bottom {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.panel-left {
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.panel-right {
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  display: flex;
  align-items: center;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.empty-logs {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.log-list {
  space-y: 12px;
}

.log-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.log-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-item.success {
  border-left: 4px solid #52c41a;
}

.log-item.error {
  border-left: 4px solid #ff4d4f;
}

.log-item.running {
  border-left: 4px solid #faad14;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.success {
  background: #52c41a;
}

.status-dot.error {
  background: #ff4d4f;
}

.status-dot.running {
  background: #faad14;
}

.status-text {
  font-weight: 500;
  font-size: 14px;
}

.log-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #64748b;
}

.log-details {
  margin-top: 8px;
}

.log-output,
.log-error {
  margin-bottom: 8px;
}

.detail-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
}

.detail-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 120px;
  overflow-y: auto;
}

.log-error .detail-content {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

/* 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-panel {
    width: calc(100vw - 40px);
    max-width: 400px;
  }
  
  .log-tooltip {
    max-width: 250px;
  }
}
</style>
