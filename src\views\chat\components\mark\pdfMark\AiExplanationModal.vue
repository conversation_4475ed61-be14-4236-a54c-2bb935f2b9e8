<template>
  <div
    v-if="visible"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="close"
  >
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">AI智能解释</h3>
        <button
          @click="close"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-4 overflow-y-auto max-h-[60vh]">
        <!-- 选中的文本 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">选中的文本:</h4>
          <div class="bg-gray-100 p-3 rounded-lg text-sm">
            "{{ selectedText }}"
          </div>
        </div>

        <!-- AI解释结果 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">AI解释:</h4>
          <div class="bg-blue-50 p-4 rounded-lg">
            <div v-if="isLoading" class="flex items-center text-blue-600">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              正在分析文本内容...
            </div>
            <div v-else class="text-gray-800">
              <div v-html="formattedExplanation"></div>
            </div>
          </div>
        </div>

        <!-- 相关建议 -->
        <div v-if="suggestions.length > 0" class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">相关建议:</h4>
          <div class="space-y-2">
            <div
              v-for="(suggestion, index) in suggestions"
              :key="index"
              class="bg-green-50 p-3 rounded-lg text-sm text-green-800"
            >
              {{ suggestion }}
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2 pt-4 border-t border-gray-200">
          <button
            @click="copyExplanation"
            class="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>
              <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"/>
            </svg>
            复制解释
          </button>
          <button
            @click="regenerateExplanation"
            class="flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
            </svg>
            重新生成
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  visible: boolean
  selectedText: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'copy', text: string): void
}>()

const isLoading = ref(false)
const explanation = ref('')
const suggestions = ref<string[]>([])

// 格式化解释文本
const formattedExplanation = computed(() => {
  return explanation.value.replace(/\n/g, '<br>')
})

// 监听文本变化，自动生成解释
watch(() => props.selectedText, (newText) => {
  if (newText && props.visible) {
    generateExplanation(newText)
  }
}, { immediate: true })

// 模拟AI解释生成
const generateExplanation = async (text: string) => {
  isLoading.value = true
  explanation.value = ''
  suggestions.value = []

  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 根据文本内容生成模拟解释
    const mockExplanation = generateMockExplanation(text)
    explanation.value = mockExplanation.explanation
    suggestions.value = mockExplanation.suggestions
  } catch (error) {
    explanation.value = '抱歉，AI解释服务暂时不可用，请稍后再试。'
  } finally {
    isLoading.value = false
  }
}

// 生成模拟AI解释
const generateMockExplanation = (text: string) => {
  const explanations = {
    explanation: '',
    suggestions: [] as string[]
  }

  // 根据文本内容生成不同的解释
  if (text.includes('审核') || text.includes('审查')) {
    explanations.explanation = `这段文本涉及应用审核流程。在移动应用开发中，审核是确保应用质量和安全性的重要环节。审核过程通常包括代码检查、功能测试、安全性评估等多个方面。

审核的主要目的是：
1. 确保应用符合平台规范和政策
2. 检查应用的安全性和稳定性
3. 验证应用功能的完整性和正确性
4. 保护用户数据和隐私安全`

    explanations.suggestions = [
      '建议在提交审核前进行充分的内部测试',
      '确保应用符合相关平台的开发者指南',
      '准备详细的应用说明文档以加速审核过程'
    ]
  } else if (text.includes('测试') || text.includes('账号')) {
    explanations.explanation = `这段文本提到了测试账号的相关内容。测试账号是软件开发和应用审核过程中的重要工具，用于验证应用功能而不影响真实用户数据。

测试账号的作用包括：
1. 功能验证：确保所有功能正常工作
2. 数据隔离：避免测试数据污染生产环境
3. 审核支持：为审核人员提供便于测试的账号
4. 问题排查：帮助开发团队快速定位和解决问题`

    explanations.suggestions = [
      '为不同角色创建专门的测试账号',
      '定期更新测试账号的数据和权限',
      '确保测试账号的安全性和访问控制'
    ]
  } else if (text.includes('应用') || text.includes('app')) {
    explanations.explanation = `这段文本涉及移动应用相关内容。现代移动应用开发需要考虑多个方面，包括用户体验、性能优化、安全性、跨平台兼容性等。

移动应用开发的关键要素：
1. 用户界面设计：简洁直观的UI/UX设计
2. 性能优化：快速响应和流畅的用户体验
3. 数据安全：保护用户隐私和数据安全
4. 平台适配：确保在不同设备和系统上的兼容性`

    explanations.suggestions = [
      '关注用户反馈，持续改进应用体验',
      '定期更新应用以修复bug和添加新功能',
      '实施适当的分析工具来了解用户行为'
    ]
  } else {
    explanations.explanation = `这段文本包含了重要的信息内容。基于文本分析，这可能涉及技术文档、操作指南或相关说明。

文本分析要点：
1. 内容结构：文本的组织方式和逻辑结构
2. 关键信息：文本中的核心概念和重要细节
3. 上下文关联：与周围内容的关系和依赖
4. 实用价值：对读者的指导意义和应用价值

建议仔细阅读相关上下文以获得更完整的理解。`

    explanations.suggestions = [
      '结合上下文内容进行综合理解',
      '关注文本中的关键术语和概念',
      '如有疑问，建议查阅相关技术文档'
    ]
  }

  return explanations
}

// 重新生成解释
const regenerateExplanation = () => {
  generateExplanation(props.selectedText)
}

// 复制解释内容
const copyExplanation = async () => {
  const textToCopy = `选中文本: "${props.selectedText}"\n\nAI解释:\n${explanation.value}`

  try {
    await navigator.clipboard.writeText(textToCopy)
    emit('copy', textToCopy)
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = textToCopy
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    emit('copy', textToCopy)
  }
}

// 关闭模态框
const close = () => {
  emit('close')
}
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
