<template>
  <!-- 右侧滑出配置面板 -->
  <div
    class="config-panel-overlay"
    v-show="visible"
    @click="handleOverlayClick"
  >
    <div class="config-panel" :class="{ 'panel-open': visible }" @click.stop>
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-content">
          <div class="node-info">
            <div class="node-name flex">
              <img class="w-[18px] h-[18px]" :src="formData.icon" />
              {{ formData.label }}
            </div>
          </div>
          <div class="header-actions">
            <!-- 运行节点按钮 -->
            <button
              v-if="showRunButton"
              class="run-btn"
              :class="{ running: isRunning }"
              :disabled="isRunning"
              @click="handleRunNode"
              :title="isRunning ? '运行中...' : '运行节点'"
            >
              <svg
                v-if="!isRunning"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polygon points="5,3 19,12 5,21"></polygon>
              </svg>
              <svg
                v-else
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                class="animate-spin"
              >
                <path d="M21 12a9 9 0 11-6.219-8.56"></path>
              </svg>
            </button>
            
            <!-- 更多操作下拉菜单 -->
            <n-dropdown
              :options="moreOptions"
              @select="handleMoreAction"
              trigger="click"
              placement="bottom-end"
            >
              <button class="more-btn" title="更多操作">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <circle cx="5" cy="12" r="1"></circle>
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="19" cy="12" r="1"></circle>
                </svg>
              </button>
            </n-dropdown>
          </div>
        </div>
      </div>
      
      <div class="divider"></div>
      
      <!-- 面板内容 - 动态组件 -->
      <div class="panel-content" v-if="node">
        <n-scrollbar style="height: calc(100vh - 140px)">
          <n-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-placement="top"
            :show-feedback="true"
            :show-label="true"
          >
            <!-- 动态加载节点配置组件 -->
            <component
              :is="currentConfigComponent"
              v-if="currentConfigComponent"
              :node="node"
              :formData="formData"
              :visible="visible"
              @update:formData="handleFormDataUpdate"
              @save="handleSave"
              @run-node="handleRunNode"
            />
            
            <!-- 如果没有对应的配置组件，显示默认提示 -->
            <div v-else class="no-config-message">
              <n-empty description="该节点类型暂不支持配置" />
            </div>
          </n-form>
        </n-scrollbar>
      </div>

      <!-- 面板底部 -->
      <div class="panel-footer">
        <div class="footer-actions">
          <n-button @click="handleClose">取消</n-button>
          <n-button type="primary" @click="handleSave">保存配置</n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { 
  NDropdown, 
  NScrollbar, 
  NForm, 
  NButton, 
  NEmpty,
  useMessage,
  useDialog 
} from 'naive-ui';
import type { FlowNode } from "@/store/modules/orchestration";
import { useOrchestrationStore } from "@/store";
import { 
  getNodeConfigComponent, 
  isNodeConfigurable,
  useNodeConfig,
  useFormValidation 
} from './index';

// Props 和 Events
const props = defineProps<{
  show: boolean;
  node: FlowNode | null;
}>();

const emit = defineEmits<{
  "update:show": [value: boolean];
  save: [config: any];
}>();

// 基础逻辑
const message = useMessage();
const dialog = useDialog();
const orchestrationStore = useOrchestrationStore();

// 使用共享逻辑
const { isRunning, showRunButton, handleRunNode } = useNodeConfig(props);
const { formRef, getFormRules, validateForm } = useFormValidation();

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 表单数据
const formData = ref({
  label: "",
  description: "",
  config: {},
});

// 表单验证规则
const formRules = computed(() => {
  return props.node ? getFormRules(props.node.type) : {};
});

// 当前配置组件
const currentConfigComponent = computed(() => {
  if (!props.node) return null;
  return getNodeConfigComponent(props.node.type);
});

// 更多操作菜单选项
const moreOptions = [
  {
    label: "复制节点",
    key: "duplicate",
    icon: () => "📋",
  },
  {
    label: "删除节点",
    key: "delete",
    icon: () => "🗑️",
  },
];

// 监听节点变化，更新表单数据
watch(
  () => props.node,
  (newNode) => {
    if (newNode) {
      const config = { ...newNode.data.config } || {};
      
      formData.value = {
        label: newNode.data.label || "",
        icon: newNode.data.icon,
        description: newNode.data.description || "",
        config,
      };
    }
  },
  { immediate: true, deep: true }
);

// 处理表单数据更新
const handleFormDataUpdate = (newFormData: any) => {
  formData.value = { ...newFormData };
};

// 处理关闭
const handleClose = () => {
  visible.value = false;
};

// 处理遮罩点击
const handleOverlayClick = () => {
  handleClose();
};

// 处理保存
const handleSave = async () => {
  const isValid = await validateForm();
  if (!isValid) {
    message.error("请检查表单输入");
    return;
  }

  try {
    emit("save", formData.value);
    handleClose();
    message.success("节点配置保存成功");
  } catch (error) {
    message.error("保存失败");
  }
};

// 处理更多操作菜单
const handleMoreAction = (key: string) => {
  if (!props.node) return;

  switch (key) {
    case "delete":
      handleDeleteNode();
      break;
    case "duplicate":
      handleDuplicateNode();
      break;
  }
};

// 删除节点
const handleDeleteNode = () => {
  if (!props.node) return;

  if (props.node.type === "start" || props.node.type === "end") {
    message.warning("开始节点和结束节点不能删除");
    return;
  }

  dialog.warning({
    title: "删除确认",
    content: `确定要删除节点 "${props.node.data.label}" 吗？此操作不可撤销。`,
    positiveText: "确定删除",
    negativeText: "取消",
    onPositiveClick: () => {
      if (props.node) {
        orchestrationStore.removeNode(props.node.id);
        handleClose();
        message.success("节点删除成功");
      }
    },
  });
};

// 复制节点
const handleDuplicateNode = () => {
  if (!props.node) return;
  try {
    orchestrationStore.duplicateNode(props.node.id);
    message.success("节点复制成功");
  } catch (error) {
    console.error("节点复制失败:", error);
    message.error("节点复制失败");
  }
};
</script>

<style scoped lang="less">
// 这里可以复用原有的样式，或者提取到共享样式文件中
@import './styles/panel.less';

.no-config-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
