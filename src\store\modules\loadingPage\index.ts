import { defineStore } from 'pinia'
import type { loadingPageData, loadingPageState } from './helper'
import { defaultSetting, getLocalState, setLocalState } from './helper'

// 定义store
export const loadingPageStore = defineStore('loading-page-store', {
  state: (): loadingPageState => getLocalState(),
  actions: {
    // 重置loadingPageData
    resetLoadingPageData() {
      this.loadingPageData = defaultSetting().loadingPageData;
    },
    updateloadingData(data: Partial<loadingPageData>) {
      this.loadingPageData = { ...this.loadingPageData, ...data }
    },
    recordState() {
      setLocalState(this.$state)
    },
  },
})