<template>
  <div class="knowledge-node-config">
    <!-- 输入配置 -->
    <n-form-item label-placement="left" class="setHeight mb-[13px]">
      <template #label>
        <div class="rowstit"><span class="rowicon"></span> 输入</div>
      </template>
    </n-form-item>

    <n-form-item label-placement="left" class="setHeight mb-[8px]">
      <template #label>
        <div class="text-[#565756]">查询变量</div>
      </template>
      <div class="flex justify-end w-full text-[#C7C7C7]">文本</div>
    </n-form-item>

    <!-- 查询变量配置 -->
    <n-form-item path="config.jiansuoType" label-placement="left">
      <div class="w-[162px] mr-[14px] h-[38px]">
        <n-select
          v-model:value="formData.config.jiansuoType"
          :options="variableOptions"
          default-value="0"
          filterable
        />
      </div>
      <div class="w-full h-[38px]">
        <n-input
          v-if="formData.config.jiansuoType == '0'"
          v-model:value="formData.config.jiansuotypevalue"
          type="text"
          placeholder="请输入变量"
          maxlength="20"
          show-count
        />
        <AggregationSelector
          v-else
          v-model="formData.config.jiansuotypevalue"
          :options="aggregationOptions"
          placeholder="请选择变量"
          @change="handleAggregationChange"
        />
      </div>
    </n-form-item>

    <!-- 知识库配置 -->
    <n-form-item
      label-placement="left"
      class="setHeight mt-[24px] mb-[12px]"
    >
      <template #label>
        <div class="rowstit">知识库</div>
      </template>
      <div class="flex justify-end w-full">
        <img
          class="w-[16px] h-[16px] cursor-pointer"
          src="@/assets/agentOrchestration/yitupeizhi.png"
        />
        <img
          class="w-[16px] h-[16px] ml-[10px] cursor-pointer"
          src="@/assets/agentOrchestration/yituzhishi.png"
          @click="showKnowledgeBaseDialog"
        />
      </div>
    </n-form-item>

    <!-- 已选择的知识库列表 -->
    <div
      class="knowledgelist flex justify-between"
      v-for="(item, index) in formData.config.databases"
      :key="index"
    >
      {{ item.name }}
      <img
        class="w-[12px]"
        src="@/assets/agentOrchestration/yituzhishidel.png"
        @click="deleteKnowledge(index)"
      />
    </div>

    <!-- 输出配置 -->
    <n-form-item label-placement="left" class="setHeight mt-[12px]">
      <template #label>
        <div class="rowstit"><span class="rowicon"></span>输出</div>
      </template>
    </n-form-item>

    <n-form-item label-placement="left">
      <div class="w-full flex text-[#C7C7C7]">
        <div class="w-[50%]">名称</div>
        <div>数据类型</div>
      </div>
    </n-form-item>

    <n-form-item path="config.wenbenshuchu" label-placement="left">
      <div class="w-full flex text-[#565756] items-center">
        <div class="w-[50%]">
          <div class="w-[234px] h-[38px]">
            <n-input
              v-model:value="formData.config.wenbenshuchu"
              type="text"
              placeholder="请输入输出名称"
            />
          </div>
        </div>
        <div>文本</div>
      </div>
    </n-form-item>

    <!-- 知识库选择弹窗 -->
    <n-modal v-model:show="knowledgeBaseShow">
      <n-card
        class="modelParametercard"
        style="width: 864px"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div class="flex items-center justify-between h-[70px] text-[17px] font-medium text-[#1F1F1F] pl-[26px] pr-[23px] border-b-[1px] border-[#E5E5E5]">
          <span>添加知识库</span>
          <span @click="knowledgeBaseShow = false" class="cursor-pointer">×</span>
        </div>
        
        <!-- 搜索框 -->
        <div class="pl-[26px] pr-[23px] flex h-[38px] items-center mb-[20px] mt-[25.83px]">
          <div class="w-[678px]">
            <n-input
              v-model:value="searchknowledgeBase"
              placeholder="搜索知识库"
            />
          </div>
          <div class="btnparent w-[112px] h-[38px] ml-[16px]">
            <n-button type="info" color="#125EFF">新建知识库</n-button>
          </div>
        </div>
        
        <!-- 知识库列表 -->
        <n-checkbox-group v-model:value="knowledgeBasechecklist">
          <div class="pl-[26px] pr-[23px] max-h-[400px] overflow-y-auto">
            <div
              v-for="(item, index) in knowledgeBaseshow"
              :key="index"
              class="flex items-center justify-between h-[60px] border-b border-[#F0F0F0] last:border-b-0"
            >
              <div class="flex items-center">
                <n-checkbox :value="item.value" />
                <div class="ml-[12px]">
                  <div class="text-[16px] font-medium text-[#1F1F1F]">{{ item.name }}</div>
                  <div class="text-[14px] text-[#999999]">{{ item.size }}个文档</div>
                </div>
              </div>
            </div>
          </div>
        </n-checkbox-group>

        <template #footer>
          <div class="flex w-full justify-end pl-[26px] pr-[23px] pb-[27px] mt-[20px]">
            <div class="btnparent w-[80px] h-[36px]">
              <n-button @click="knowledgeBaseShow = false">取消</n-button>
            </div>
            <div class="btnparent w-[80px] h-[36px] ml-[16px]">
              <n-button @click="saveKnowledgeBase" type="info" color="#125EFF">
                保存
              </n-button>
            </div>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { 
  NFormItem, 
  NSelect, 
  NInput, 
  NModal, 
  NCard, 
  NButton,
  NCheckboxGroup,
  NCheckbox 
} from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import { useNodeConfig } from './composables/useNodeConfig';
import type { NodeConfigProps, NodeConfigEvents, KnowledgeBase } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const { 
  variableOptions,
  aggregationOptions, 
  updateAggregationOptions, 
  handleAggregationChange 
} = useNodeConfig(props);

// 知识库相关状态
const knowledgeBaseShow = ref(false);
const knowledgeBasechecklist = ref<string[]>([]);
const searchknowledgeBase = ref("");

// 知识库列表数据
const knowledgeBaseList = ref<KnowledgeBase[]>([
  { name: "知识库名称1", size: "34", value: "001" },
  { name: "知识库名称2", size: "23", value: "002" },
]);

// 过滤后的知识库列表
const knowledgeBaseshow = computed(() => {
  if (searchknowledgeBase.value) {
    return knowledgeBaseList.value.filter((item) => {
      return item.name.includes(searchknowledgeBase.value);
    });
  } else {
    return knowledgeBaseList.value;
  }
});

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'knowledge') {
      const config = newNode.data.config;
      
      // 初始化默认配置
      if (!config.jiansuoType) {
        config.jiansuoType = "0";
      }
      if (!config.jiansuotypevalue) {
        config.jiansuotypevalue = "";
      }
      if (!config.databases) {
        config.databases = [];
      }
      if (!config.wenbenshuchu) {
        config.wenbenshuchu = "检索结果";
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听显示状态，更新聚合选项
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.node) {
      updateAggregationOptions();
    }
  },
  { immediate: true }
);

// 显示知识库选择对话框
const showKnowledgeBaseDialog = () => {
  knowledgeBaseShow.value = true;
};

// 删除知识库
const deleteKnowledge = (index: number) => {
  if (props.formData.config.databases) {
    props.formData.config.databases.splice(index, 1);
  }
};

// 保存知识库选择
const saveKnowledgeBase = () => {
  props.formData.config.databases = [];
  knowledgeBasechecklist.value.forEach((item: string) => {
    const data = knowledgeBaseList.value.find((items) => {
      return items.value === item;
    });
    if (data) {
      props.formData.config.databases.push(data);
    }
  });
  knowledgeBaseShow.value = false;
};
</script>

<style scoped lang="less">
.knowledge-node-config {
  .rowstit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
    width: 100%;
    
    .rowicon {
      width: 5px;
      height: 16px;
      background: #abc6ff;
      background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
      border-radius: 3px;
      margin-right: 9px;
    }
  }

  .setHeight {
    :deep(.n-form-item-label) {
      height: 22px !important;
      min-height: 22px;
    }
    
    :deep(.n-form-item-blank) {
      height: 22px;
      min-height: 22px;
    }
  }

  .knowledgelist {
    align-items: center;
    min-height: 38px;
    justify-content: space-between;
    padding-top: 10px;
    padding-right: 16px;
    padding-bottom: 10px;
    padding-left: 16px;
    border-radius: 8px;
    background-color: #f5f5f5;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 13px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #3b3b3b;
    margin-bottom: 8px;
  }

  .modelParametercard {
    :deep(.n-card__content) {
      padding: 0px;
    }
    
    :deep(.n-card__footer) {
      padding: 0px;
    }
  }

  .btnparent {
    :deep(.n-button) {
      width: 100%;
    }
  }
}
</style>
