# 视频知识点导航播放器组件

这是一个基于Vue 3、TypeScript和TailwindCSS开发的视频播放器组件，支持时间轴上的知识点标记和导航功能。适用于在线教育、培训视频等需要标记重点内容的场景。

## 特性

- 精致的视频播放控制（半透明黑色控制栏）
- 顶部进度条设计，支持知识点标记和快速跳转
- 知识点在视频时间轴上可视化标记
- **垂直时间轴样式**的知识点内容展示
- 进度条与时间轴联动高亮
- 自动滚动到当前播放知识点
- 可选在知识点处自动暂停
- 响应式设计，适配移动端和桌面端
- 完全使用TypeScript开发，提供类型定义
- 使用TailwindCSS进行样式设计

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|-------|------|
| videoSrc | string | - | 视频源URL，必填 |
| knowledgePoints | KnowledgePoint[] | [] | 知识点数据数组，必填 |
| autoHighlightPoints | boolean | true | 是否自动高亮当前播放位置的知识点 |
| autoPauseAtPoints | boolean | false | 是否在到达知识点时自动暂停 |

## 知识点数据结构

```typescript
interface KnowledgePoint {
  time: number;       // 知识点在视频中的时间点（秒）
  title: string;      // 知识点标题
  description: string; // 知识点描述
}
```

## 使用示例

```vue
<template>
  <VideoPlayer
    :videoSrc="videoSrc"
    :knowledgePoints="knowledgePoints"
    :autoHighlightPoints="true"
    :autoPauseAtPoints="false"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import * as VideoPlayerComponent from '@/components/VideoPlayer/index.vue';

// 使用具名导入方式解决默认导出问题
const VideoPlayer = VideoPlayerComponent;

// 视频源
const videoSrc = ref('https://example.com/video.mp4');

// 知识点数据
const knowledgePoints = ref([
  {
    time: 10,
    title: '知识点1',
    description: '这是第一个知识点的详细描述'
  },
  {
    time: 30,
    title: '知识点2',
    description: '这是第二个知识点的详细描述'
  },
  // 更多知识点...
]);
</script>
```

## 功能解析

### 1. 精致的视频控件

- 半透明黑色背景控制栏，提供现代感的UI设计
- 进度条置于控制按钮上方，符合主流视频播放器布局
- 悬停时显示进度条拖动把手，增强交互体验
- 优化的音量滑块样式，精致美观

### 2. 视频时间轴和知识点标记

组件在视频进度条上使用不同颜色的标记来表示知识点位置。当视频播放到对应位置时，知识点会自动高亮。点击进度条上的知识点标记时，不仅会跳转到视频对应位置，还会自动滚动到下方时间轴中的对应知识点。

### 3. 垂直时间轴展示

知识点内容以垂直时间轴的形式展示，直观显示知识点的时间顺序和内容：
- 时间轴左侧显示时间点
- 时间轴上的圆点标记知识点位置
- 当前播放的知识点会高亮显示
- 点击任意知识点可以跳转到对应的视频位置

### 4. 进度条与时间轴联动

最大的特点是实现了进度条与时间轴的双向联动：
- 点击进度条上的知识点标记会自动滚动到下方时间轴的对应条目
- 点击时间轴上的知识点会跳转视频到对应时间点
- 视频播放到知识点时，相应的时间轴条目会高亮显示

### 5. 自动暂停功能

如果启用了`autoPauseAtPoints`选项，视频将在到达每个知识点时自动暂停，让用户有时间阅读相关信息。

## 注意事项

- 确保提供的视频URL可访问且格式兼容浏览器
- 知识点时间应按升序排列，且不应超过视频总时长
- 在移动设备上，建议使用响应式容器来确保良好的用户体验 