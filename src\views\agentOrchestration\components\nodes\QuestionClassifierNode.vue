<template>
  <div class="question-classifier-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />
    
    <!-- 节点主体 -->
    <div class="node-body" :style="{ minHeight: nodeHeight + 'px' }">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass">
        <div class="status-dot"></div>
      </div>
      
      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/textGenerationIcon.png" alt="问题分类器">
        <div class="node-title">{{ data.label || '问题分类器' }}</div>
      </div>

      <!-- 分类列表 -->
      <div class="categories-list" v-if="categories.length > 0">
        <div
          v-for="category in displayCategories"
          :key="category.id"
          class="category-item"
        >
          <span class="category-label">{{ category.name }}</span>
          <!-- 每个分类的连接点 -->
          <Handle
            type="source"
            :position="Position.Right"
            :id="`${id}-${category.id}`"
            class="output-handle category-handle"
          />
        </div>
        <div v-if="categories.length > maxDisplayCategories" class="more-indicator">
          其他 {{ categories.length - maxDisplayCategories }} 项
          <!-- 隐藏分类的连接点 -->
          <Handle
            v-for="category in hiddenCategories"
            :key="`hidden-${category.id}`"
            type="source"
            :position="Position.Right"
            :id="`${id}-${category.id}`"
            class="output-handle category-handle hidden-handle"
          />
        </div>
      </div>

      <div class="node-description" v-else-if="data.description">
        {{ data.description }}
      </div>

      <div class="default-hint" v-else>
        点击配置分类规则
      </div>
    </div>

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus, type CategoryConfig } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'

interface QuestionClassifierNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      categories?: CategoryConfig[]
      defaultCategory?: string
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<QuestionClassifierNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)
const isSuccess = computed(() => props.data.status === NodeStatus.SUCCESS)
const isError = computed(() => props.data.status === NodeStatus.ERROR)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})

const categories = computed(() => {
  return props.data.config?.categories || []
})

const maxDisplayCategories = 3

const displayCategories = computed(() => {
  return categories.value.slice(0, maxDisplayCategories)
})

const hiddenCategories = computed(() => {
  return categories.value.slice(maxDisplayCategories)
})

// 根据分类数量动态计算节点高度
const nodeHeight = computed(() => {
  const baseHeight = 120
  const categoryHeight = Math.max(categories.value.length * 8, 0)
  return baseHeight + categoryHeight
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.question-classifier-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .categories-list {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .category-item {
        .node-list-item-style();
        position: relative;

        .category-label {
          font-size: 11px;
          color: #6b7280;
        }

        // 分类连接点样式 - 显示在节点卡片上方，位置与分类对齐
        :deep(.category-handle) {
          position: absolute;
          right: -16px; // 放在卡片外侧，视觉上更明显
          top: 50%;
          transform: translateY(-50%);
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border: 2px solid #ffffff;
          border-radius: 50%;
          // 分离transform和其他属性的transition，避免位移
          transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
          z-index: 100; // 高层级，显示在卡片上方
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            // 使用原地缩放而不是改变尺寸，避免位移
            transform: translateY(-50%) scale(1.2);
            transform-origin: center;
            background: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            // 为transform单独设置快速transition
            transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease, transform 0.1s ease;
          }

          &.vue-flow__handle-connecting {
            background: #125EFF;
            box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.3);
          }
        }
      }

      .more-indicator {
        .node-label-style();
        font-style: italic;
        text-align: center;
        padding: 2px 4px;
        color: #9ca3af;
        position: relative;

        // 隐藏分类的连接点样式
        :deep(.hidden-handle) {
          position: absolute;
          right: -12px; // 放在卡片外侧，视觉上更明显
          top: 50%;
          transform: translateY(-50%);
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border: 2px solid #ffffff;
          border-radius: 50%;
          // 分离transform和其他属性的transition，避免位移
          transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
          z-index: 100; // 高层级，显示在卡片上方
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            // 使用原地缩放而不是改变尺寸，避免位移
            transform: translateY(-50%) scale(1.2);
            transform-origin: center;
            background: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            // 为transform单独设置快速transition
            transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease, transform 0.1s ease;
          }

          &.vue-flow__handle-connecting {
            background: #125EFF;
            box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.3);
          }
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }

}
</style>
