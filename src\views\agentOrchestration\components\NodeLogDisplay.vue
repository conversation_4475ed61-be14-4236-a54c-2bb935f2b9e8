<template>
  <div
    class="node-log-display"
    v-if="latestLog"
    @click.stop
    @mousedown.stop
    @mouseup.stop
  >
    <div class="log-container">
      <!-- 日志头部 -->
      <div class="log-header" @click.stop="toggleExpanded">
        <div class="log-title">
          <span class="log-icon">●</span>
          <span class="status-indicator" :class="latestLog.status">
            {{ getStatusText(latestLog.status) }}
          </span>
          <span class="duration">{{ formatDuration(latestLog.duration) }}</span>
        </div>
        <div class="log-actions" @click.stop>
          <button
            class="toggle-btn"
            @click.stop="toggleExpanded"
            :title="isExpanded ? '收起' : '展开'"
          >
            <svg
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              :class="{ 'rotate-180': isExpanded }"
            >
              <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
          </button>
          <button
            class="clear-btn"
            @click.stop="clearLogs"
            title="清除日志"
          >
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m3 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 日志内容 -->
      <div class="log-content" v-show="isExpanded">
        <div class="log-terminal">
          <!-- 时间戳 -->
          <div class="log-timestamp">
            <span class="timestamp-label">执行时间:</span>
            <span class="timestamp-value">{{ formatTimestamp(latestLog.timestamp) }}</span>
          </div>

          <!-- 输入内容 -->
          <div v-if="latestLog.input" class="log-section">
            <div class="section-header" @click.stop="toggleInputExpanded">
              <span class="section-prompt">→</span>
              <span class="section-title">输入参数</span>
              <span class="expand-indicator" :class="{ 'expanded': isInputExpanded }">
                {{ isInputExpanded ? '▼' : '▶' }}
              </span>
            </div>
            <div class="section-content" v-show="isInputExpanded">
              <pre class="terminal-text">{{ formatContent(latestLog.input) }}</pre>
            </div>
          </div>

          <!-- 输出内容 -->
          <div v-if="latestLog.output" class="log-section">
            <div class="section-header" @click.stop="toggleOutputExpanded">
              <span class="section-prompt">←</span>
              <span class="section-title">执行结果</span>
              <span class="expand-indicator" :class="{ 'expanded': isOutputExpanded }">
                {{ isOutputExpanded ? '▼' : '▶' }}
              </span>
            </div>
            <div class="section-content" v-show="isOutputExpanded">
              <pre class="terminal-text output-text">{{ formatContent(latestLog.output) }}</pre>
            </div>
          </div>

          <!-- 错误信息 -->
          <div v-if="latestLog.error" class="log-section error-section">
            <div class="section-header">
              <span class="section-prompt">✕</span>
              <span class="section-title">错误信息</span>
            </div>
            <div class="section-content">
              <pre class="terminal-text error-text">{{ latestLog.error }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { NodeExecutionLog } from '@/store/modules/orchestration'
import { useOrchestrationStore } from '@/store'
import { useMessage } from 'naive-ui'

interface Props {
  nodeId: string
}

const props = defineProps<Props>()

// 定义组件名称
defineOptions({
  name: 'NodeLogDisplay'
})

const message = useMessage()
const orchestrationStore = useOrchestrationStore()

// 响应式数据
const isExpanded = ref(false)
const isInputExpanded = ref(false)
const isOutputExpanded = ref(false)

// 计算属性
const executionLogs = computed(() => orchestrationStore.getNodeExecutionLogs(props.nodeId))

// 获取节点当前状态
const currentNode = computed(() => {
  return orchestrationStore.currentNodes.find(n => n.id === props.nodeId)
})

// 只显示最新的一条日志，且节点状态不是IDLE时才显示
const latestLog = computed(() => {
  const hasLogs = executionLogs.value.length > 0
  const nodeStatus = currentNode.value?.data.status
  const shouldShow = hasLogs && nodeStatus !== 'idle'

  return shouldShow ? executionLogs.value[0] : null
})

// 方法
const getStatusText = (status: string): string => {
  const statusMap = {
    idle: '空闲',
    running: '运行中',
    success: '成功',
    error: '失败'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

const formatDuration = (duration: number): string => {
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatContent = (content: any): string => {
  if (typeof content === 'string') return content
  return JSON.stringify(content, null, 2)
}

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const toggleInputExpanded = () => {
  isInputExpanded.value = !isInputExpanded.value
}

const toggleOutputExpanded = () => {
  isOutputExpanded.value = !isOutputExpanded.value
}

const clearLogs = () => {
  orchestrationStore.clearNodeExecutionLogs(props.nodeId)
  message.success('日志已清除')
}
</script>

<style scoped>
.node-log-display {
  position: absolute;
  top: 100%;
  left: -30px; /* 向左扩展 */
  right: -30px; /* 向右扩展 */
  margin-top: 8px;
  z-index: 5;
  min-width: 200px; /* 确保最小宽度 */
}

.log-container {
  background: #f1f5f9; /* 浅蓝灰色背景，与白色节点区分 */
  border: 1px solid #cbd5e1;
  border-radius: 6px; /* 更小的圆角，更扁平 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 更轻的阴影 */
  overflow: hidden;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px; /* 进一步减少内边距，更紧凑 */
  background: #e2e8f0; /* 更深的背景色，与容器区分 */
  border-bottom: 1px solid #cbd5e1;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px; /* 设置最小高度，控制整体高度 */
}

.log-header:hover {
  background: #d1d5db;
}

.log-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.log-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4ade80;
  box-shadow: 0 0 8px rgba(74, 222, 128, 0.5);
  animation: pulse-dot 2s infinite;
}

.status-indicator {
  padding: 2px 8px; /* 减少内边距 */
  border-radius: 12px; /* 减少圆角 */
  font-size: 10px; /* 减少字体大小 */
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.2px;
  border: none;
}

.status-indicator.success {
  background: #f0f9ff;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.status-indicator.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-indicator.running {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}

.duration {
  color: #6b7280;
  font-size: 10px; /* 减少字体大小 */
  font-weight: 400;
  background: #f3f4f6;
  padding: 2px 6px; /* 减少内边距 */
  border-radius: 10px; /* 减少圆角 */
}

.log-actions {
  display: flex;
  gap: 4px;
}

.toggle-btn,
.clear-btn {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  padding: 4px; /* 减少内边距 */
  cursor: pointer;
  color: #6b7280;
  border-radius: 4px; /* 减少圆角 */
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px; /* 固定宽度 */
  height: 24px; /* 固定高度 */
}

.toggle-btn:hover {
  background: #f9fafb;
  color: #374151;
  border-color: #d1d5db;
}

.clear-btn:hover {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.rotate-180 {
  transform: rotate(180deg);
}

.log-content {
  max-height: 320px;
  overflow-y: auto;
}

.log-terminal {
  padding: 14px; /* 减少内边距 */
  background: #ffffff; /* 白色背景，与容器区分 */
  color: #374151;
}

.log-timestamp {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 6px;
  border-left: 3px solid #10b981;
  border: 1px solid #e5e7eb;
  font-family: inherit;
}

.timestamp-label {
  color: #6b7280;
  font-size: 11px;
  font-weight: 500;
}

.timestamp-value {
  color: #374151;
  font-size: 11px;
  font-weight: 600;
}

.log-section {
  margin-bottom: 16px;
}

.log-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.section-header:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.section-prompt {
  color: #4ade80;
  font-weight: 600;
  font-size: 14px;
  min-width: 16px;
  text-align: center;
}

.section-title {
  color: #60a5fa;
  font-size: 12px;
  font-weight: 600;
  flex: 1;
  letter-spacing: 0.3px;
}

.expand-indicator {
  color: #9ca3af;
  font-size: 11px;
  font-weight: 600;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

.expand-indicator.expanded {
  color: #4ade80;
  background: rgba(74, 222, 128, 0.1);
}

.section-content {
  margin-left: 0;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.terminal-text {
  font-size: 12px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  color: #374151;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  max-height: 180px;
  overflow-y: auto;
  line-height: 1.5;
  padding: 12px;
  background: #f8f9fa;
}

.output-text {
  color: #059669;
  background: #f0fdf4;
  border-left: 3px solid #10b981;
}

.error-text {
  color: #dc2626;
  background: #fef2f2;
  border-left: 3px solid #ef4444;
}

.error-section .section-prompt {
  color: #dc2626;
}

.error-section .section-title {
  color: #dc2626;
}

.error-section .section-header {
  border-color: #fecaca;
  background: #fef2f2;
}

/* 动画效果 */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes pulse-status {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 12px rgba(251, 191, 36, 0.2);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.4);
  }
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar,
.terminal-text::-webkit-scrollbar {
  width: 8px;
}

.log-content::-webkit-scrollbar-track,
.terminal-text::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb,
.terminal-text::-webkit-scrollbar-thumb {
  background: linear-gradient(145deg, #4a5568 0%, #2d3748 100%);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.log-content::-webkit-scrollbar-thumb:hover,
.terminal-text::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(145deg, #5a6578 0%, #3d4758 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-container {
    border-radius: 8px;
  }

  .log-header {
    padding: 8px 12px;
  }

  .log-terminal {
    padding: 12px;
  }

  .terminal-text {
    font-size: 10px;
    padding: 10px;
  }

  .section-header {
    gap: 8px;
    padding: 6px 10px;
  }

  .log-title {
    gap: 8px;
    font-size: 11px;
  }

  .status-indicator {
    padding: 2px 6px;
    font-size: 9px;
  }
}
</style>
