import { get, post } from '@/utils/request'

/**
 * 课程下拉数据接口
 * @param params
 */
export function courseAllApi<T = any>(params?: any) {
  return get<T>({
    url: '/eaide/course/all',
    data: params,
  })
}

/**
 * 列表接口
 * @param params
 */
export function list<T = any>(params?: any) {
  return get<T>({
    url: '/eaide/knowledge/getFlow',
    data: params,
  })
}

/**
 * 学习资源下拉数据接口
 * @param params
 */
export function naturalResourcesApi<T = any>(params?: any) {
  return get<T>({
    url: '/eaide/resource/all',
    data: params,
  })
}

/**
 * 课程节次下拉数据接口
 * @param params
 */
export function planallApi<T = any>(params?: any) {
  return get<T>({
    url: '/eaide/plan/all',
    data: params,
  })
}

/**
 * 前置知识下拉数据接口
 * @param params
 */
export function preKnowledgeApi<T = any>(params?: any) {
  return get<T>({
    url: '/eaide/knowledge/all',
    data: params,
  })
}

/**
 * 知识详情数据接口
 * @param params
 */
export function knowledgeDetailApi<T = any>(params?: any) {
  return get<T>({
    url: `/eaide/knowledgeDetails/${params.id}`,
    data: params,
  })
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export function saveOrUpdate<T = any>(params?: any, isUpdate: boolean = false) {
  const url = isUpdate ? '/eaide/knowledgeDetails/{id}/update' : '/eaide/knowledgeDetails'
  return post<T>({
    url: url,
    data: params,
  })
}

/**
 * 保存或者更新
 * @param params
 */
export function addNodeApi<T = any>(params?: any) {
  return post<T>({
    url: '/eaide/knowledge',
    data: params,
  })
}

/**
 * 删除单个
 * 注意：原文件使用 delete 方法，当前 index.ts 未导入，若需要可添加导入并修改
 */
// 这里假设 utils/request 有 delete 方法，若没有需要补充
// import { get, post, delete as del } from '@/utils/request' 
export function deleteOne<T = any>(params?: any) {
  // 若有 del 方法，可替换为 del<T>
  return get<T>({
    url: '/eaide/knowledgeDetails/{id}/delete',
    data: params,
  })
}