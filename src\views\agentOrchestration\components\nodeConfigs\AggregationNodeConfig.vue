<template>
  <div class="aggregation-node-config">
    <!-- 变量聚合配置 -->
    <n-form-item label-placement="left">
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span>
          变量聚合
        </div>
      </template>
      <div class="flex justify-end w-full">
        <span class="text-[#565756]">文本</span>
      </div>
    </n-form-item>

    <!-- 聚合变量选择下拉框 -->
    <n-form-item
      path="config.aggregationVariable"
      label-placement="left"
      class="mt-[12px]"
    >
      <AggregationSelector
        v-model="formData.config.aggregationVariable"
        :options="aggregationOptions"
        placeholder="当前对话信息"
        @change="handleAggregationChange"
      />
    </n-form-item>
  </div>
</template>

<script setup lang="ts">
import { watch, onMounted } from 'vue';
import { NFormItem } from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import { useNodeConfig } from './composables/useNodeConfig';
import type { NodeConfigProps, NodeConfigEvents } from './types';

// Props 和 Events
const props = defineProps<NodeConfigProps>();
const emit = defineEmits<NodeConfigEvents>();

// 使用共享逻辑
const {
  aggregationOptions,
  updateAggregationOptions,
  handleAggregationChange,
} = useNodeConfig(props);

// 监听节点变化，初始化配置
watch(
  () => props.node,
  (newNode) => {
    if (newNode && newNode.type === 'aggregation') {
      // 为聚合节点初始化默认配置
      if (!newNode.data.config.aggregationVariable) {
        newNode.data.config.aggregationVariable = "";
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听显示状态，更新聚合选项
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.node) {
      updateAggregationOptions();
    }
  },
  { immediate: true }
);

// 组件挂载时初始化
onMounted(() => {
  if (props.visible && props.node) {
    updateAggregationOptions();
  }
});
</script>

<style scoped lang="less">
.aggregation-node-config {
  .rowstit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
    width: 100%;
    
    .rowicon {
      width: 5px;
      height: 16px;
      background: #abc6ff;
      background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
      border-radius: 3px;
      margin-right: 9px;
    }
  }
}
</style>
