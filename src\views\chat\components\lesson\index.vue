<script setup>
import { NDivider, NTooltip, useMessage } from 'naive-ui'
import { computed, onUnmounted, ref } from 'vue'
import { QuillEditor } from '@vueup/vue-quill'
import axios from 'axios'
import html2pdf from 'html2pdf.js'
import { usestretchoutStore } from '@/store'
import { useCrypto } from '@/hooks/useSystem'
import signMd5Utils from '@/utils/encryption/signMd5Utils'
import { auth } from '@/utils/auth'
import { ConfigEnum } from '@/enums/httpEnum'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

const emit = defineEmits(['loadTap'])

const useTretchOut = usestretchoutStore()
const message = useMessage()

const richTextContent = computed(() => {
  return useTretchOut.stretchoutStorageInfo.lessonText
})
const toolbar = ref([
  { font: [] }, 'bold', 'italic', 'underline', { header: 1 },
  { align: [] }, { size: [] },
  { list: 'ordered' }, { list: 'bullet' }, { list: 'check' },
  { indent: '-1' }, { indent: '+1' }, 'image', 'link', 'clean',
])

function closefun() {
  useTretchOut.setContentShow(false)
}

// 获取设备ID和加解密工具
const device = window.$Eucp?.auth?.getBrowserFingerprint()
const { sm4Decrypt } = useCrypto()

// 获取上传请求头
const getUploadHeaders = () => {
  const headers = {
    Authorization: `Bearer ${auth.getToken()}`,
  }
  const timestamp = signMd5Utils.getTimestamp()
  headers[ConfigEnum.TIMESTAMP] = timestamp
  headers[ConfigEnum.Sign] = signMd5Utils.getSign('/resource/files/upload', {
    [ConfigEnum.TIMESTAMP]: timestamp,
  })
  headers[ConfigEnum.DEVICEID] = device
  headers[ConfigEnum.VERSION] = 'v3'
  return headers
}

// 自定义图片上传处理器
const handleImageUpload = async (file) => {
  try {
    if (!file) {
      message.error('请选择要上传的图片')
      return
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      message.error('只能上传jpg、jpeg、png图片')
      return
    }

    // 验证文件大小 (30MB)
    const maxSize = 30 * 1024 * 1024
    if (file.size > maxSize) {
      message.error('单个文件大小不能超过30M')
      return
    }

    message.info('图片上传中...')

    // 构建FormData
    const formData = new FormData()
    formData.append('file', file)
    formData.append('name', file.name)
    formData.append('guid', new Date().getTime().toString())
    formData.append('chunk', '0')
    formData.append('chunks', '1')
    formData.append('storeId', '')

    // 上传文件
    const uploadUrl = `${import.meta.env.VITE_GLOB_API_URL}/resource/files/upload`
    const response = await axios.post(uploadUrl, formData, {
      headers: {
        ...getUploadHeaders(),
        'Content-Type': 'multipart/form-data',
      },
    })

    if (response.data.code === '0') {
      // 解密响应数据
      const decryptedData = JSON.parse(sm4Decrypt(response.data.data))
      console.log('上传成功:', decryptedData)

      message.success('图片上传成功')
      // 返回图片预览URL
      return decryptedData.fileUrl
    }
    else {
      throw new Error(response.data.message || '上传失败')
    }
  }
  catch (error) {
    console.error('图片上传失败:', error)
    message.error(`图片上传失败: ${error.message || '未知错误'}`)
    return null
  }
}

// 编辑器准备完成回调
const handleEditorReady = (quill) => {
  // 自定义图片上传处理
  const toolbar = quill.getModule('toolbar')
  if (toolbar) {
    toolbar.addHandler('image', () => {
      const input = document.createElement('input')
      input.setAttribute('type', 'file')
      input.setAttribute('accept', 'image/*')
      input.click()

      input.onchange = async () => {
        const file = input.files[0]
        if (file) {
          const imageUrl = await handleImageUpload(file)
          if (imageUrl) {
            // 获取当前光标位置
            const range = quill.getSelection()
            if (range) {
              // 插入图片
              quill.insertEmbed(range.index, 'image', imageUrl)
              // 移动光标到图片后面
              quill.setSelection(range.index + 1)
            }
          }
        }
      }
    })
  }
}

// 本地导出
const uploadPdf = async () => {
  emit('loadTap', true)
  try {
    const contentMain = document.querySelector('.ql-editor')
    if (!contentMain) {
      message.error('未找到要导出的内容')
      return
    }

    // 配置PDF选项
    const options = {
      margin: [10, 10, 10, 10], // 上右下左边距(毫米)
      filename: `教学设计方案_${new Date().toLocaleDateString('zh-CN')}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2, // 提高清晰度
        useCORS: true,
        allowTaint: true,
      },
      jsPDF: {
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait',
      },
    }

    // message.info('正在生成PDF，请稍候...')

    // 生成并下载PDF
    await html2pdf().set(options).from(contentMain).save()

    message.success('PDF下载成功')
    emit('loadTap', false)
  }
  catch (error) {
    // console.error('PDF生成失败:', error)
    message.error('PDF生成失败，请重试')
    emit('loadTap', false)
  }
}

onUnmounted(() => {
  closefun()
})
</script>

<template>
  <div class="examinationbox">
    <div class="title">
      <span>教案内容生成助手</span>
      <div class="flex">
        <NTooltip trigger="hover">
          <template #trigger>
            <img class="cursor-pointer w-[28px] h-[28px] mr-[14px]" src="@/assets/uoload.png" alt="" @click="uploadPdf">
          </template>
          本地导出
        </NTooltip>
        <img alt="关闭" class="cursor-pointer" src="@/assets/close.png" @click="closefun">
      </div>
    </div>
    <NDivider />
    <QuillEditor
      :content="richTextContent.trimStart()" :toolbar="toolbar"
      content-type="html" placeholder="请输入内容..." style="height: 64vh"
      @ready="handleEditorReady"
    />
  </div>
</template>

<style lang="less" scoped>
.examinationbox {
	height: 100%;
	padding-left: 35.5px;
	padding-right: 35.5px;
}

.title {
	height: 80px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	font-size: 20px;
	color: #323233;
	letter-spacing: 0;

	img {
		width: 28px;
		height: 28px;
	}
}

.n-divider:not(.n-divider--vertical) {
	margin: 0;
}

.btnrow {
	margin-top: 14px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 20px;

	button {
		width: 152px;
		height: 36px;
		background: #1263ff;
		box-shadow: 0 2px 5px 0 #615ced4d;
		border-radius: 6px;
	}
}
</style>
