<script setup lang='ts'>
// import { computed } from 'vue'
// import { useUserStore } from '@/store'
import { infoStore } from '@/store/modules/info'
const info = infoStore()
// const userStore = useUserStore()
// const userInfo = computed(() => userStore.userInfo)
</script>

<template>
  <div class="" />
<!--  <div class="flex items-center overflow-hidden"> -->
<!--    <div class="w-10 h-10 overflow-hidden rounded-full shrink-0"> -->
<!--      <template v-if="isString(info.userInfo?.nickName) && info.userInfo.nickName.length > 0"> -->
<!--        <NAvatar -->
<!--          size="large" -->
<!--          round -->
<!--          :src="info.userInfo.nickName" -->
<!--          :fallback-src="defaultAvatar" -->
<!--        /> -->
<!--      </template> -->
<!--      <template v-else> -->
<!--        <NAvatar size="large" round :src="defaultAvatar" /> -->
<!--      </template> -->
<!--    </div> -->
<!--    <div class="flex-1 min-w-0 ml-2"> -->
<!--      <h2 class="overflow-hidden font-bold text-md text-ellipsis whitespace-nowrap text-white"> -->
<!--        {{ info.userInfo.nickName ?? '匿名用户' }} -->
<!--      </h2> -->
<!--      &lt;!&ndash;      <p class="overflow-hidden text-xs text-gray-500 text-ellipsis whitespace-nowrap"> &ndash;&gt; -->
<!--      &lt;!&ndash;        <span &ndash;&gt; -->
<!--      &lt;!&ndash;          v-if="isString(userInfo.description) && userInfo.description !== ''" &ndash;&gt; -->
<!--      &lt;!&ndash;          v-html="userInfo.description" &ndash;&gt; -->
<!--      &lt;!&ndash;        /> &ndash;&gt; -->
<!--      &lt;!&ndash;      </p> &ndash;&gt; -->
<!--    </div> -->
<!--  </div> -->
</template>
