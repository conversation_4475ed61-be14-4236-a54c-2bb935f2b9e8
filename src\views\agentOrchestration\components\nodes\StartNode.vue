<template>
  <div class="start-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 节点主体 - 横向布局 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 内容区域 - 横向排列 -->
      <div class="node-content">
        <!-- 播放图标 -->
        <img class="play-icon" src="@/assets/agentOrchestration/startIcon.png" alt="开始">

        <!-- 节点标题和描述 -->
        <div class="node-text-content">
          <div class="node-title">
            {{ data.label || '开始' }}
          </div>
          <!-- 节点描述信息 -->
          <div v-if="data.description" class="node-description">
            {{ data.description }}
          </div>
        </div>
      </div>
    </div>

    <!-- 输出连接点 -->
    <Handle
      type="source"
      :position="Position.Right"
      :id="`${id}-output`"
      class="output-handle"
    />

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'

interface StartNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<StartNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.start-node {
  .circular-node-style();
  .unified-handle-style();

  .node-body {
    // 横向布局容器
    // 当有描述信息时增加内边距和高度
    &:has(.node-description:not(:empty)) {
      padding: 10px 16px;
      min-height: 55px;
    }

    .node-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
    }

    .play-icon {
      width: 18px;
      height: 18px;
      color: #1890ff;
      flex-shrink: 0;
    }

    .node-text-content {
      display: flex;
      flex-direction: column;
      gap: 2px;
      flex: 1;
      min-width: 0; // 允许文本截断

      .node-title {
        .node-title-style();
        font-size: 12px;
        text-align: left;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .node-description {
        .node-description-style();
        font-size: 10px; // 更小的字体适应横向布局
        -webkit-line-clamp: 2; // 允许显示2行
        max-width: 90px; // 增加最大宽度
        text-align: left;
        line-height: 1.3;
        margin-top: 2px;
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }

}
</style>
