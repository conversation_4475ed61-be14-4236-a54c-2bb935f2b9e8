import { get, post } from '@/utils/request'
import request from '@/utils/request/axios'
import type { AxiosProgressEvent, GenericAbortSignal } from 'axios'

/**
 * 获取教案内容级联数据
 * @param params - 可选参数，包含父级ID
 * @returns 返回级联数据
 */
export function getTeachingPlanListApi<T = any>(params?: { pid?: string | number }) {
    return get<T>({
        url: '/emind/conversationContent/temp/teaching_plan/list',
        data: params,
        headers: {
            'Content-Type': 'application/json',
        },
    })
}

/**
 * 保存教案生成数据
 * @param data - 教案数据
 * @returns 返回保存结果
 */
export function saveTeachingPlanApi<T = any>(data: {
    coursesName: string
    themeName: string
    coreKnowledgeName: string
    cognitiveStrategyName: string
    teachingObjectives: string
    signal?: GenericAbortSignal
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
}) {
    return post<T>({
        url: '/emind/graph/teaching_plan',
        data,
        headers: {
            'Content-Type': 'application/json',
        },
        signal: data.signal,
        onDownloadProgress: data.onDownloadProgress,
    })
}

/**
 * 导出教案为Word文档
 * @returns 返回Word文档的blob数据
 */
export function exportTeachingPlanWordApi(downId: string) {
    return request({
        method: 'POST',
        url: `/emind/conversationContent/export_word`,
        data: {
            id: downId
        },
        responseType: 'blob',
        headers: {
            'Content-Type': 'application/json',
        },
    })
} 