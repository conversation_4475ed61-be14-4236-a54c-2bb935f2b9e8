/**
 * 数据格式转换工具函数
 * 
 * 提供前后端数据格式的双向转换功能，
 * 移除UI相关属性，保留业务逻辑核心数据
 */

import type { FlowData, FlowNode, FlowEdge } from '@/store/modules/orchestration'
import type {
  BackendFlowData,
  BackendNodeData,
  BackendEdgeData,
  NodeConfig,
  LLMNodeConfig,
  APINodeConfig,
  ConditionNodeConfig,
  FlowVariable
} from '@/types/backend'
import { NodeType, NodeStatus } from '@/store/modules/orchestration'

/**
 * 将前端流程数据转换为后端格式
 * @param frontendFlow 前端流程数据
 * @returns 后端流程数据
 */
export function transformToBackendFormat(frontendFlow: FlowData): BackendFlowData {
  if (!frontendFlow) {
    throw new Error('前端流程数据不能为空')
  }
console.log(frontendFlow);

  return {
    id: frontendFlow.id,
    name: frontendFlow.name,
    description: frontendFlow.description,
    nodes: frontendFlow.nodes.map(transformNodeToBackend),
    edges: frontendFlow.edges.map(transformEdgeToBackend),
    variables: extractFlowVariables(frontendFlow),
    metadata: {
      version: '1.0',
      createdAt: frontendFlow.createdAt,
      updatedAt: frontendFlow.updatedAt,
      ...frontendFlow.metadata
    }
  }
}

/**
 * 将后端流程数据转换为前端格式
 * @param backendFlow 后端流程数据
 * @returns 前端流程数据
 */
export function transformFromBackendFormat(backendFlow: BackendFlowData): FlowData {
  if (!backendFlow) {
    throw new Error('后端流程数据不能为空')
  }

  return {
    id: backendFlow.id,
    name: backendFlow.name,
    description: backendFlow.description,
    nodes: backendFlow.nodes.map(transformNodeFromBackend),
    edges: backendFlow.edges.map(transformEdgeFromBackend),
    createdAt: backendFlow.metadata.createdAt,
    updatedAt: backendFlow.metadata.updatedAt,
    metadata: {
      version: backendFlow.metadata.version,
      tags: backendFlow.metadata.tags,
      category: backendFlow.metadata.category,
      sceneCategory: backendFlow.metadata.sceneCategory
    }
  }
}

/**
 * 将前端节点转换为后端格式
 * @param node 前端节点数据
 * @returns 后端节点数据
 */
export function transformNodeToBackend(node: FlowNode): BackendNodeData {
  if (!node || !node.id || !node.type) {
    throw new Error('节点数据不完整')
  }
  console.log(node);
  return {
    id: node.id,
    type: node.type,
    label: node.data.label || '',
    description: node.data.description,
    position: {
      x: node.position?.x || 0,
      y: node.position?.y || 0
    },
    config: sanitizeNodeConfig(node.data.config || {}, node.type),
    handleBounds: node.handleBounds,
    inputParams: node.data.inputParams,
    outputParams: node.data.outputParams
  }
}

/**
 * 将后端节点转换为前端格式
 * @param backendNode 后端节点数据
 * @returns 前端节点数据
 */
export function transformNodeFromBackend(backendNode: BackendNodeData): FlowNode {
  if (!backendNode || !backendNode.id || !backendNode.type) {
    throw new Error('后端节点数据不完整')
  }

  return {
    id: backendNode.id,
    type: backendNode.type,
    position: {
      x: backendNode.position?.x || 0,
      y: backendNode.position?.y || 0
    },
    data: {
      label: backendNode.label,
      description: backendNode.description,
      config: backendNode.config,
      status: NodeStatus.IDLE,
      inputParams: backendNode.inputParams,
      outputParams: backendNode.outputParams,
      // 初始化运行日志相关字段
      executionLogs: [],
      lastExecution: undefined,
      totalExecutions: 0
    },
    draggable: true,
    selectable: true,
    deletable: backendNode.type !== NodeType.START && backendNode.type !== NodeType.END
  }
}

/**
 * 将前端边转换为后端格式
 * @param edge 前端边数据
 * @returns 后端边数据
 */
export function transformEdgeToBackend(edge: FlowEdge): BackendEdgeData {
  if (!edge || !edge.id || !edge.source || !edge.target) {
    throw new Error('边数据不完整')
  }

  return {
    id: edge.id,
    source: edge.source,
    target: edge.target,
    sourceHandle: edge.sourceHandle,
    targetHandle: edge.targetHandle,
    condition: edge.data?.condition
  }
}

/**
 * 将后端边转换为前端格式
 * @param backendEdge 后端边数据
 * @returns 前端边数据
 */
export function transformEdgeFromBackend(backendEdge: BackendEdgeData): FlowEdge {
  if (!backendEdge || !backendEdge.id || !backendEdge.source || !backendEdge.target) {
    throw new Error('后端边数据不完整')
  }

  return {
    id: backendEdge.id,
    source: backendEdge.source,
    target: backendEdge.target,
    sourceHandle: backendEdge.sourceHandle,
    targetHandle: backendEdge.targetHandle,
    animated: false, // 默认不动画
    data: backendEdge.condition ? { condition: backendEdge.condition } : undefined
  }
}

/**
 * 清理节点配置数据，移除UI相关和临时数据
 * @param config 原始配置数据
 * @param nodeType 节点类型
 * @returns 清理后的配置数据
 */
export function sanitizeNodeConfig(config: Record<string, any>, nodeType: NodeType): NodeConfig {
  if (!config) {
    return {}
  }

  // 创建配置副本，避免修改原始数据
  const sanitizedConfig = { ...config }

  // 移除通用的UI相关属性
  delete sanitizedConfig.position
  delete sanitizedConfig.style
  delete sanitizedConfig.className
  delete sanitizedConfig.selected
  delete sanitizedConfig.dragging
  delete sanitizedConfig.resizing

  // 移除临时状态和调试信息
  delete sanitizedConfig.tempData
  delete sanitizedConfig.debugInfo
  delete sanitizedConfig.uiState
  delete sanitizedConfig.isEditing

  // 根据节点类型进行特定清理
  switch (nodeType) {
    case NodeType.API:
      return sanitizeAPIConfig(sanitizedConfig)
    case NodeType.CONDITION:
      return sanitizeConditionConfig(sanitizedConfig)
    default:
      return sanitizedConfig
  }
}


/**
 * 清理API节点配置
 */
function sanitizeAPIConfig(config: Record<string, any>): APINodeConfig {
  return {
    method: config.method || 'GET',
    url: config.url || '',
    headers: config.headers,
    params: config.params,
    body: config.body,
    timeout: typeof config.timeout === 'number' ? config.timeout : 30,
    retryCount: typeof config.retryCount === 'number' ? config.retryCount : 0
  }
}

/**
 * 清理条件节点配置
 */
function sanitizeConditionConfig(config: Record<string, any>): ConditionNodeConfig {
  return {
    conditions: config.conditions || [],
    conditionLogic: config.conditionLogic || config.logicOperator || 'AND',
    elseIfBranches: config.elseIfBranches,
    elseIfLogic: config.elseIfLogic,
    elseBranch: config.elseBranch || config.defaultBranch
  }
}

/**
 * 从流程数据中提取变量信息
 * @param flowData 流程数据
 * @returns 流程变量列表
 */
function extractFlowVariables(flowData: FlowData): FlowVariable[] {
  const variables: FlowVariable[] = []

  // 从流程的variables字段提取（新的变量管理系统）
  if (flowData.variables && Array.isArray(flowData.variables)) {
    variables.push(...flowData.variables)
  }

  // 兼容旧版本：从开始节点提取变量定义
  const startNode = flowData.nodes.find(node => node.type === NodeType.START)
  if (startNode?.data.config?.variables && Array.isArray(startNode.data.config.variables)) {
    // 转换旧格式变量为新格式
    const legacyVariables = startNode.data.config.variables.map((variable: any, index: number) => ({
      id: variable.id || `legacy_var_${index}`,
      name: variable.name,
      type: 'user' as const, // 旧版本变量默认为用户变量
      dataType: variable.type || 'string',
      description: variable.description,
      defaultValue: variable.defaultValue,
      readonly: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }))

    // 避免重复添加
    legacyVariables.forEach(legacyVar => {
      if (!variables.find(v => v.name === legacyVar.name)) {
        variables.push(legacyVar)
      }
    })
  }

  return variables
}

/**
 * 计算数据大小减少比例
 * @param originalData 原始数据
 * @param transformedData 转换后数据
 * @returns 减少比例（百分比）
 */
export function calculateDataReduction(originalData: any, transformedData: any): number {
  const originalSize = JSON.stringify(originalData).length
  const transformedSize = JSON.stringify(transformedData).length
  return Math.round((1 - transformedSize / originalSize) * 100)
}

/**
 * 分析边数据优化效果
 * @param frontendEdges 前端边数据
 * @param backendEdges 后端边数据
 * @returns 优化分析结果
 */
export function analyzeEdgeOptimization(frontendEdges: FlowEdge[], backendEdges: BackendEdgeData[]) {
  const frontendSize = JSON.stringify(frontendEdges).length
  const backendSize = JSON.stringify(backendEdges).length
  const reduction = calculateDataReduction(frontendEdges, backendEdges)

  // 统计移除的UI属性和冗余数据
  const removedProperties = new Set<string>()
  frontendEdges.forEach(edge => {
    Object.keys(edge).forEach(key => {
      // 只保留核心连接信息，移除所有UI相关和冗余数据
      if (!['id', 'source', 'target', 'sourceHandle', 'targetHandle', 'data'].includes(key)) {
        removedProperties.add(key)
      }
    })
  })

  // 特别标记最大的冗余数据
  const majorRedundancies = ['sourceNode', 'targetNode', 'style', 'markerEnd', 'animated', 'selected', 'type', 'events', 'label']
  const foundRedundancies = majorRedundancies.filter(prop => removedProperties.has(prop))

  return {
    frontendSize,
    backendSize,
    reduction,
    removedProperties: Array.from(removedProperties),
    majorRedundancies: foundRedundancies,
    edgeCount: frontendEdges.length,
    avgSizePerEdge: {
      frontend: Math.round(frontendSize / frontendEdges.length),
      backend: Math.round(backendSize / backendEdges.length)
    }
  }
}
