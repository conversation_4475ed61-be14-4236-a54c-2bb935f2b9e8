
  // 获取当前 token
  export function   getToken(): string {
    return window.$EucpAuth?.getToken() || '';
  }
  // 获取用户信息
  export function   getUserInfo(): any {
    return window.$EucpAuth?.getUserInfo() || null;
  }

  // 检查角色权限
  export function  hasRole(roles: string | string[]): boolean {
    return window.$EucpAuth?.hasRole(roles) || false;
  }

    // 检查功能权限
  export function  hasPermission(permissions: string | string[]): boolean {
    return window.$EucpAuth?.hasPermission(permissions) || false;
  }

  // 退出登录
  export function logout(): void {
    window.$EucpAuth?.logout();
  }

   // 获取数据字典
   export function getDicts(): void {
      return window.$EucpAuth?.getDicts();
  }
     // 获取数据字典ByCode
     export function getDictByCode(code:any): void {
      return window.$EucpAuth?.getDictByCode(code);
  }
     // 获取数据字典ByCode，value
     export function getDictTextByCodeValue(code:any, value:any): void {
      return window.$EucpAuth?.getDictTextByCodeValue(code, value);
  }

   // 获取数据字典ByCode，value
   export function getpictureDictTextByCodeValue(code:any, text:any): void {
    return  getDictByCode(code).find((item: any) => item.text === text)?.value || null;
   
    // return window.$EucpAuth?.getDictTextByCodeValue(code, value);
}
  //  // 获取数据字典Text ByCode，value
  //  getDictTextByCodeValue: (code: string, value: string) => {
  //   // 判断code是否存在
  //   if (!authStore.dicts[code]) {
  //     return null;
  //   }
  //   // 返回 text
  //   return authStore.dicts[code].find((item: any) => item.value === value)?.text || null;
  // },