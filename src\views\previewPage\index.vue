<template>
  <NSpin :show="loading" >
    <div class="ovsize">
      <CustomViewer v-bind="preview" v-if="isView && pdfUrl" style="width: 100%;height: 100%" />
      <!-- <img v-if="isView && pdfUrl" :src="pdfUrl" alt="" style="width: 100%;height: 100%" /> -->
      <iframe v-else :src="pdfUrl" frameborder="0" style="width: 100%;height: 100%;min-height: 100vh;"></iframe>
    </div>
  </NSpin>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import CustomViewer from "@/views/previewPage/CustomViewer.vue";
import {NSpin} from 'naive-ui';
import {downloadfileApi} from "@/api/knowledgeFactory"

const route = useRoute();
const pdfUrl = ref('');
const fileTypeList = ['jpg', 'jpeg', 'png'];
const isView = ref(false);
const loading = ref(false);
const myRow = ref({})
const preview = ref({
  url: "",
  suffix: "",
});
      loading.value = true;
      myRow.value =  route.query
      if (fileTypeList.includes(myRow.value.fileType)) {
        isView.value = true;
        
        downloadfileApi({ filePath: myRow.value.filePath }).then(res => {
          // const contentDisposition = res.headers?.['content-disposition'] || ''
          // if (contentDisposition) {
          //   const filenameMatch = contentDisposition.match(/filename="(.+?)"/)
          //   if (filenameMatch && filenameMatch[1])
          //     filename = filenameMatch[1]
          // }

          // 创建Blob对象
          const blob = new Blob([res.data], { type: 'application/octet-stream' })
          pdfUrl.value = window.URL.createObjectURL(blob);
          loading.value = false;
          preview.value = {
            url: pdfUrl.value,
            suffix: myRow.value.fileType,
          }
        });
      } else if (myRow.value.fileType === 'txt') {
        isView.value = false;
        downloadfileApi({ filePath: myRow.value.filePath }).then(res => {
          const blob = new Blob([res.data], { type: 'text/plain;charset=utf-8' });
          pdfUrl.value = window.URL.createObjectURL(blob);
          loading.value = false;
        });
      } else if (myRow.value.fileType === 'pdf') {
        isView.value = true;
        downloadfileApi({ filePath: myRow.value.filePath }).then(res => {
          const url = window.URL.createObjectURL(new Blob([res.data], { type: 'application/pdf' }));
          pdfUrl.value = url;
          loading.value = false;
          preview.value = {
            url: pdfUrl.value,
            suffix: myRow.value.fileType,
          }
        });
      } else if (myRow.value.fileType === 'doc' || myRow.value.fileType === 'docx') {
        isView.value = true;
        downloadfileApi({ filePath: myRow.value.filePath }).then(res => {
          // 根据文件类型设置正确的 MIME 类型
          const mimeType = myRow.value.fileType === 'doc' ? 'application/msword' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          const url = window.URL.createObjectURL(new Blob([res.data], { type: mimeType }));
          pdfUrl.value = url;
          loading.value = false;
          preview.value = {
            url: pdfUrl.value,
            suffix: myRow.value.fileType,
          }
        });
      }
      else if (myRow.value.fileType === 'xls' || myRow.value.fileType === 'xlsx') {
        isView.value = true;
        downloadfileApi({ filePath: myRow.value.filePath }).then(res => {
          // 根据文件类型设置正确的 MIME 类型
          const mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          const url = window.URL.createObjectURL(new Blob([res.data], { type: mimeType }));
          pdfUrl.value = url;
          loading.value = false;
          preview.value = {
            url: pdfUrl.value,
            suffix: myRow.value.fileType,
          }
        });
      }


  

</script>

<style scoped lang="less">
.ovsize {
  background-color: #FFF;
  width: 80%;
  height: 100%;
  margin: 0 auto;
}
</style>
