import { get, post } from '@/utils/request'

export function getHistory<T = any>(data: any) {
  return get<T>({
    url: '/conversation/all',
    data,
  })
}

export function deleteHistory<T = any>(data: any) {
  return post<T>({
    url: `/conversation/delete`,
    data,
		headers: {
			'Content-Type': 'application/json',
		},
  })
}

export function getHistoryDft<T = any>(data: any) {
	return get<T>({
		url: '/msg_history/all',
		data,
	})
}

export function updateHistory<T = any>(id: any, data: any) {
  return post<T>({
    url: `/conversation/${id}/update`,
    data,
  })
}
import { useChatStore } from '@/store'

export function addHistory<T = any>(data: any) {
  return post<T>({
    url: `/conversation`,
    data,
		headers: {
			'Content-Type': 'application/json',
		},
  })
}
export function gethotTopic<T = any>() {
  return get<T>({
    url: '/hot_topic/get_cache_hot_topic_list',
  })
}
