# 智慧教育 GPT 协作学习平台 Brownfield 增强架构

## Introduction

This document outlines the architectural approach for enhancing 智慧教育 GPT with 协作学习平台 (Collaborative Learning Platform). Its primary goal is to serve as the guiding architectural blueprint for AI-driven development of new features while ensuring seamless integration with the existing system.

**Relationship to Existing Architecture:**
This document supplements existing project architecture by defining how new components will integrate with current systems. Where conflicts arise between new and existing patterns, this document provides guidance on maintaining consistency while implementing enhancements.

### Existing Project Analysis

#### Current Project State
- **Primary Purpose:** 智慧教育 GPT 是一个基于 Vue 3 + TypeScript 的现代化教育平台，集成多个教育相关功能模块，包括聊天、教学计划生成、课件处理、视频学习助手等
- **Current Tech Stack:** Vue 3.2.47 + TypeScript 4.9.5 + Vite 4.2.0 + Naive UI 2.34.3 + Pinia 2.0.33 + Vue Router 4.1.6
- **Architecture Style:** 单页应用 (SPA) 采用组件化架构，使用状态管理 (Pinia) 和模块化路由设计
- **Deployment Method:** Vite 构建输出到 agent 目录，支持 Docker 部署和代理配置

#### Available Documentation
- 项目配置文件 (package.json, vite.config.ts, tsconfig.json)
- 路由配置 (src/router/index.ts)
- 主应用结构 (src/App.vue, src/main.ts)
- API 接口定义 (src/api/index.ts)
- 状态管理结构 (src/store/)
- 组件目录结构
- PRD 文档 (docs/prd.md)

#### Identified Constraints
- 现有系统使用 Naive UI 作为主要 UI 组件库，新功能必须保持一致性
- 项目使用 Pinia 进行状态管理，新功能需要遵循现有的状态管理模式
- 系统使用 RESTful API 与后端通信，新功能需要遵循现有的 API 设计模式
- 项目使用 TypeScript，新功能需要保持类型安全
- 现有系统支持多语言，新功能需要集成到现有的国际化系统中

### Change Log

| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|--------|
| Initial Architecture Creation | 2025-08-26 | v1.0 | Created Brownfield Architecture for Collaborative Learning Platform | Winston (Architect) |

## Enhancement Scope and Integration Strategy

### Enhancement Overview
**Enhancement Type:** New Feature Addition
**Scope:** 添加全新的协作学习平台功能，支持多人协作学习和项目开发
**Integration Impact:** Moderate Impact (some existing code changes) - 需要添加新的路由、组件、状态管理和 API 集成

### Integration Approach
**Code Integration Strategy:**
- 在现有路由结构中添加新的协作学习平台路由
- 创建新的组件目录结构，遵循现有的组织模式
- 扩展现有的状态管理，添加协作相关的 store 模块
- 与现有的 API 层集成，添加协作相关的 API 调用

**Database Integration:**
- 与现有后端 API 集成，通过 RESTful 接口进行数据交互
- 不直接操作数据库，所有数据操作通过现有 API 层进行
- 遵循现有的数据模型和接口规范

**API Integration:**
- 扩展现有的 API 请求模块，添加协作相关的接口调用
- 遵循现有的 API 设计模式，包括错误处理、认证和响应格式
- 使用现有的请求拦截器和响应处理机制

**UI Integration:**
- 使用现有的 Naive UI 组件库，保持视觉一致性
- 遵循现有的样式规范和设计系统
- 集成到现有的布局和导航结构中

### Compatibility Requirements
- **Existing API Compatibility:** 所有新功能必须与现有 API 兼容，遵循相同的认证、错误处理和响应格式
- **Database Schema Compatibility:** 通过现有 API 层访问数据，不直接修改数据库结构
- **UI/UX Consistency:** 使用 Naive UI 组件库，遵循现有的设计规范和交互模式
- **Performance Impact:** 新功能不应显著影响现有系统的性能，需要优化数据加载和状态管理

## Tech Stack Alignment

### Existing Technology Stack

| Category | Current Technology | Version | Usage in Enhancement | Notes |
|----------|-------------------|---------|---------------------|-------|
| Frontend Framework | Vue | 3.2.47 | Core framework for new components | Maintain existing version |
| Language | TypeScript | 4.9.5 | Type safety for new features | Maintain existing version |
| Build Tool | Vite | 4.2.0 | Build and development | Maintain existing version |
| UI Library | Naive UI | 2.34.3 | UI components for new features | Maintain existing version |
| State Management | Pinia | 2.0.33 | State management for collaboration | Maintain existing version |
| Router | Vue Router | 4.1.6 | Navigation for new pages | Maintain existing version |
| HTTP Client | Axios | - | API calls for collaboration | Use existing implementation |
| Styling | Tailwind CSS + Less | 3.2.7 | Component styling | Maintain existing approach |
| Internationalization | Vue I18n | 9.2.2 | Multi-language support | Maintain existing version |
| Document Processing | @vue-office | Various | Document collaboration | Use existing components |
| Workflow | Vue Flow | Various | Process visualization | Use existing components |

### New Technology Additions

| Technology | Version | Purpose | Rationale | Integration Method |
|------------|---------|---------|-----------|------------------|
| WebSocket | - | Real-time collaboration | Enable real-time editing and messaging | Integrate with existing request utilities |
| Operational Transformation | - | Conflict resolution for collaborative editing | Handle concurrent edits to same document | Implement as custom utility service |
| WebRTC | - | Real-time audio/video communication | Enable real-time communication between users | Implement as custom component |

## Data Models and Schema Changes

### New Data Models

#### Project
**Purpose:** 协作项目的核心数据模型，存储项目基本信息、设置和状态
**Integration:** 与现有的用户系统集成，通过 userId 关联项目所有者和成员

**Key Attributes:**
- id: string - 项目唯一标识符
- name: string - 项目名称
- description: string - 项目描述
- ownerId: string - 项目所有者ID
- status: string - 项目状态（active, completed, archived）
- createdAt: Date - 创建时间
- updatedAt: Date - 更新时间
- settings: Object - 项目设置（权限、通知等）

**Relationships:**
- **With Existing:** 通过 ownerId 关联到现有用户系统
- **With New:** 一个项目包含多个成员、任务、文档和聊天消息

#### ProjectMember
**Purpose:** 项目成员数据模型，管理项目参与者的角色和权限
**Integration:** 与现有的用户认证系统集成，确保权限控制的一致性

**Key Attributes:**
- id: string - 成员关系唯一标识符
- projectId: string - 关联项目ID
- userId: string - 用户ID
- role: string - 成员角色（owner, admin, editor, viewer）
- joinedAt: Date - 加入时间
- permissions: Object - 特定权限设置

**Relationships:**
- **With Existing:** 通过 userId 关联到现有用户系统
- **With New:** 一个成员可以参与多个项目，一个项目有多个成员

#### Task
**Purpose:** 任务管理数据模型，支持项目任务创建、分配和跟踪
**Integration:** 与现有的通知系统集成，支持任务提醒和状态更新通知

**Key Attributes:**
- id: string - 任务唯一标识符
- projectId: string - 关联项目ID
- title: string - 任务标题
- description: string - 任务描述
- assigneeId: string - 负责人ID
- status: string - 任务状态（todo, in_progress, completed）
- priority: string - 优先级（low, medium, high）
- dueDate: Date - 截止日期
- createdAt: Date - 创建时间
- updatedAt: Date - 更新时间

**Relationships:**
- **With Existing:** 通过 assigneeId 关联到现有用户系统
- **With New:** 一个任务属于一个项目，一个项目有多个任务；一个任务可以有多个评论

#### Document
**Purpose:** 文档协作数据模型，支持实时协作编辑和版本控制
**Integration:** 与现有的文档处理系统集成，复用文档预览和编辑功能

**Key Attributes:**
- id: string - 文档唯一标识符
- projectId: string - 关联项目ID
- name: string - 文档名称
- type: string - 文档类型（text, markdown, code）
- content: string - 文档内容
- version: number - 文档版本
- lastEditedBy: string - 最后编辑者ID
- lastEditedAt: Date - 最后编辑时间
- createdAt: Date - 创建时间

**Relationships:**
- **With Existing:** 通过 lastEditedBy 关联到现有用户系统
- **With New:** 一个文档属于一个项目，一个项目有多个文档；一个文档有多个版本历史

### Schema Integration Strategy

**Database Changes Required:**
- **New Tables:** 通过 API 创建新的数据模型，不需要直接修改数据库
- **Modified Tables:** 不需要修改现有表结构
- **New Indexes:** 通过 API 优化查询性能
- **Migration Strategy:** 通过 API 进行数据迁移，确保向后兼容性

**Backward Compatibility:**
- 现有功能不受影响，新功能作为独立模块添加
- 通过 API 层进行数据访问，不直接修改现有数据结构
- 保持现有 API 接口不变，新功能通过新端点提供

## Component Architecture

### New Components

#### CollaborativeProjectList
**Responsibility:** 显示用户参与的所有协作项目列表，支持项目筛选、排序和创建
**Integration Points:** 与现有的用户认证系统集成，获取用户信息；与导航系统集成，提供项目访问入口

**Key Interfaces:**
- loadProjects(): Promise<Project[]> - 加载用户项目列表
- createProject(project: ProjectData): Promise<Project> - 创建新项目
- filterProjects(filter: ProjectFilter): Project[] - 筛选项目
- sortProjects(sortBy: string): Project[] - 排序项目

**Dependencies:**
- **Existing Components:** 使用现有的导航组件、用户组件和加载状态组件
- **New Components:** ProjectCard, CreateProjectModal
- **Technology Stack:** Vue 3, TypeScript, Naive UI, Pinia

#### CollaborativeProjectDetail
**Responsibility:** 显示项目详细信息、成员列表、任务和文档，支持项目管理和导航
**Integration Points:** 与现有的路由系统集成，提供项目详情页面；与状态管理系统集成，管理项目数据

**Key Interfaces:**
- loadProject(projectId: string): Promise<Project> - 加载项目详情
- updateProject(project: Partial<Project>): Promise<Project> - 更新项目信息
- deleteProject(projectId: string): Promise<void> - 删除项目
- navigateToTask(taskId: string): void - 导航到任务详情
- navigateToDocument(documentId: string): void - 导航到文档详情

**Dependencies:**
- **Existing Components:** 使用现有的布局组件、导航组件和用户组件
- **New Components:** ProjectMembers, ProjectTasks, ProjectDocuments, ProjectSettings
- **Technology Stack:** Vue 3, TypeScript, Naive UI, Vue Router, Pinia

#### CollaborativeEditor
**Responsibility:** 提供多人实时协作编辑功能，支持文档编辑、评论和版本控制
**Integration Points:** 与现有的文档处理系统集成，复用文档编辑功能；与实时通信系统集成，支持实时协作

**Key Interfaces:**
- loadDocument(documentId: string): Promise<Document> - 加载文档
- saveDocument(document: Document): Promise<Document> - 保存文档
- onDocumentChange(callback: Function): void - 监听文档变更
- addComment(comment: Comment): Promise<Comment> - 添加评论
- getVersionHistory(): Promise<DocumentVersion[]> - 获取版本历史

**Dependencies:**
- **Existing Components:** 使用现有的文档编辑器组件和工具栏组件
- **New Components:** RealtimeCursor, CommentPanel, VersionHistory
- **Technology Stack:** Vue 3, TypeScript, Naive UI, WebSocket, Operational Transformation

#### TaskBoard
**Responsibility:** 提供看板视图的任务管理界面，支持任务创建、分配、拖拽和状态更新
**Integration Points:** 与现有的状态管理系统集成，管理任务数据；与拖拽功能集成，支持任务状态更新

**Key Interfaces:**
- loadTasks(projectId: string): Promise<Task[]> - 加载项目任务
- createTask(task: TaskData): Promise<Task> - 创建新任务
- updateTask(taskId: string, updates: Partial<Task>): Promise<Task> - 更新任务
- deleteTask(taskId: string): Promise<void> - 删除任务
- moveTask(taskId: string, newStatus: string): Promise<void> - 移动任务到新状态

**Dependencies:**
- **Existing Components:** 使用现有的拖拽组件和状态组件
- **New Components:** TaskCard, TaskColumn, CreateTaskModal
- **Technology Stack:** Vue 3, TypeScript, Naive UI, Vue Draggable, Pinia

#### RealtimeChat
**Responsibility:** 提供项目内的实时聊天功能，支持文字消息、文件分享和@提及
**Integration Points:** 与现有的聊天系统集成，复用消息显示功能；与实时通信系统集成，支持实时消息传递

**Key Interfaces:**
- loadMessages(projectId: string): Promise<Message[]> - 加载聊天消息
- sendMessage(message: MessageData): Promise<Message> - 发送消息
- onNewMessage(callback: Function): void - 监听新消息
- markMessagesAsRead(): Promise<void> - 标记消息为已读
- uploadFile(file: File): Promise<string> - 上传文件

**Dependencies:**
- **Existing Components:** 使用现有的消息组件、文件上传组件和用户组件
- **New Components:** MessageInput, MessageList, FilePreview
- **Technology Stack:** Vue 3, TypeScript, Naive UI, WebSocket, Pinia

### Component Interaction Diagram

```mermaid
graph TD
    A[CollaborativeProjectList] --> B[CollaborativeProjectDetail]
    B --> C[TaskBoard]
    B --> D[CollaborativeEditor]
    B --> E[RealtimeChat]
    B --> F[ProjectMembers]
    B --> G[ProjectSettings]
    
    C --> H[TaskCard]
    C --> I[CreateTaskModal]
    
    D --> J[RealtimeCursor]
    D --> K[CommentPanel]
    D --> L[VersionHistory]
    
    E --> M[MessageInput]
    E --> N[MessageList]
    E --> O[FilePreview]
    
    P[Pinia Store] --> A
    P --> B
    P --> C
    P --> D
    P --> E
    
    Q[API Service] --> P
    R[WebSocket Service] --> D
    R --> E
```

## API Design and Integration

### API Integration Strategy
**API Integration Strategy:** 扩展现有的 RESTful API 架构，添加协作相关的端点，遵循现有的 API 设计模式和规范
**Authentication:** 使用现有的 JWT 认证机制，确保 API 安全性
**Versioning:** 遵循现有的版本控制策略，保持向后兼容性

### New API Endpoints

#### Project Management APIs
##### GET /api/collaborative/projects
- **Purpose:** 获取用户参与的协作项目列表
- **Integration:** 与现有的用户系统集成，验证用户身份和权限
- **Request:**
  ```json
  {
    "page": 1,
    "limit": 20,
    "filter": {
      "status": "active",
      "role": "owner"
    }
  }
  ```
- **Response:**
  ```json
  {
    "code": 200,
    "data": {
      "projects": [
        {
          "id": "project-123",
          "name": "数学学习项目",
          "description": "高中数学协作学习项目",
          "ownerId": "user-456",
          "status": "active",
          "createdAt": "2023-08-26T10:00:00Z",
          "updatedAt": "2023-08-26T15:30:00Z",
          "memberCount": 5,
          "taskCount": 12
        }
      ],
      "total": 1,
      "page": 1,
      "limit": 20
    }
  }
  ```

##### POST /api/collaborative/projects
- **Purpose:** 创建新的协作项目
- **Integration:** 与现有的用户系统集成，设置项目所有者
- **Request:**
  ```json
  {
    "name": "数学学习项目",
    "description": "高中数学协作学习项目",
    "settings": {
      "permissions": {
        "defaultRole": "editor",
        "allowInvites": true
      }
    }
  }
  ```
- **Response:**
  ```json
  {
    "code": 200,
    "data": {
      "id": "project-123",
      "name": "数学学习项目",
      "description": "高中数学协作学习项目",
      "ownerId": "user-456",
      "status": "active",
      "createdAt": "2023-08-26T10:00:00Z",
      "updatedAt": "2023-08-26T10:00:00Z",
      "settings": {
        "permissions": {
          "defaultRole": "editor",
          "allowInvites": true
        }
      }
    }
  }
  ```

#### Task Management APIs
##### GET /api/collaborative/projects/{projectId}/tasks
- **Purpose:** 获取项目任务列表
- **Integration:** 与现有的项目系统集成，验证项目访问权限
- **Request:**
  ```json
  {
    "filter": {
      "status": "in_progress",
      "assigneeId": "user-789"
    },
    "sort": "priority"
  }
  ```
- **Response:**
  ```json
  {
    "code": 200,
    "data": {
      "tasks": [
        {
          "id": "task-456",
          "projectId": "project-123",
          "title": "完成数学练习",
          "description": "完成第三章的数学练习题",
          "assigneeId": "user-789",
          "status": "in_progress",
          "priority": "high",
          "dueDate": "2023-09-01T23:59:59Z",
          "createdAt": "2023-08-26T12:00:00Z",
          "updatedAt": "2023-08-26T14:30:00Z"
        }
      ]
    }
  }
  ```

##### POST /api/collaborative/projects/{projectId}/tasks
- **Purpose:** 创建新任务
- **Integration:** 与现有的项目系统集成，验证项目编辑权限
- **Request:**
  ```json
  {
    "title": "完成数学练习",
    "description": "完成第三章的数学练习题",
    "assigneeId": "user-789",
    "priority": "high",
    "dueDate": "2023-09-01T23:59:59Z"
  }
  ```
- **Response:**
  ```json
  {
    "code": 200,
    "data": {
      "id": "task-456",
      "projectId": "project-123",
      "title": "完成数学练习",
      "description": "完成第三章的数学练习题",
      "assigneeId": "user-789",
      "status": "todo",
      "priority": "high",
      "dueDate": "2023-09-01T23:59:59Z",
      "createdAt": "2023-08-26T12:00:00Z",
      "updatedAt": "2023-08-26T12:00:00Z"
    }
  }
  ```

#### Document Collaboration APIs
##### GET /api/collaborative/projects/{projectId}/documents
- **Purpose:** 获取项目文档列表
- **Integration:** 与现有的项目系统集成，验证项目访问权限
- **Request:** N/A
- **Response:**
  ```json
  {
    "code": 200,
    "data": {
      "documents": [
        {
          "id": "doc-789",
          "projectId": "project-123",
          "name": "数学笔记",
          "type": "markdown",
          "version": 3,
          "lastEditedBy": "user-456",
          "lastEditedAt": "2023-08-26T16:45:00Z",
          "createdAt": "2023-08-26T11:00:00Z"
        }
      ]
    }
  }
  ```

##### GET /api/collaborative/documents/{documentId}
- **Purpose:** 获取文档内容和元数据
- **Integration:** 与现有的文档处理系统集成，支持不同文档格式
- **Request:** N/A
- **Response:**
  ```json
  {
    "code": 200,
    "data": {
      "id": "doc-789",
      "projectId": "project-123",
      "name": "数学笔记",
      "type": "markdown",
      "content": "# 数学笔记\n\n## 第三章\n\n主要内容...",
      "version": 3,
      "lastEditedBy": "user-456",
      "lastEditedAt": "2023-08-26T16:45:00Z",
      "createdAt": "2023-08-26T11:00:00Z"
    }
  }
  ```

##### PUT /api/collaborative/documents/{documentId}
- **Purpose:** 更新文档内容
- **Integration:** 与现有的文档处理系统集成，支持版本控制
- **Request:**
  ```json
  {
    "content": "# 数学笔记\n\n## 第三章\n\n更新后的内容...",
    "changeDescription": "添加了新的练习题"
  }
  ```
- **Response:**
  ```json
  {
    "code": 200,
    "data": {
      "id": "doc-789",
      "projectId": "project-123",
      "name": "数学笔记",
      "type": "markdown",
      "content": "# 数学笔记\n\n## 第三章\n\n更新后的内容...",
      "version": 4,
      "lastEditedBy": "user-456",
      "lastEditedAt": "2023-08-26T17:00:00Z",
      "createdAt": "2023-08-26T11:00:00Z"
    }
  }
  ```

#### Real-time Collaboration APIs
##### WebSocket /ws/collaborative/documents/{documentId}
- **Purpose:** 实时文档协作的 WebSocket 连接
- **Integration:** 与现有的 WebSocket 服务集成，支持实时通信
- **Message Format (Client to Server):**
  ```json
  {
    "type": "document_change",
    "data": {
      "operation": "insert",
      "position": 150,
      "text": "新增内容",
      "version": 4
    }
  }
  ```
- **Message Format (Server to Client):**
  ```json
  {
    "type": "document_update",
    "data": {
      "operation": "insert",
      "position": 150,
      "text": "新增内容",
      "version": 5,
      "userId": "user-789",
      "timestamp": "2023-08-26T17:05:00Z"
    }
  }
  ```

##### WebSocket /ws/collaborative/projects/{projectId}/chat
- **Purpose:** 项目实时聊天的 WebSocket 连接
- **Integration:** 与现有的聊天系统集成，支持实时消息传递
- **Message Format (Client to Server):**
  ```json
  {
    "type": "chat_message",
    "data": {
      "content": "大家好，我刚刚更新了文档",
      "mentions": ["user-789"]
    }
  }
  ```
- **Message Format (Server to Client):**
  ```json
  {
    "type": "new_message",
    "data": {
      "id": "msg-123",
      "projectId": "project-123",
      "userId": "user-456",
      "content": "大家好，我刚刚更新了文档",
      "mentions": ["user-789"],
      "timestamp": "2023-08-26T17:10:00Z"
    }
  }
  ```

## External API Integration

### WebSocket Service API
- **Purpose:** 实现实时协作功能的核心通信服务
- **Documentation:** 内部 WebSocket 服务文档
- **Base URL:** ws://localhost:1003/ws
- **Authentication:** JWT Token in connection URL
- **Integration Method:** 创建专用的 WebSocket 服务类，集成到现有的通信架构中

**Key Endpoints Used:**
- `CONNECT /ws/collaborative/documents/{documentId}` - 文档协作连接
- `CONNECT /ws/collaborative/projects/{projectId}/chat` - 项目聊天连接
- `SUBSCRIBE /ws/collaborative/projects/{projectId}/updates` - 项目更新订阅

**Error Handling:**
- 实现自动重连机制，处理连接中断
- 提供连接状态反馈，显示在线/离线状态
- 记录连接错误和异常，便于调试和监控

## Source Tree Integration

### Existing Project Structure
```
src/
├── api/                    # API 接口定义
│   ├── index.ts           # 通用 API 接口
│   ├── agentOrchestration.ts
│   ├── applicationPage.ts
│   ├── coCreationPage.ts
│   ├── courseware.ts
│   ├── knowledgeFactory.ts
│   ├── loadingPage.ts
│   ├── orchestration.ts
│   ├── promptManagement.ts
│   ├── tankChat.ts
│   ├── teachPlan.ts
│   ├── tools.ts
│   └── workShop.ts
├── assets/                # 静态资源
├── components/            # 通用组件
│   ├── common/           # 通用组件
│   ├── custom/           # 自定义组件
│   └── weboffice/        # 办公组件
├── hooks/                 # Vue Hooks
├── locales/              # 国际化
├── router/               # 路由配置
├── store/                # 状态管理
│   ├── modules/         # Store 模块
│   │   ├── app/
│   │   ├── auth/
│   │   ├── chat/
│   │   ├── info/
│   │   ├── loadingPage/
│   │   ├── orchestration/
│   │   ├── prompt/
│   │   ├── settings/
│   │   ├── stretchout/
│   │   ├── toolboxPage/
│   │   ├── tools/
│   │   └── user/
│   ├── helper.ts
│   └── index.ts
├── styles/               # 样式文件
├── types/                # TypeScript 类型定义
├── typings/              # 类型声明
├── utils/                # 工具函数
│   ├── auth/
│   ├── encryption/
│   ├── file/
│   ├── functions/
│   ├── is/
│   ├── microFrontEnd/
│   ├── orchestration/
│   ├── request/
│   └── storage/
└── views/                # 页面组件
    ├── agentOrchestration/
    ├── applicationPage/
    ├── chat/
    ├── classroom/
    ├── coCreationPage/
    ├── courseware/
    ├── exception/
    ├── historyPage/
    ├── iframePage/
    ├── knowledgeFactoryPage/
    ├── loadingPage/
    ├── previewPage/
    ├── promptManagement/
    ├── tankChat/
    ├── teachPlanContentGen/
    ├── toolboxPage/
    ├── videoStudyHelper/
    └── workShopPage/
```

### New File Organization
```
src/
├── api/                    # API 接口定义
│   ├── index.ts           # 现有通用 API 接口
│   ├── collaborative.ts   # 新增：协作相关 API 接口
│   └── ...                # 其他现有 API 接口
├── components/            # 通用组件
│   ├── common/           # 现有通用组件
│   ├── collaborative/    # 新增：协作相关组件
│   │   ├── CollaborativeEditor.vue      # 协作编辑器
│   │   ├── RealtimeChat.vue            # 实时聊天
│   │   ├── TaskBoard.vue               # 任务看板
│   │   ├── ProjectMembers.vue          # 项目成员管理
│   │   ├── ProjectDocuments.vue        # 项目文档
│   │   ├── ProjectSettings.vue         # 项目设置
│   │   ├── RealtimeCursor.vue          # 实时光标
│   │   ├── CommentPanel.vue            # 评论面板
│   │   ├── VersionHistory.vue          # 版本历史
│   │   ├── TaskCard.vue                # 任务卡片
│   │   ├── TaskColumn.vue              # 任务列
│   │   ├── CreateTaskModal.vue         # 创建任务模态框
│   │   ├── MessageInput.vue            # 消息输入
│   │   ├── MessageList.vue            # 消息列表
│   │   └── FilePreview.vue            # 文件预览
│   ├── custom/           # 现有自定义组件
│   └── weboffice/        # 现有办公组件
├── store/                # 状态管理
│   ├── modules/         # Store 模块
│   │   ├── app/         # 现有
│   │   ├── auth/        # 现有
│   │   ├── chat/        # 现有
│   │   ├── info/        # 现有
│   │   ├── loadingPage/ # 现有
│   │   ├── orchestration/ # 现有
│   │   ├── prompt/      # 现有
│   │   ├── settings/    # 现有
│   │   ├── stretchout/  # 现有
│   │   ├── toolboxPage/ # 现有
│   │   ├── tools/       # 现有
│   │   ├── user/        # 现有
│   │   └── collaborative/ # 新增：协作相关状态管理
│   │       ├── index.ts
│   │       ├── projects.ts
│   │       ├── tasks.ts
│   │       ├── documents.ts
│   │       ├── chat.ts
│   │       └── members.ts
│   ├── helper.ts        # 现有
│   └── index.ts         # 现有
├── utils/                # 工具函数
│   ├── auth/            # 现有
│   ├── encryption/      # 现有
│   ├── file/            # 现有
│   ├── functions/       # 现有
│   ├── is/              # 现有
│   ├── microFrontEnd/   # 现有
│   ├── orchestration/   # 现有
│   ├── request/         # 现有
│   ├── storage/         # 现有
│   └── collaborative/   # 新增：协作相关工具函数
│       ├── websocket.ts     # WebSocket 服务
│       ├── ot.js            # Operational Transformation 算法
│       ├── conflict-resolution.js # 冲突解决
│       └── index.ts
└── views/                # 页面组件
    ├── agentOrchestration/ # 现有
    ├── applicationPage/    # 现有
    ├── chat/              # 现有
    ├── classroom/         # 现有
    ├── coCreationPage/    # 现有
    ├── courseware/        # 现有
    ├── exception/         # 现有
    ├── historyPage/       # 现有
    ├── iframePage/        # 现有
    ├── knowledgeFactoryPage/ # 现有
    ├── loadingPage/       # 现有
    ├── previewPage/       # 现有
    ├── promptManagement/  # 现有
    ├── tankChat/          # 现有
    ├── teachPlanContentGen/ # 现有
    ├── toolboxPage/       # 现有
    ├── videoStudyHelper/  # 现有
    ├── workShopPage/      # 现有
    └── collaborative/     # 新增：协作相关页面
        ├── index.vue               # 协作项目列表
        ├── project-detail.vue      # 项目详情页
        ├── document-editor.vue     # 文档编辑器
        ├── task-board.vue          # 任务看板
        ├── project-chat.vue        # 项目聊天
        ├── project-settings.vue    # 项目设置
        └── project-members.vue     # 项目成员管理
```

### Integration Guidelines
- **File Naming:** 遵循现有的 PascalCase 命名约定，组件文件使用 PascalCase.vue，工具函数文件使用 camelCase.ts
- **Folder Organization:** 保持现有的模块化组织结构，新功能作为独立模块添加
- **Import/Export Patterns:** 使用现有的 ES6 模块导入导出模式，保持路径别名 @ 指向 src 目录

## Infrastructure and Deployment Integration

### Existing Infrastructure
**Current Deployment:** Vite 构建输出到 agent 目录，支持 Docker 容器化部署和 Nginx 反向代理
**Infrastructure Tools:** Docker, Docker Compose, Nginx, Node.js
**Environments:** 开发、测试、生产环境通过环境变量配置

### Enhancement Deployment Strategy
**Deployment Approach:** 使用现有的 Docker 容器化部署流程，通过环境变量配置协作功能
**Infrastructure Changes:** 
- 添加 WebSocket 服务支持，需要配置 Nginx 反向代理支持 WebSocket 协议
- 可能需要增加服务器资源，特别是内存和 CPU，以支持实时协作功能
- 添加文件存储支持，用于文档和文件共享功能

**Pipeline Integration:** 
- 扩展现有的 CI/CD 流程，添加协作功能的构建和测试步骤
- 添加 WebSocket 服务的健康检查和监控
- 实现协作功能的数据备份和恢复策略

### Rollback Strategy
**Rollback Method:** 使用现有的 Docker 镜像版本控制，支持快速回滚到上一个稳定版本
**Risk Mitigation:** 
- 实现功能开关，可以通过配置文件启用或禁用协作功能
- 分阶段发布，先在小规模用户中测试，然后逐步扩大范围
- 监控系统性能和错误率，设置自动告警机制

**Monitoring:** 
- 扩展现有的监控系统，添加协作功能的性能指标和错误监控
- 监控 WebSocket 连接数、消息吞吐量和响应时间
- 实现用户行为分析，跟踪协作功能的使用情况

## Coding Standards and Conventions

### Existing Standards Compliance
**Code Style:** 使用 ESLint 和 Prettier 进行代码格式化，遵循现有的代码风格配置
**Linting Rules:** 使用 .eslintrc.cjs.disabled 配置文件，禁用了一些规则但保持基本的代码质量检查
**Testing Patterns:** 项目目前没有明显的测试框架配置，新功能应该添加单元测试和集成测试
**Documentation Style:** 使用 JSDoc 风格的注释，特别是对于公共 API 和复杂函数

### Enhancement-Specific Standards
- **WebSocket 连接管理:** 实现连接池和自动重连机制，确保连接稳定性
- **实时数据同步:** 使用 Operational Transformation 算法处理并发编辑冲突
- **错误处理:** 实现统一的错误处理机制，包括网络错误、权限错误和数据冲突错误
- **性能优化:** 实现虚拟滚动、懒加载和防抖/节流等技术，优化大型项目性能

### Critical Integration Rules
- **Existing API Compatibility:** 所有新的 API 调用必须遵循现有的认证、错误处理和响应格式
- **Database Integration:** 通过现有的 API 层访问数据，不直接修改数据库结构
- **Error Handling:** 使用现有的错误处理机制，包括全局错误拦截和用户友好的错误提示
- **Logging Consistency:** 使用现有的日志系统，记录关键操作和错误信息

## Testing Strategy

### Integration with Existing Tests
**Existing Test Framework:** 项目目前没有明确的测试框架配置，需要引入测试框架
**Test Organization:** 在项目根目录创建 tests 目录，按照模块组织测试文件
**Coverage Requirements:** 目标是达到 80% 以上的代码覆盖率，特别是核心协作功能

### New Testing Requirements
#### Unit Tests for New Components
- **Framework:** 使用 Vitest 作为单元测试框架，与 Vite 构建工具集成
- **Location:** tests/unit 目录，按照组件和工具函数组织测试文件
- **Coverage Target:** 80% 以上的代码覆盖率
- **Integration with Existing:** 测试现有组件与新组件的交互，确保兼容性

#### Integration Tests
- **Scope:** 测试协作功能的端到端流程，包括项目管理、任务分配、文档协作和实时聊天
- **Existing System Verification:** 验证新功能不会破坏现有功能，特别是用户认证、导航和基本功能
- **New Feature Testing:** 测试协作功能的核心场景，包括多人同时编辑、任务分配和实时通信

#### Regression Tests
- **Existing Feature Verification:** 创建回归测试套件，验证现有功能在新功能添加后仍然正常工作
- **Automated Regression Suite:** 使用 Playwright 或 Cypress 进行端到端回归测试
- **Manual Testing Requirements:** 定义关键用户场景的手动测试流程，确保用户体验一致性

## Security Integration

### Existing Security Measures
**Authentication:** 使用 JWT Token 进行用户认证，通过现有的认证中间件验证用户身份
**Authorization:** 基于角色的访问控制，通过用户角色限制功能访问
**Data Protection:** 使用 HTTPS 加密数据传输，敏感数据在存储前进行加密
**Security Tools:** 使用现有的安全中间件和验证机制，包括输入验证和 XSS 防护

### Enhancement Security Requirements
**New Security Measures:** 
- 实现 WebSocket 连接的安全认证，确保只有授权用户可以建立连接
- 添加项目级别的权限控制，包括项目创建、编辑、查看和删除权限
- 实现文档版本控制和访问历史，支持审计和回滚
- 添加文件上传的安全检查，防止恶意文件上传

**Integration Points:** 
- 与现有的认证系统集成，确保 WebSocket 连接使用相同的认证机制
- 与现有的权限系统集成，扩展角色和权限模型以支持协作功能
- 与现有的日志系统集成，记录协作功能的安全事件和用户操作

**Compliance Requirements:** 
- 遵循数据保护法规，确保用户数据的安全和隐私
- 实现数据备份和恢复策略，防止数据丢失
- 支持数据导出和删除，满足用户数据权利要求

### Security Testing
**Existing Security Tests:** 扩展现有的安全测试，包括输入验证、权限检查和数据保护测试
**New Security Test Requirements:** 
- 测试 WebSocket 连接的安全性和认证机制
- 验证项目权限控制的正确性
- 测试文件上传和处理的安全性
- 进行渗透测试，识别潜在的安全漏洞

**Penetration Testing:** 
- 对协作功能进行专业的渗透测试，特别是实时通信和文件处理功能
- 测试常见的安全漏洞，包括 XSS、CSRF 和注入攻击
- 验证数据加密和传输的安全性

## Checklist Results Report

### Architect Checklist for Brownfield Enhancement

#### ✅ Project Analysis and Understanding
- [x] 深入分析了现有项目结构和技术栈
- [x] 理解了现有架构模式和设计原则
- [x] 识别了现有系统的约束和限制
- [x] 验证了增强功能的复杂性和必要性

#### ✅ Integration Strategy
- [x] 定义了与现有系统的集成方法
- [x] 确保了新功能与现有架构的一致性
- [x] 识别了关键的集成点和依赖关系
- [x] 制定了向后兼容性策略

#### ✅ Technical Design
- [x] 设计了符合现有技术栈的新组件
- [x] 定义了清晰的数据模型和接口
- [x] 规划了实时协作功能的技术实现
- [x] 考虑了性能和可扩展性要求

#### ✅ Security and Compliance
- [x] 与现有安全框架集成
- [x] 实现了适当的权限控制
- [x] 考虑了数据保护和隐私要求
- [x] 规划了安全测试和验证

#### ✅ Deployment and Operations
- [x] 与现有部署流程集成
- [x] 制定了监控和日志策略
- [x] 规划了回滚和恢复机制
- [x] 考虑了基础设施需求

## Next Steps

### Story Manager Handoff
基于此架构文档，Story Manager 应该优先实现以下用户故事：

1. **项目管理功能** - 创建协作项目的基础框架，包括项目创建、成员管理和基本设置
2. **任务管理系统** - 实现任务看板和任务管理功能，支持任务创建、分配和状态跟踪
3. **文档协作功能** - 实现基础的文档编辑和共享功能，为实时协作做准备
4. **实时聊天功能** - 实现项目内的实时聊天功能，支持文字消息和文件分享

**关键集成要求：**
- 与现有的用户认证系统集成，确保安全性
- 与现有的 UI 组件库集成，保持视觉一致性
- 与现有的状态管理系统集成，确保数据一致性
- 与现有的 API 架构集成，遵循现有的设计模式

**现有系统约束：**
- 必须使用 Naive UI 组件库，不能引入新的 UI 框架
- 必须遵循现有的文件组织结构和命名约定
- 必须使用 Pinia 进行状态管理，不能引入新的状态管理库
- 必须遵循现有的 API 设计模式，包括错误处理和响应格式

**第一故事建议：**
建议首先实现"协作项目列表"功能，包括项目创建、项目列表展示和项目详情页面。这个故事相对独立，可以作为其他功能的基础，同时能够快速验证集成策略的正确性。

### Developer Handoff
开发者开始实现协作学习平台功能时，请参考以下指导：

**架构文档参考：**
- 本架构文档提供了全面的技术设计和集成指导
- 现有项目的编码标准和规范在 .eslintrc.cjs.disabled 和其他配置文件中定义
- 现有的组件和工具函数可以作为参考，确保代码一致性

**集成要求：**
- 所有新的 API 调用必须通过现有的请求工具函数进行，使用相同的错误处理和拦截器
- 新的状态管理模块必须遵循现有的 Pinia 模式，使用相同的 helper 函数
- 新组件必须使用 Naive UI 组件库，遵循现有的样式和设计规范
- 所有新功能必须支持多语言，集成到现有的 Vue I18n 系统中

**技术决策基于实际项目约束：**
- 使用 WebSocket 实现实时协作功能，与现有的 HTTP API 架构并行
- 使用 Operational Transformation 算法处理文档协作冲突，确保数据一致性
- 采用组件化设计，确保新功能可以独立开发和测试
- 实现渐进式增强，先实现基础功能，再添加高级特性

**现有系统兼容性验证步骤：**
1. 验证新功能不会破坏现有的路由和导航
2. 确保新功能与现有的认证和权限系统兼容
3. 测试新功能在不同设备和浏览器上的表现
4. 验证新功能的性能不会显著影响现有功能

**实现顺序建议：**
1. 首先实现基础的项目管理功能（创建、列表、详情）
2. 然后实现任务管理功能（任务看板、任务操作）
3. 接着实现文档协作功能（编辑、版本控制）
4. 最后实现实时通信功能（聊天、实时编辑）

每个阶段都应该进行充分的测试，确保与现有系统的兼容性和功能的稳定性。