# Vue Flow智能体编排系统 - 数据保存与加载机制详解

## 🏗️ 整体架构设计

### 分层架构
```
┌─────────────────┐
│   UI层 (Vue)    │ ← 用户交互界面
├─────────────────┤
│ 状态管理 (Store) │ ← 数据状态管理
├─────────────────┤
│  数据转换层      │ ← 格式转换处理
├─────────────────┤
│   API接口层     │ ← 网络请求封装
├─────────────────┤
│   存储层        │ ← 数据持久化
└─────────────────┘
```

## 🔄 数据转换机制

### 前端格式 vs 后端格式

**前端格式 (FlowData)**：
```typescript
interface FlowData {
  id: string
  name: string
  nodes: FlowNode[]  // 包含UI属性：position, style, draggable等
  edges: FlowEdge[]  // 包含UI属性：animated, style等
  // ... 其他UI相关属性
}
```

**后端格式 (BackendFlowData)**：
```typescript
interface BackendFlowData {
  id: string
  name: string
  nodes: BackendNodeData[]  // 只保留业务逻辑数据
  edges: BackendEdgeData[]  // 只保留连接关系
  variables: FlowVariable[] // 流程变量
  metadata: {               // 元数据信息
    version: string
    createdAt: string
    updatedAt: string
  }
}
```

### 转换过程

**保存时转换 (transformToBackendFormat)**：
```typescript
export function transformToBackendFormat(frontendFlow: FlowData): BackendFlowData {
  return {
    id: frontendFlow.id,
    name: frontendFlow.name,
    nodes: frontendFlow.nodes.map(transformNodeToBackend), // 移除UI属性
    edges: frontendFlow.edges.map(transformEdgeToBackend), // 移除UI属性
    variables: extractFlowVariables(frontendFlow),         // 提取变量
    metadata: {
      version: '1.0',
      createdAt: frontendFlow.createdAt,
      updatedAt: frontendFlow.updatedAt
    }
  }
}
```

**加载时转换 (transformFromBackendFormat)**：
```typescript
export function transformFromBackendFormat(backendFlow: BackendFlowData): FlowData {
  return {
    id: backendFlow.id,
    name: backendFlow.name,
    nodes: backendFlow.nodes.map(transformNodeFromBackend), // 重建UI属性
    edges: backendFlow.edges.map(transformEdgeFromBackend), // 重建UI属性
    createdAt: backendFlow.metadata.createdAt,
    updatedAt: backendFlow.metadata.updatedAt
  }
}
```

## 💾 存储设计

### 当前模拟存储 (localStorage)
```typescript
const STORAGE_KEYS = {
  FLOWS: 'orchestration_flows',        // 流程数据
  TEMPLATES: 'orchestration_templates', // 模板数据
  EXECUTIONS: 'orchestration_executions' // 执行记录
}

// 存储结构
localStorage['orchestration_flows'] = [
  {
    id: "flow_123",
    name: "情感分析流程",
    nodes: [...],
    edges: [...],
    metadata: { ... }
  }
]
```

### 真实后端存储设计
```sql
-- 流程主表
CREATE TABLE flows (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  user_id VARCHAR(50),
  status ENUM('draft', 'published', 'archived')
);

-- 节点数据表
CREATE TABLE flow_nodes (
  id VARCHAR(50) PRIMARY KEY,
  flow_id VARCHAR(50),
  type VARCHAR(50),
  label VARCHAR(200),
  config JSON,
  FOREIGN KEY (flow_id) REFERENCES flows(id)
);

-- 边数据表
CREATE TABLE flow_edges (
  id VARCHAR(50) PRIMARY KEY,
  flow_id VARCHAR(50),
  source_node VARCHAR(50),
  target_node VARCHAR(50),
  condition JSON,
  FOREIGN KEY (flow_id) REFERENCES flows(id)
);
```

## 🌐 API接口设计

### 当前模拟API
```typescript
// 保存流程 (模拟实现)
export async function saveFlowApi(flowData: BackendFlowData): Promise<ApiResponse> {
  await mockDelay() // 模拟网络延迟
  
  // 保存到localStorage
  const flows = getStorageData<BackendFlowData[]>(STORAGE_KEYS.FLOWS, [])
  flows.push(flowData)
  setStorageData(STORAGE_KEYS.FLOWS, flows)
  
  return createMockResponse({ id: flowData.id }, '流程保存成功')
}
```

### 真实API接口
```typescript
// 保存流程 (真实实现)
export function saveFlowApi(flowData: BackendFlowData) {
  return post<ApiResponse>({
    url: '/api/orchestration/flows',
    data: flowData,
    headers: { 'Content-Type': 'application/json' }
  })
}
```

## 🗃️ 状态管理 (Pinia Store)

### 后端交互状态
```typescript
interface OrchestrationState {
  currentFlow: FlowData | null
  flows: FlowData[]
  backend: {
    isLoading: boolean      // 是否正在加载
    isSyncing: boolean      // 是否正在同步
    lastSyncTime: string    // 最后同步时间
    syncStatus: 'idle' | 'syncing' | 'success' | 'error'
    lastError: string | null
  }
}
```

### 核心方法
```typescript
// 保存到后端
async saveFlowToBackend(): Promise<void> {
  // 1. 转换为后端格式
  const backendData = this.exportBackendFlow()
  
  // 2. 调用API保存
  const response = await saveFlowApi(backendData)
  
  // 3. 更新同步状态
  this.backend.syncStatus = 'success'
  this.backend.lastSyncTime = new Date().toISOString()
}

// 从后端加载
async autoLoadDefaultFlow(): Promise<void> {
  // 1. 调用API加载
  const response = await loadDefaultFlowApi()
  
  // 2. 转换为前端格式
  const frontendFlow = transformFromBackendFormat(response.data)
  
  // 3. 导入到当前状态
  this.importFlow(frontendFlow)
}
```

## 🔄 完整的数据流转过程

### 保存流程
```
用户点击"保存到后端"
        ↓
UI层调用 store.saveFlowToBackend()
        ↓
Store调用 exportBackendFlow()
        ↓
transformToBackendFormat() 转换数据
        ↓
移除UI属性 (position, style, draggable等)
        ↓
保留业务数据 (config, inputParams, outputParams等)
        ↓
调用 saveFlowApi() 发送请求
        ↓
模拟网络延迟 (200-800ms)
        ↓
保存到 localStorage['orchestration_flows']
        ↓
返回成功响应
        ↓
更新同步状态和时间戳
        ↓
UI显示保存成功消息
```

### 加载流程
```
页面初始化 onMounted()
        ↓
调用 store.autoLoadDefaultFlow()
        ↓
调用 loadDefaultFlowApi()
        ↓
从 localStorage 读取数据
        ↓
模拟网络延迟
        ↓
返回后端格式数据
        ↓
transformFromBackendFormat() 转换数据
        ↓
重建UI属性 (position: {x:0, y:0}, draggable: true等)
        ↓
调用 importBackendFlow()
        ↓
验证数据格式
        ↓
导入到当前流程
        ↓
UI自动更新显示
```

## 🎯 节点配置保存机制

### 配置数据清理
```typescript
export function sanitizeNodeConfig(config: Record<string, any>, nodeType: NodeType): NodeConfig {
  const sanitizedConfig = { ...config }
  
  // 移除UI相关属性
  delete sanitizedConfig.position
  delete sanitizedConfig.style
  delete sanitizedConfig.selected
  delete sanitizedConfig.dragging
  
  // 移除临时状态
  delete sanitizedConfig.tempData
  delete sanitizedConfig.debugInfo
  delete sanitizedConfig.isEditing
  
  // 根据节点类型特定处理
  switch (nodeType) {
    case NodeType.API:
      return sanitizeAPIConfig(sanitizedConfig)
    // ...
  }
}
```

### LLM节点配置示例
```typescript
// 前端完整配置
{
  model: "gpt-4",
  prompt: "你是一个AI助手",
  maxTokens: 2000,
  temperature: 0.7,
  // UI相关 (会被移除)
  isEditing: true,
  tempPrompt: "临时提示词",
  uiState: { expanded: true }
}

// 后端保存配置
{
  model: "gpt-4",
  prompt: "你是一个AI助手", 
  maxTokens: 2000,
  temperature: 0.7
}
```

## 🎛️ 面板节点配置信息存储

### 配置面板数据结构

#### 1. 节点配置数据存储
```typescript
interface NodeData {
  label: string
  description?: string
  config: NodeConfig           // 核心配置数据
  inputParams?: ParameterDefinition[]
  outputParams?: ParameterDefinition[]
  
  // UI状态 (不保存到后端)
  status: NodeStatus
  executionLogs: NodeExecutionLog[]
  lastExecution: any
  totalExecutions: number
}
```

#### 2. 不同节点类型的配置结构

**LLM节点配置**：
```typescript
interface LLMNodeConfig {
  model: string              // 模型选择: "gpt-4", "gpt-3.5-turbo"
  prompt: string             // 用户提示词
  systemPrompt?: string      // 系统提示词
  maxTokens: number          // 最大Token数
  temperature: number        // 温度参数 0-1
  topP?: number             // Top-P参数
  frequencyPenalty?: number  // 频率惩罚
  presencePenalty?: number   // 存在惩罚
  stopSequences?: string[]   // 停止序列
}
```

**API节点配置**：
```typescript
interface APINodeConfig {
  url: string                // API地址
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers: Record<string, string>  // 请求头
  params: Record<string, any>      // 请求参数
  body?: any                       // 请求体
  timeout: number                  // 超时时间
  retryCount: number              // 重试次数
  authentication?: {              // 认证信息
    type: 'none' | 'bearer' | 'basic' | 'apikey'
    token?: string
    username?: string
    password?: string
    apiKey?: string
  }
}
```

**条件判断节点配置**：
```typescript
interface ConditionNodeConfig {
  conditions: ConditionRule[]     // 条件规则列表
  logicOperator: 'AND' | 'OR'    // 逻辑操作符
  defaultBranch: string          // 默认分支
}

interface ConditionRule {
  id: string
  variable: string               // 变量名
  operator: '==' | '!=' | '>' | '<' | '>=' | '<=' | 'contains' | 'startsWith' | 'endsWith'
  value: any                     // 比较值
  dataType: 'string' | 'number' | 'boolean' | 'array' | 'object'
}
```

**问题分类器节点配置**：
```typescript
interface QuestionClassifierConfig {
  categories: ClassificationCategory[]  // 分类类别
  model: string                        // 分类模型
  threshold: number                    // 置信度阈值
  fallbackCategory: string            // 默认分类
}

interface ClassificationCategory {
  id: string
  name: string                         // 分类名称
  description: string                  // 分类描述
  keywords: string[]                   // 关键词
  examples: string[]                   // 示例问题
  outputHandle: string                 // 输出连接点ID
}
```

**知识库节点配置**：
```typescript
interface KnowledgeNodeConfig {
  databases: KnowledgeDatabase[]       // 知识库列表
  searchMode: 'semantic' | 'keyword' | 'hybrid'  // 搜索模式
  topK: number                         // 返回结果数量
  scoreThreshold: number               // 相似度阈值
  rerank: boolean                      // 是否重排序
}

interface KnowledgeDatabase {
  id: string
  name: string                         // 知识库名称
  description: string                  // 知识库描述
  type: 'vector' | 'graph' | 'document'  // 知识库类型
  config: {
    endpoint?: string                  // 知识库接口
    apiKey?: string                    // API密钥
    indexName?: string                 // 索引名称
  }
}
```

### 配置数据的保存流程

#### 1. 用户在配置面板中修改配置
```typescript
// NodeConfigPanel.vue
const handleSave = () => {
  // 验证配置数据
  const isValid = validateNodeConfig(formData.value.config, node.value.type)
  
  if (isValid) {
    // 保存配置到节点
    emit('save', {
      nodeId: node.value.id,
      config: formData.value.config,
      inputParams: formData.value.inputParams,
      outputParams: formData.value.outputParams
    })
  }
}
```

#### 2. Store更新节点配置
```typescript
// orchestration store
updateNodeConfig(nodeId: string, config: NodeConfig): void {
  if (!this.currentFlow) return
  
  const nodeIndex = this.currentFlow.nodes.findIndex(n => n.id === nodeId)
  if (nodeIndex >= 0) {
    // 更新节点配置
    this.currentFlow.nodes[nodeIndex].data.config = config
    this.currentFlow.updatedAt = new Date().toISOString()
    
    // 记录历史状态
    this.recordState()
    
    // 保存到本地
    this.saveCurrentFlow()
  }
}
```

#### 3. 转换为后端格式时的配置处理
```typescript
export function transformNodeToBackend(node: FlowNode): BackendNodeData {
  return {
    id: node.id,
    type: node.type,
    label: node.data.label,
    description: node.data.description,
    config: sanitizeNodeConfig(node.data.config, node.type), // 清理配置
    inputParams: node.data.inputParams,
    outputParams: node.data.outputParams
  }
}

```

### 配置数据的存储结构

#### localStorage中的存储格式
```json
{
  "orchestration_flows": [
    {
      "id": "flow_123",
      "name": "智能客服流程",
      "nodes": [
        {
          "id": "condition_node_1", 
          "type": "condition",
          "label": "意图分支",
          "config": {
            "conditions": [
              {
                "id": "cond_1",
                "variable": "intent",
                "operator": "==",
                "value": "查询订单",
                "dataType": "string"
              }
            ],
            "logicOperator": "AND",
            "defaultBranch": "other"
          }
        }
      ]
    }
  ]
}
```
```

### 配置验证机制

#### 1. 前端验证
```typescript
export function validateLLMConfig(config: any): ValidationResult {
  const errors: ValidationError[] = []
  
  // 验证必填字段
  if (!config.model) {
    errors.push({ field: 'model', message: '模型不能为空' })
  }
  
  if (!config.prompt) {
    errors.push({ field: 'prompt', message: '提示词不能为空' })
  }
  
  // 验证数值范围
  if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 8192)) {
    errors.push({ field: 'maxTokens', message: 'Token数量必须在1-8192之间' })
  }
  
  if (config.temperature && (config.temperature < 0 || config.temperature > 1)) {
    errors.push({ field: 'temperature', message: '温度参数必须在0-1之间' })
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}
```

#### 2. 后端验证
```typescript
// API层验证
export async function validateFlowApi(flowData: BackendFlowData): Promise<ApiResponse> {
  const validation = validateBackendFlowData(flowData)
  
  if (!validation.valid) {
    return createMockResponse(
      { valid: false, errors: validation.errors },
      '验证失败'
    )
  }
  
  return createMockResponse({ valid: true, errors: [] }, '验证通过')
}
```

## 🔧 智能同步机制

### 冲突检测
```typescript
async syncWithBackend(): Promise<void> {
  if (this.currentFlow.metadata?.backendId) {
    const response = await loadFlowApi(this.currentFlow.metadata.backendId)
    const backendFlow = response.data
    
    // 比较更新时间
    const localTime = new Date(this.currentFlow.updatedAt).getTime()
    const backendTime = new Date(backendFlow.metadata.updatedAt).getTime()
    
    if (backendTime > localTime) {
      // 后端更新 → 拉取最新数据
      const frontendFlow = transformFromBackendFormat(backendFlow)
      this.currentFlow = frontendFlow
    } else if (localTime > backendTime) {
      // 本地更新 → 推送到后端
      await this.saveFlowToBackend()
    }
  }
}
```

## 🚀 真实后端对接指南

### 需要修改的地方

1. **API层** (`src/api/orchestration.ts`)：
```typescript
// 当前模拟
export async function saveFlowApi(flowData: BackendFlowData) {
  await mockDelay()
  // localStorage操作...
}

// 真实对接
export function saveFlowApi(flowData: BackendFlowData) {
  return post<ApiResponse>({
    url: '/api/orchestration/flows',
    data: flowData
  })
}
```

2. **错误处理**：
```typescript
// 添加网络错误处理
try {
  const response = await saveFlowApi(backendData)
} catch (error) {
  if (error.code === 'NETWORK_ERROR') {
    // 网络错误处理
  } else if (error.code === 'VALIDATION_ERROR') {
    // 数据验证错误
  }
}
```

3. **认证授权**：
```typescript
// 添加用户认证
headers: {
  'Authorization': `Bearer ${getToken()}`,
  'Content-Type': 'application/json'
}
```

## 📊 数据压缩效果

通过移除UI相关属性，数据大小减少约60-70%：

